- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - orgName: 东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: 13344445555
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - personOid1: 4468fe9323bc4bfeaf5f659af14b6754
      - personOid2: 824708b5195546eab28565ebb0203a1e
      - personOid3: 310913bf68594f8bb7c457215e0b4831
      - orgOid1: 4d4352cd0de849ab9daa507397086bf1
      - fileId1: 37492a3be0c64282b834a48f77db3c89
      - m1: "19922222222"
      - m2: "19800000000"
      - e1: <EMAIL>
      - e2: <EMAIL>
      - pOid1: ${getOid($m1)}
      - pOid2: ${getOid($m2)}
      - pOid3: ${getOid($e1)}
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:
      - m3: "***********"
      - pOid_m: ${getOid($m3)}
      - tPUserId1: autotest1${get_randomNo()}
      - appid_xuanyuan: ${ENV(appId_xuanyuan)}
      - m4: "***********"
      - pOid_m1: ${getOid($m4)}

- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户3，只输入必填项（用户名、证件号），转签用"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid3: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建组织：个人账户1创建组织"
    variables:
      - accountId: $personOid2
      #        - organCode: ${get_orgCode()}
      - idNo: ${get_idNo()}
      - orgType: CRED_ORG_CODE
      - orgCode: ${get_orgCode()}
      - legalIdNo:
      - legalName:
      - name: $orgName
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]
#    output:
#        -  $orgId

- test:
    name: "创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建个人模版印章"
    variables:
      - accountId: $personOid3
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建个人模版印章"
    variables:
      - accountId: $personOid3
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建企业模板印章"
    variables:
      - orgId: $orgOid1
      - color: RED
      - height: 150
      - width: 150
      - alias: 企业模板印章
      - type: TEMPLATE_ROUND
      - central: STAR
      - hText: 上弦文
      - qText: 下弦文
    api: api/seals/seals/create_c_template_seal_appid.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/file_template/files/get_uploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 创建模板，获取templateTypeId
    variables:
      - accountId: $personOid1
      - fileMimeType: "application/pdf"
      - templateName: "测试模板"
    api: api/file_template/templates/create_templates.yml
    extract:
      - templateId: content.data.templateId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

#$personOid1
- test:
    name: 根据文件模板创建文件
    variables:
      - accountId: $personOid1
      - chunkedFields:
      - {"mimeType": "text/html","chunkedData": "PGh0bWw+Cgo8aGVhZD4KPHRpdGxlPui2heeugOWNlUhUTUwg6aG16Z2iPC90aXRsZT4KPC9oZWFkPgoKPGJvZHk+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjwvYm9keT4KCjwvaHRtbD4K", "sequence": 0, "pagePos": 5}
      - simpleFormFields: {"出借方": "1","借款方": "2018-12-21"}
      - fileName: 个人借贷合同-模板文件
      - name: 测试
    api: api/file_template/files/createbytemplate.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

