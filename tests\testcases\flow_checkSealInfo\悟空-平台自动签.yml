- config:
    name: 指定印章平台绑定证书的印章id自动签署-43M文件
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: "7876625192"
      - app_id: "7876625192"
      - app_secret: "28d8bcb9dd1f5e1545e372195l9693208"
      - personOid1: "47e46a51057d45dabf13b62a00f920b3"
      - sleep: ${hook_sleep_n_secs(3)}



#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 指定绑定证书的印章Id
    variables:

      - json: {  "docs": [
    {
      "fileId": "b55dbe7464a74ba5bfd5bcbf038d30d2",
      "fileName": "2页.pdf"
    }
  ],
  "flowInfo": {
    "flowConfigItemBean": {
      "buttonConfig": {
        "propButton": "1,2,6,11"
      }
    },
    "autoArchive": true,
    "autoInitiate": true,
    "businessScene": "签签签签签署署署署署1648006561971",
    "contractRemind": 65,
    "contractValidity": "*************",

    "flowConfigInfo": {
      "noticeDeveloperUrl": "http://101.35.194.58/mock/f1222ad728710973bae59a7dd1b25dc943ccab69a9b43c228f11343a459f2f37/callback",
      "noticeType": "",
      "redirectUrl": "",
      "signPlatform": "",
      "willTypes": [],
      "countdown": 0,
      "redirectDelayTime": 3
    },
    "signValidity": "*************"
  },
  "signers": [
      {
      "platformSign": true,
      "signOrder": 1,
      "signerAccount": {
        "signerAccountId": "8fbe5926547e40149978fb8c5a448394",
        "authorizedAccountId": "8fbe5926547e40149978fb8c5a448394",
        "noticeType": ""
      },
      "signfields": [
        {
          "fieldType": 0,
          "assignedPosbean": false,
          "autoExecute": true,
          "actorIndentityType": 2,
          "sealId": "",
          "fileId": "b55dbe7464a74ba5bfd5bcbf038d30d2",
          "posBean": {
            "posPage": "1",
            "posX": 400,
            "posY": 400
          },
          "signTimeFormat": "YYYY/MM/dd",
          "signDateBeanType": 1,
          "sealType": "1",
          "signType": 1,
          "width": 150
        }
      ],
      "thirdOrderNo": "11111"
    }
  ]
}

    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.flowId


-   test:
        name: "获取签署链接"
        variables:
            -   app_secret: $app_secret
            -   flowId: $flowId1
            -   accountId: "8fbe5926547e40149978fb8c5a448394"
            -   urlType: 0
            -   multiSubject: true
            -   organizeId: "8fbe5926547e40149978fb8c5a448394"
            -   compressContext: true
        api: api/iterate_cases/executeUrl.yml
        extract:
            -   shortUrl: content.data.shortUrl
            -   code: content.code
            -   message: content.message
        validate:
            -   eq: ["content.code",0]

-   test:
        name:  获取config
        variables:
            -   app_secret: $app_secret
            -   flowId: "e9a417bda4d3426a9d02d753b05678f8"
            -   queryAccountId: "8fbe5926547e40149978fb8c5a448394"
            -   querySpaceAccountId: "8fbe5926547e40149978fb8c5a448394"
            -   resourceShareId:
        api: api/share/config.yml
        validate:
            -   eq: ["content.code",0]
            -   eq: ["content.message",成功]
            -   eq: ["content.data.pageConfig.configList", [{'name': '查看', 'code': 'see', 'type': 'text'}, {'name': '下载', 'code': 'download', 'type': 'text'}, {'name': '上传附件', 'code': 'attachment', 'type': 'text'}, {'name':
'返回列表', 'code': 'list', 'type': 'text'}, {'name': '下载APP', 'code': 'downloadApp', 'type': 'text'}, {'name': '签署完成文案提示', 'code': 'signDoneDesc', 'type': 'text'}]
]