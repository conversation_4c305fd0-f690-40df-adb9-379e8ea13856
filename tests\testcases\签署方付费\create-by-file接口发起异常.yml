- config:
    name: 通过文件创建签署流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id1: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - app_id2: ${ENV(saas_noapproval)}
      - file_id2: ${ENV(file_id_in_saas_noapproval)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - account_id_2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_3_tsign: ${ENV(account_id3_in_tsign)}
      - account_id_4_tsign: ${ENV(standardoid)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}

#流程发起失败
- test:
    name: "流程发起失败 - 计费隔离模型非DISTRIBUTION"
    api: api/sign-flow/create-by-file.yml
    variables:
      - app_id: "$app_id1"
      - json: {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
            {
              "fileEditPwd": "",
              "fileId": "$file_id",
              "fileName": "测试文档1.pdf",
              "neededPwd": false
            }
          ],

          "signFlowConfig": {
            "signFlowTitle": "流程发起失败 - 计费隔离模型非DISTRIBUTION",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": [],
              "willingnessAuthModes": ["CODE_SMS","CODE_EMAIL","PSN_FACE_ALIPAY","PSN_FACE_TECENT","PSN_FACE_ESIGN","PSN_FACE_WECHAT"]
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
              "orderType":""

            },
            "noticeConfig": {
              "noticeTypes": "1,2"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": 2,
              "redirectUrl": "https://www.baidu.com"
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [
          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "psnSignerInfo": {
              "psnAccount": "",
              "psnId": "$account_id_4_tsign"

            },
            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": "$file_id",
              "normalSignFieldConfig": {
                "assignedSealId": "",
                "autoSign": false,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 180,
                  "positionY": 180
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 0
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - eq: [content.message, '参数错误: 签署方付费模式仅支持使用渠道套餐，请调整后重试']



- test:
    name: "流程发起失败 - appid售卖方案为悟空"
    api: api/sign-flow/create-by-file.yml
    variables:
      - app_id: "$app_id1"
      - json: {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": "$file_id",
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "流程发起失败 - appid售卖方案为悟空",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": [],
              "willingnessAuthModes": ["CODE_SMS","CODE_EMAIL","PSN_FACE_ALIPAY","PSN_FACE_TECENT","PSN_FACE_ESIGN","PSN_FACE_WECHAT"]
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
              "orderType":"DISTRIBUTION"

            },
            "noticeConfig": {
              "noticeTypes": "1,2"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": 2,
              "redirectUrl": "https://www.baidu.com"
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [
          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "psnSignerInfo": {
              "psnAccount": "",
              "psnId": "$account_id_4_tsign"

            },
            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": "$file_id",
              "normalSignFieldConfig": {
                "assignedSealId": "",
                "autoSign": false,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 180,
                  "positionY": 180
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 0
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - eq: [content.message, '参数错误: 发起方/签署方付费不允许使用分项套餐，请联系交付经理确认']

- test:
    name: "流程发起失败-chargeOrgId传入非签署方oid"
    api: api/sign-flow/create-by-file.yml
    variables:
      - app_id: "$app_id2"
      - json: {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
            {
              "fileEditPwd": "",
              "fileId": "$file_id2",
              "fileName": "测试文档1.pdf",
              "neededPwd": false
            }
          ],
          "signFlowConfig": {
            "signFlowTitle": "流程发起失败-chargeOrgId传入非签署方oid",
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
               "orderType" : "DISTRIBUTION",
               "chargeOrgId": ["6e7deed2498249c1a0d44f850ddb6985"]
            }
          },
          "signers": [
              {
              "orgSignerInfo": {
                  "orgId": "fe740a09609e434a91fe310ff8a6cc7f",
                  "transactorInfo": {
                      "psnId":"$account_id_1_tsign"
                  }
              },
              "signerType": 1,
              "signFields": [
                  {
                    "signFieldType": "0",
                      "fileId": "$file_id2",
                      "normalSignFieldConfig": {
                          "signFieldStyle": 1,
                          "autoSign-": true,
                          "freeMode": false,
                          "signFieldPosition": {
                              "positionPage":"1",
                              "positionX":"150",
                              "positionY":"250"
                          }
                      }
                  }
              ]
            }
          ]
        }
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435011]
      - eq: [content.message, 'chargeOrgId与signers中信息不匹配，请调整后重试']

- test:
    name: "流程发起失败-chargeOrgName传入非签署方orgName"
    api: api/sign-flow/create-by-file.yml
    variables:
      - app_id: "$app_id2"
      - json: {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
            {
              "fileEditPwd": "",
              "fileId": "$file_id2",
              "fileName": "测试文档1.pdf",
              "neededPwd": false
            }
          ],
          "signFlowConfig": {
            "signFlowTitle": "流程发起失败-chargeOrgId传入非签署方oid",
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
               "orderType" : "DISTRIBUTION",
               "chargeOrgName": ["esigntest桃浪企业A"]
            }
          },
          "signers": [
              {
              "orgSignerInfo": {
                  "orgName": "非常异端咖啡馆",
                  "transactorInfo": {
                      "psnAccount": "***********",
                      "psnName": "测试猫猫一"
                  }
              },
              "signerType": 1,
              "signFields": [
                  {
                    "signFieldType": "0",
                      "fileId": "$file_id2",
                      "normalSignFieldConfig": {
                          "signFieldStyle": 1,
                          "autoSign-": true,
                          "freeMode": false,
                          "signFieldPosition": {
                              "positionPage":"1",
                              "positionX":"150",
                              "positionY":"250"
                          }
                      }
                  }
              ]
            }
          ]
        }
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435011]
      - eq: [content.message, 'chargeOrgName与signers中信息不匹配，请调整后重试']

- test:
    name: "流程发起失败-chargeOrgNane指定企业未授权use_org_order"
    api: api/sign-flow/create-by-file.yml
    variables:
      - app_id: "$app_id2"
      - json: {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
            {
              "fileEditPwd": "",
              "fileId": "$file_id2",
              "fileName": "测试文档1.pdf",
              "neededPwd": false
            }
          ],
          "signFlowConfig": {
            "signFlowTitle": "流程发起失败-chargeOrgNane指定企业未授权use_org_order",
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
               "orderType" : "DISTRIBUTION",
               "chargeOrgName": ["蛋花喵喵屋"]
            }
          },
          "signers": [
              {
              "orgSignerInfo": {
                  "orgName": "蛋花喵喵屋",
                  "transactorInfo": {
                      "psnAccount": "***********",
                      "psnName": "测试猫猫一"
                  }
              },
              "signerType": 1,
              "signFields": [
                  {
                    "signFieldType": "0",
                      "fileId": "$file_id2",
                      "normalSignFieldConfig": {
                          "signFieldStyle": 1,
                          "autoSign-": true,
                          "freeMode": false,
                          "signFieldPosition": {
                              "positionPage":"1",
                              "positionX":"150",
                              "positionY":"250"
                          }
                      }
                  }
              ]
            }
          ]
        }
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1436116]
      - contains: [content.message, '未授权:use_org_order']

