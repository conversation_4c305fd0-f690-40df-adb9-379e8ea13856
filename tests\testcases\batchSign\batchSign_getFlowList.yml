- config:
    name: "/v3/sign-flow/batch-sign-list接口集测case"
    base_url: ${ENV(base_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(app_id_in_wukong_ckl)}
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - fileId2: a152d745f521490aaedcadafed493b98
      - mobile_fengjiu: ***********
      - mobile_caogu: ***********
      - sealId1: 8fa71acc-425d-4270-9fa7-b0e91f57fbef
      - signFlowStartTimeTo: *************
      - standardoid: ${ENV(standardoid)}

- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: ckl201911141994
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId




#V3接口创建签署流程，不取appid配置数据，流程强制签署人、签署主体实名意愿
- test:
    name: 创建钟灵的V3签署流程1
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      #        - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      - flowInfo: { autoArchive: false, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},
                   signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/xy_wk_integration/V3_createFlowOneStep.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 创建钟灵的V3签署流程2
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      #        - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: "esigntest东营伟信建筑安装工程有限公司"},
                   signfields: [{ certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/xy_wk_integration/V3_createFlowOneStep.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 创建钟灵的V2签署流程3
    variables:
      - doc: {encryption: 0,fileId: $fileId2, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $accountId_ckl, autoArchive: true, autoInitiate: false, businessScene: "流程无抄送人",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId2,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 创建凤九的V3签署流程4
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},
                   signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/xy_wk_integration/V3_createFlowOneStep.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



- test:
    name: 查询V3流程的签署中+签署完成流程，V2的流程查不到
    setup_hooks:
      - ${sleep_N_secs(3)}
    variables:
      - json: {
        "pageNum": 1,
        "pageSize": 100,
        "signFlowStartTimeFrom": *************,
        "signFlowStartTimeTo": $signFlowStartTimeTo,
        "signFlowStatus": [],
        "signerInfos": [
        {
          "operatorId": "",
          "operatorName": "",
          "signerId": "",
          "signerName": ""
        }]
      }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - signFlowInfos: content.data.signFlowInfos
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId1)}",True]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId2)}",True]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId3)}",False]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId4)}",True]

- test:
    name: 查询V3流程的签署中+签署完成流程，指定签署人钟灵，凤九的流程查不到
    variables:
      - json: {
        "pageNum": 1,
        "pageSize": 100,
        "signFlowStartTimeFrom": *************,
        "signFlowStartTimeTo": $signFlowStartTimeTo,
        "signFlowStatus": [1,2],
        "signerInfos": [
        {
          "operatorId": $standardoid,
          "operatorName": "",
          "signerId": "",
          "signerName": ""
        }]
      }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - signFlowInfos: content.data.signFlowInfos
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
        #        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId1)}",True]
        #        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId2)}",True]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId3)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId4)}",False]


- test:
    name: 查询V3流程的签署中+签署完成流程，指定签署人钟灵，指定签署主体钟灵，企业主体的流程查不到
    variables:
      - json: {
        "pageNum": 1,
        "pageSize": 100,
        "signFlowStartTimeFrom": *************,
        "signFlowStartTimeTo": $signFlowStartTimeTo,
        "signFlowStatus": [],
        "signerInfos": [
        {
          "operatorId": $standardoid,
          "operatorName": "",
          "signerId": $standardoid,
          "signerName": ""
        }]
      }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - signFlowInfos: content.data.signFlowInfos
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
        # - str_eq: ["${isContainFlowId($signFlowInfos,$flowId1)}",True]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId2)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId3)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId4)}",False]

- test:
    name: 查询V3流程的签署完成流程，指定签署人钟灵，指定签署主体钟灵，签署中流程查不到
    variables:
      - json: {
        "pageNum": 1,
        "pageSize": 100,
        "signFlowStartTimeFrom": *************,
        "signFlowStartTimeTo": $signFlowStartTimeTo,
        "signFlowStatus": [2],
        "signerInfos": [
        {
          "operatorId": $standardoid,
          "operatorName": "",
          "signerId": $standardoid,
          "signerName": ""
        }]
      }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - signFlowInfos: content.data.signFlowInfos
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId1)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId2)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId3)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId4)}",False]


#流程1签署
- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId1
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1_signerAccount: content.data.signfields.0.signerAccountId
      - flowId1_signfieldId: content.data.signfields.0.signfieldId


- test:
    name: 查询签署人印章
    variables:
      - accountId: $flowId1_signerAccount
    api: api/seals/seals/select_p_seals.yml
    extract:
      - flowId1_signerAccount_seal: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

#意愿认证
- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $flowId1_signerAccount
      - flowId: $flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $flowId1_signerAccount
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署 - 用户做完意愿认证，签署成功
    variables:
      - accountId: $flowId1_signerAccount
      - flowId: $flowId1
      - posBean: {addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $flowId1_signerAccount, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $flowId1_signerAccount_seal, signfieldId: $flowId1_signfieldId, signerOperatorAuthorizerId: $flowId1_signerAccount, signerOperatorId: $flowId1_signerAccount}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfieldSignResult.0.optResult", 0]

- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: 查询V3流程的签署完成流程，签署中流程查不到
    variables:
      - json: {
        "pageNum": 1,
        "pageSize": 100,
        "signFlowStartTimeFrom": *************,
        "signFlowStartTimeTo": $signFlowStartTimeTo,
        "signFlowStatus": [2],
        "signerInfos": [
        {
          "operatorId": "",
          "operatorName":"",
          "signerId": "",
          "signerName": ""
        }]
      }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - signFlowInfos: content.data.signFlowInfos
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
        #   - str_eq: ["${isContainFlowId($signFlowInfos,$flowId1)}",True]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId2)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId3)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId4)}",False]


- test:
    name: 查询V3流程的签署中流程，指定签署人钟灵，指定签署主体钟灵，签署完成流程查不到
    variables:
      - json: {
        "pageNum": 1,
        "pageSize": 100,
        "signFlowStartTimeFrom": *************,
        "signFlowStartTimeTo": $signFlowStartTimeTo,
        "signFlowStatus": [1],
        "signerInfos": [
        {
          "operatorId": $standardoid,
          "operatorName":"",
          "signerId": "",
          "signerName": ""
        }]
      }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - signFlowInfos: content.data.signFlowInfos
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId1)}",False]
        #      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId2)}",True]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId3)}",False]
      - str_eq: ["${isContainFlowId($signFlowInfos,$flowId4)}",False]


#分布接口发起的V3流程
- test:
    name: 分布发起钟灵V3签署流程5
    variables:
      - autoArchive: true
      - businessScene: 分布发起钟灵V3签署流程5
      - configInfo: {noticeType: "",noticeDeveloperUrl: "","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId5: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId5
      - docs: [{fileId: $fileId1, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-签署人传个人oid，签署主体传同一个oid
    variables:
      - flowId: $flowId5
      - json: {signfields: [{fileId: "$fileId1","actorIndentityType": 0,signerAccountId: $standardoid,"authorizedAccountId": $standardoid,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId5
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 查询V3流程的签署中+签署完成流程，分布发起的流程可以查询到
    setup_hooks:
      - ${sleep_N_secs(3)}
    variables:
      - json: {
        "pageNum": 1,
        "pageSize": 100,
        "signFlowStartTimeFrom": *************,
        "signFlowStartTimeTo": $signFlowStartTimeTo,
        "signFlowStatus": [],
        "signerInfos": [
        {
          "operatorId": "",
          "operatorName": "",
          "signerId": "",
          "signerName": ""
        }]
      }
    api: api/signflow/batchSign0210/batchSignList.yml
    extract:
      - signFlowInfos: content.data.signFlowInfos
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId5)}",True]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId1)}",True]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId2)}",True]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId3)}",False]
#        - str_eq: ["${isContainFlowId($signFlowInfos,$flowId4)}",True]

