import json

aa = {'totalSize': 18, 'traceInfos': [{'cost': 87, 'hitTime': 2, 'loopInvokes': [{'cost': 87, 'endpoint': '/api/storage/replay/query/cacheLoad', 'loopCost': 0, 'serviceId': 'arex-storage', 'times': 53, 'type': 'Cache - Jedis/setex'}], 'occurredTime': 1741337071817, 'success': False, 'totalCost': 187, 'traceId': '10100027023.263.17413370718177069'}, {'cost': 803, 'hitTime': 4, 'loopInvokes': [{'cost': 803, 'endpoint': '/queryOrderListByEs/query', 'loopCost': 100, 'serviceId': 'basicbs-billing-order', 'times': 30, 'type': 'Http - gtc-service/v1/getInvoiceOrderByCondition/req'}], 'occurredTime': 1741336546134, 'success': False, 'totalCost': 2338, 'traceId': '***********.224.17413365461347201'}, {'cost': 16188, 'hitTime': 2, 'loopInvokes': [{'cost': 16188, 'endpoint': '/queryByShowcaseIdNew/request', 'loopCost': 81, 'serviceId': 'base-product-web', 'times': 41, 'type': 'Database - base-product-web-Mysql/JDBI/PreparedStatement/execute-select child_id from base'}], 'occurredTime': *************, 'success': False, 'totalCost': 32520, 'traceId': '***********.2852185.*************1305'}, {'cost': 453, 'hitTime': 24, 'loopInvokes': [{'cost': 453, 'endpoint': '/getPagedICUserByGuid/input', 'loopCost': 453, 'serviceId': 'account-base-service', 'times': 99, 'type': 'exceeded99'}], 'occurredTime': *************, 'success': False, 'totalCost': 9482, 'traceId': '***********.227.*************6797'}, {'cost': 753, 'hitTime': 22, 'loopInvokes': [{'cost': 753, 'endpoint': '/complete/request', 'loopCost': 753, 'serviceId': 'themis', 'times': 99, 'type': 'exceeded99'}], 'occurredTime': *************, 'success': False, 'totalCost': 17679, 'traceId': '***********.2938011.*****************'}, {'cost': 1802, 'hitTime': 17, 'loopInvokes': [{'cost': 1802, 'endpoint': '/launcherPager/input', 'loopCost': 114, 'serviceId': 'tower', 'times': 54, 'type': 'Database - tower-Mysql/JDBI/PreparedStatement/execute-select * from instance_me'}], 'occurredTime': *************, 'success': False, 'totalCost': 31465, 'traceId': '***********.859153.*****************'}, {'cost': 1302, 'hitTime': 1, 'loopInvokes': [{'cost': 1302, 'endpoint': '{POST}/v1/grayscale/versions/admin/organs', 'loopCost': 188, 'serviceId': 'gray-config-manage', 'times': 96, 'type': 'MQ - RocketMQ/org_gray_upgrade/Producer'}], 'occurredTime': 1741337479726, 'success': False, 'totalCost': 1302, 'traceId': '***********.291.17413374797266187'}, {'cost': 165, 'hitTime': 1, 'loopInvokes': [{'cost': 165, 'endpoint': '/queryTagsProductList/commodityId', 'loopCost': 74, 'serviceId': 'base-product-web', 'times': 36, 'type': 'Database - base-product-web-Mysql/JDBI/PreparedStatement/execute-select tags_id\n        fr'}], 'occurredTime': 1741336658063, 'success': False, 'totalCost': 165, 'traceId': '***********.1678704.17413366580630531'}, {'cost': 13250, 'hitTime': 1, 'loopInvokes': [{'cost': 13250, 'endpoint': '{POST}/api/createBatchPlan', 'loopCost': 857, 'serviceId': 'arex-replay-schedule', 'times': 58, 'type': 'Http - arex-storage/api/storage/replay/query/countByRange'}], 'occurredTime': 1741336897706, 'success': False, 'totalCost': 13250, 'traceId': '10100027023.115.17413368977068833'}, {'cost': 687, 'hitTime': 4, 'loopInvokes': [{'cost': 687, 'endpoint': '{GET}/v2/identity/config/{flowId}/all', 'loopCost': 0, 'serviceId': 'footstone-identity', 'times': 28, 'type': 'Cache - Jedis/get'}], 'occurredTime': 1741336800802, 'success': False, 'totalCost': 1978, 'traceId': '***********.227.17413368*********'}, {'cost': 696, 'hitTime': 2, 'loopInvokes': [{'cost': 696, 'endpoint': '/start/start', 'loopCost': 696, 'serviceId': 'epeius', 'times': 99, 'type': 'exceeded99'}], 'occurredTime': *************, 'success': False, 'totalCost': 1351, 'traceId': '***********.167.*************2129'}, {'cost': 146846, 'hitTime': 4, 'loopInvokes': [{'cost': 146846, 'endpoint': '{POST}/v1/rpc/manage/cockpit/statistics/all', 'loopCost': 2150, 'serviceId': 'esign-gov-seal-service', 'times': 72, 'type': 'Http - esign-gov-account-service{POST}/v1/rpc/config/component/value'}], 'occurredTime': *************, 'success': False, 'totalCost': 564115, 'traceId': '***********.215.*****************'}, {'cost': 46277, 'hitTime': 12, 'loopInvokes': [{'cost': 46277, 'endpoint': '{POST}/v1/invoices', 'loopCost': 65, 'serviceId': 'gtc-service', 'times': 31, 'type': 'Database - gtc-service-Mysql/JDBI/PreparedStatement/execute-select * from invoice_ext'}], 'occurredTime': *************, 'success': False, 'totalCost': 541953, 'traceId': '***********.164.*************0481'}, {'cost': 39034, 'hitTime': 2, 'loopInvokes': [{'cost': 39034, 'endpoint': '{GET}/api/v1/queryPricePreferential', 'loopCost': 3052, 'serviceId': 'crm-contract-system', 'times': 95, 'type': 'Http - crm-contract-system/base-product-web/querySaleInfo/request'}], 'occurredTime': *************, 'success': False, 'totalCost': 70688, 'traceId': '***********.2852185.*************1305'}, {'cost': 18805, 'hitTime': 4, 'loopInvokes': [{'cost': 18805, 'endpoint': '{GET}/api/v1/getNoProjectContractList', 'loopCost': 3588, 'serviceId': 'crm-contract-system', 'times': 94, 'type': 'Http - crm-contract-system/base-product-web/queryProductDetail/productId'}], 'occurredTime': 1741336404593, 'success': False, 'totalCost': 53701, 'traceId': '***********.844697.*****************'}, {'cost': 377, 'hitTime': 3, 'loopInvokes': [{'cost': 377, 'endpoint': '{GET}/v2/todo/subjects/aggregate', 'loopCost': 0, 'serviceId': 'contract-manager', 'times': 35, 'type': 'Cache - Jedis/get'}], 'occurredTime': *************, 'success': False, 'totalCost': 2615, 'traceId': '***********.291.*************5555'}, {'cost': 144, 'hitTime': 9, 'loopInvokes': [{'cost': 144, 'endpoint': '/getGrantedMemberByRole/input', 'loopCost': 101, 'serviceId': 'account-organization-service', 'times': 49, 'type': 'Database - account-organization-service-Mysql/JDBI/PreparedStatement/execute-SELECT id AS id,organ_id '}], 'occurredTime': *************, 'success': False, 'totalCost': 1223, 'traceId': '***********.222.*************7159'}, {'cost': 419, 'hitTime': 1001, 'loopInvokes': [{'cost': 419, 'endpoint': '/queryCommodityDetail/commodityId', 'loopCost': 56, 'serviceId': 'base-product-web', 'times': 26, 'type': 'Database - base-product-web-Mysql/JDBI/PreparedStatement/execute-select\n         \n        '}], 'occurredTime': *************, 'success': False, 'totalCost': 352770, 'traceId': '***********.496701.*****************'}]}
print(json.dumps(aa))