#def: add_members_into_organizations($orgId,$organCode,$organType,$applierAccountId)            #加入成员
request:
  url: ${ENV(footstone_user_url)}/v1/organizations/join
  method: PUT
  headers: ${get_headers($app_id)}
  json:
    {
      "accountId": $orgId, #组织oid
      "applierAccountId": $applierAccountId, #申请人oid
      "isAcceptApplyJoin": false, #是否是管理员同意用户申请
      "organCode": $organCode,
      "organType": $organType
    }

