- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(base_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: 13344445555
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - app_id1: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:

- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/file_template/files/get_uploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 创建模板，获取templateTypeId
    variables:
      - accountId: $personOid1
      - fileMimeType: "application/pdf"
      - templateName: "测试模板"
    api: api/file_template/templates/create_templates.yml
    extract:
      - templateId: content.data.templateId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

#$personOid1
- test:
    name: 根据文件模板创建文件
    variables:
      - accountId: $personOid1
      - chunkedFields:
      - {"mimeType": "text/html","chunkedData": "PGh0bWw+Cgo8aGVhZD4KPHRpdGxlPui2heeugOWNlUhUTUwg6aG16Z2iPC90aXRsZT4KPC9oZWFkPgoKPGJvZHk+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjxwPuS4reaWhzV3aXRoIEVuZ2xpc2g8L3A+CjwvYm9keT4KCjwvaHRtbD4K", "sequence": 0, "pagePos": 5}
      - simpleFormFields: {"出借方": "1","借款方": "2018-12-21"}
      - fileName: 个人借贷合同-模板文件
      - name: 测试
    api: api/file_template/files/createbytemplate.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 单人单文档场景-个人空间创建流程:只填必填项
    variables:
      - autoArchive: True
      - businessScene: 单人单文档
      - initiatorAccountId:
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 单人单文档场景-添加流程文档：单文档
    variables:
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 单人单文档场景-添加流程签署区：签署人账号-个人账户1、手动签署、签约主体-个人、不指定签署区
    variables:
      - flowId: $flowId1
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId1,$posBean,0,$personOid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 单人单文档场景-添加或更新签署区并执行签署，增加签署区数量控制。测试环境120个签署区就会报错
    variables:
      - flowId: $flowId1
      - accountId: $personOid1
      - posBean: {addSignTime: False, key: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: ${gen_signfield_data_V4(0,0,$fileId1,$flowId1,$posBean,1,$personOid1,$personOid1,$sealId,1,121)}
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $personOid1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


      #- test:
      #    name: 单人单文档场景-查询流程：已归档
      #    setup_hooks:
      #      - ${sleep_N_secs(3)}
      #    variables:
      #      - flowId: $flowId1
      #    api: api/signflow/signflows/signflowsSelect.yml
      #    validate:
#      - eq: ["content.data.flowStatus",2]

- test:
    name: 单人单文档场景-个人单文档签署-获取签署地址
    variables:
      - urlType: 0
      - flowId: $flowId1
      - accountId: $personOid1
      - organizeId:
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
