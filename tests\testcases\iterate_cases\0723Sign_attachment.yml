- config:
    name: "0723迭代关于附件接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/劳动合同书12.pdf
      - group: ''
      - app_Id1: ${ENV(wukong_no_realname)}
      - app_Id2: ${ENV(xuanyuan_realname)}

- test:
    name: "wukong_no_realname下创建签署人王瑶济账号"
    variables:
      - app_id: $app_Id1
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "文件直传创建一个文件,不上传oss"
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "文件直传创建一个文件,当前项目下的appId"
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf2)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf2)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf2)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf2)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: "文件直传创建一个文件,别的项目appId"
    variables:
      - app_id: $app_Id2
      - contentMd5: ${get_file_base64_md5($path_pdf3)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf3)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId3: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - app_id: $app_Id2
      - contentMd5: ${get_file_base64_md5($path_pdf3)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf3)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: "创建发起人梁贤红账号"
    variables:
      - app_id: $app_Id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId



##################-个个人签署区###################################
- test:
    name: "创建签署流程-一个签署区"
    variables:
      - app_id: $app_Id1
      - autoArchive: true
      - businessScene: 悟空非实名签
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1个未上传的文件"
    variables:
      - app_id: $app_Id1
      - addWhenCreateFlow: true
      - accountId:
      - attachmentName: 附件1
      - fileId: $fileId1
      - operatorAccountId:
      - status: 1
      - flowId: $sign_flowId1
    api: api/iterate_cases/add_attachments.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437516]
      - eq: ["content.message","文档未上传完成或上传失败, 请重新上传"]

- test:
    name: "添加1个已上传的文件-当前项目下"
    variables:
      - app_id: $app_Id1
      - addWhenCreateFlow: true
      - accountId:
      - attachmentName: 附件2
      - fileId: $fileId2
      - operatorAccountId:
      - status: 1
      - flowId: $sign_flowId1
    api: api/iterate_cases/add_attachments.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "添加1个已上传的文件-不是当前项目下"
    variables:
      - app_id: $app_Id1
      - addWhenCreateFlow: true
      - accountId:
      - attachmentName: 附件2
      - fileId: $fileId3
      - operatorAccountId:
      - status: 1
      - flowId: $sign_flowId1
      - message: ${ENV(message4)}
      - code: ${strToInt()}
    api: api/iterate_cases/add_attachments.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",$code]
      - contains: ["content.message",$message]

- test:
    name: "添加不存在的fileId"
    variables:
      - app_id: $app_Id1
      - addWhenCreateFlow: true
      - accountId:
      - attachmentName: 附件2
      - fileId: 1122222222222222224344
      - operatorAccountId:
      - status: 1
      - flowId: $sign_flowId1
    api: api/iterate_cases/add_attachments.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437511]
      - contains: ["content.message","文档不存在"]

- test:
    name: "添加不存在的fileId为空"
    variables:
      - app_id: $app_Id1
      - addWhenCreateFlow: true
      - accountId:
      - attachmentName: 附件2
      - fileId:
      - operatorAccountId:
      - status: 1
      - flowId: $sign_flowId1
    api: api/iterate_cases/add_attachments.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: fileId不能为空"]

- test:
    name: "添加签署文件"
    variables:
      - app_id: $app_Id1
      - docs: [{'fileHash': '', 'fileId': $fileId2, 'fileName': '文件2', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个个人签署区"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId2,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
- test:
    name: "发起人撤回"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - operatorId: $caogu_accountId
      - revokeReason: 撤回
    api: api/iterate_cases/revoke.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",'成功']

- test:
    name: "发起人撤回"
    variables:
      - app_id: $app_Id1
      - flowId: $sign_flowId1
      - operatorId: $caogu_accountId
      - revokeReason: 撤回
    api: api/iterate_cases/revoke.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437168]
      - eq: ["content.message",'流程已撤销']

- test:
    name: "一步发起签署-附件没有上传"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"docs":[{"fileId":$fileId2,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId2,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437516]
      - eq: ["content.message","文档未上传完成或上传失败, 请重新上传"]

- test:
    name: "一步发起签署-附件上传了"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId2}],"docs":[{"fileId":$fileId2,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId2,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

      #- test:
      #    name: "一步发起签署-附件别的项目下的"
      #    variables:
      #        - app_id: $app_Id1
      #        #        - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId3}],"docs":[{"fileId":$fileId2,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId2,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
      #    api: api/iterate_cases/createFlowOneStep.yml
      #    validate:
#      - eq: ["content.code",1435002]
#      - gt: ["content.message",'参数错误: 当前项目下，附件不存在，fileId:']

- test:
    name: "一步发起签署-附件不存在"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":*****************}],"docs":[{"fileId":$fileId2,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId2,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: ["content.code",1435608]
      #      - contains: ["content.message","合同不存在"]
      - eq: [ "content.code",1437511 ]
      - contains: [ "content.message","文档不存在" ]

- test:
    name: "一步发起签署-附件为空"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":}],"docs":[{"fileId":$fileId2,"fileName":"待签文档.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"代签文档","contractRemind":10,"contractValidity":"","flowConfigInfo":{"archiveLock":false,"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2","redirectUrl":"","signPlatform":"1,2,3,4"},"remark":"流程备注","signValidity":""},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":$yuzan_accountId,"authorizedAccountId":$yuzan_accountId},"signfields":[{"autoExecute":false,"fileId":$fileId2,"sealType":"","signDateBean":{"fontSize":18,"format":"","posPage":1,"posX":300,"posY":100},"posBean":{"posPage":"1","posX":100,"posY":200},"signType":1,"signTimeFormat":""}],"thirdOrderNo":"********",}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: ["content.code",0]
      #      - contains: ["content.message","成功"]
      - eq: [ "content.code",1437511 ]
      - contains: [ "content.message","文档不存在" ]

- test:
    name: "一步到位创建流程-对内，附件没有上传"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"docs":[{"fileId":$fileId2}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$yuzan_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId2,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437516]
      - eq: ["content.message","文档未上传完成或上传失败, 请重新上传"]

- test:
    name: "一步到位创建流程-对内，附件没有上传"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId1}],"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"docs":[{"fileId":$fileId2}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$yuzan_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId2,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437516]
      - eq: ["content.message","文档未上传完成或上传失败, 请重新上传"]

- test:
    name: "一步到位创建流程-对内，附件已上传"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId2}],"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"docs":[{"fileId":$fileId2}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$yuzan_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId2,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

      #- test:
      #    name: "一步到位创建流程-对内，附件是别的项目下"
      #    variables:
      #        - app_id: $app_Id1
      #        #        - json: {"attachments":[{"attachmentName":"附件","fileId":$fileId3}],"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"docs":[{"fileId":$fileId2}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$yuzan_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId2,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
      #    api: api/mobile-shield/createAllAtOnce.yml
      #    validate:
#      - eq: ["content.code",1435002]
#      - gt: ["content.message",'参数错误: 当前项目下，附件不存在，fileId:']

- test:
    name: "一步到位创建流程-对内，附件不存在"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":*****************}],"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"docs":[{"fileId":$fileId2}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$yuzan_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId2,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437511]
      - contains: ["content.message","文档不存在"]

- test:
    name: "一步到位创建流程-对内，附件为空"
    variables:
      - app_id: $app_Id1
      - json: {"attachments":[{"attachmentName":"附件","fileId":}],"accountId":$yuzan_accountId,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"docs":[{"fileId":$fileId2}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$yuzan_accountId,"signerAccountName":"","signerAuthorizerId":$yuzan_accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId2,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]


