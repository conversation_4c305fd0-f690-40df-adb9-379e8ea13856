- config:
    name: "V3一步发起rpc接口改动相关case"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ${ENV(envCode)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - mobile_fengjiu: ***********
      - mobile_caogu: ***********
      - sealId_authorization: ${ENV(sealId_authorization)}
      - sealId_notauthorization: ${ENV(sealId_notauthorization)}
      - sealId_notbelong: ${ENV(sealId_notbelong)}
      - sealId_dy: ${ENV(sealId_dy)}
      - signerAccountId: ${ENV(standardoid)}
      - authorizedAccountId: ${ENV(orgid_sx)}
      - billIsolationCode: ${ENV(billIsolationCode)}
      - billStrategy: ${ENV(billStrategy)}
      - sealId_sx_acrossEnterprise: ${ENV(sealId_sx_acrossEnterprise)}
      - sealId_sx_new: ${ENV(sealId_sx_new)}
      - sealId_dy_acrossEnterprise: ${ENV(sealId_dy_acrossEnterprise)}

#异常场景
- test:
    name: createFlowOneStepRPC - "企业自动签印章和签署主体不匹配"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_notbelong,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 印章和签署主体不匹配"]


- test:
    name: createFlowOneStepRPC - "企业自动签印章未授权给平台方"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_notauthorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1436012]
      - contains: ["content.message", "未授权给平台方"]


- test:
    name: createFlowOneStepRPC - "企业自动签未传印章id"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 企业自动签印章id不能为空"]




- test:
    name: createFlowOneStepRPC - "企业自动签-隔离码与产品id不匹配"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": "1TRNI49HGG5U7",
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435405]
      - contains: ["content.message", "企业账户中电子合同份数不足"]



- test:
    name: createFlowOneStepRPC - "企业自动签-隔离码与产品id不匹配-不传付费方"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": "1TRNI49HGG5U7",
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435405]
      - contains: ["content.message", "企业账户中电子合同份数不足"]




- test:
    name: createFlowOneStepRPC - "企业自动签-隔离码失效"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": "1TRNI497#$%^&**&^",
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435405]
      - contains: ["content.message", "企业账户中电子合同份数不足"]



- test:
    name: createFlowOneStepRPC - "企业自动签-actorIndentityType=0"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "0",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 企业自动签主体类型必须为企业"]


- test:
    name: createFlowOneStepRPC - "企业自动签-actorIndentityType=3"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "3",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 企业自动签主体类型必须为企业"]


- test:
    name: createFlowOneStepRPC - "企业自动签-actorIndentityType=4"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "4",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 企业自动签主体类型必须为企业"]




- test:
    name: createFlowOneStepRPC - "平台自动签-autoExecuteType=0%&autoExecute=true&platformSign=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "0",
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]


- test:
    name: createFlowOneStepRPC - "平台自动签-autoExecuteType=1%&autoExecute=true&platformSign=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "1",
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]




- test:
    name: createFlowOneStepRPC - "平台自动签-autoExecuteType=2%&autoExecute=true&platformSign=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "2",
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]


- test:
    name: createFlowOneStepRPC - "平台自动签-autoExecuteType=4%&autoExecute=true&platformSign=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "4",
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]


- test:
    name: createFlowOneStepRPC - "企业手动签-autoExecuteType=0%&autoExecute=false&platformSign=false"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "0",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": false,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]




- test:
    name: createFlowOneStepRPC - "企业手动签-autoExecuteType=4%&autoExecute=false&platformSign=false"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "4",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": false,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]




- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=0&autoExecute=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "0",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]

- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=1&autoExecute=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "1",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]


- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=2&autoExecute=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "2",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]


- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=4&autoExecute=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "4",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]

- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType不传&autoExecute=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 请调整发起参数autoExecuteType&autoExecute&platformSign后重试"]


- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=false"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": false,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 企业自动签签署区必须自动执行"]

- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute不传"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 企业自动签签署区必须自动执行"]

- test:
    name: createFlowOneStepRPC - "平台自动签-autoExecute=true&platformSign=true&指定印章id未授权给平台方"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_notauthorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],

          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437182]
      - eq: ["content.message", "未获取该企业有效的印章授权"]

- test:
    name: createFlowOneStepRPC - "平台自动签-autoExecuteType=3&autoExecute=true&platformSign=true"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_notauthorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 请调整发起参数autoExecuteType&platformSign后重试"]

- test:
    name: createFlowOneStepRPC - "平台自动签-一个签署区不能指定多种类型"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $signerAccountId,
          "flowConfigInfo": {

            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "4",
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_notauthorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不合法的autoExecuteType,请调整发起参数autoExecuteType后重试"]


- test:
    name: createFlowOneStepRPC - "平台自动签-付费方余额不足"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $signerAccountId,
          "flowConfigInfo": {

            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_notauthorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435403]
      - contains: ["content.message", "账户中电子合同份数不足，请购买套餐后再操作"]





#正向场景
- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、传入扣费策略+隔离码"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]



- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、传入扣费策略+隔离码、传入新的企业授权印章"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1-指定新的授权印章",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_sx_new,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]






      #- test:
      #    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、传入扣费策略、不传隔离码"
      #    variables:
      #        - json : {
      #        "appId":$app_id,
      #        "attachments": [
      #        {
      #            "accountId": "",
      #            "attachmentName": "",
      #            "bizFileId": "",
      #            "fileId": $fileId1,
      #            "operatorAccountId": "",
      #            "status": 0
      #        }
      #        ],
      #        "docs": [
      #        {
      #            "fileId": $fileId1,
      #            "fileName": "文档1"
      #        }
      #        ],
      #        "flowInfo": {
      #            "autoArchive": true,
      #            "autoInitiate": true,
      #            "businessScene": "场景包年-一步发起rpc1",
      #            "initiatorAccountId": "",
      #            "initiatorAuthorizedAccountId": "",
      #            "signValidity": "",
      #            "contractRemind": "",
      #            "contractValidity": "",
      #            "payerAccountId": $authorizedAccountId,
      #            "flowConfigInfo": {
      #            "billConfig": {
      #                "billStrategy": $billStrategy
      #            },
      #            "notifyConfig": {
      #                "noticeDeveloperUrl": "",
      #                "noticeType": "",
      #                "sendNotice": true
      #            }
      #            }
      #        },
      #        "signers": [
      #        {
      #            "autoExecuteType": "3",
      #            "platformSign": false,
      #            "signOrder": 0,
      #            "signerAccount": {
      #            "authorizedAccountId": $authorizedAccountId,
      #            "noticeType": "",
      #            "signerAccountId": $signerAccountId
      #            },
      #            "signFields": [
      #            {
      #            "actorIndentityType": "2",
      #            "autoExecute": true,
      #            "fileId": $fileId1,
      #            "posBean": {
      #                "addSignTime": true,
      #                "posPage": "1",
      #                "posX": 110,
      #                "posY": 110,
      #                "signDateBean": {
      #                    "fontSize": 12,
      #                    "posPage": 1,
      #                    "posX": 50,
      #                    "posY": 100
      #                },
      #                "signDateBeanType": 1
      #            },
      #            "sealId": $sealId_authorization,
      #            "sealIds": [],
      #            "sealType": "",
      #            "signType": 1
      #            }
      #            ],
      #            "thirdOrderNo": ""
      #        }
      #        ]
      #        }
      #    api: api/scene_Packyear/createFlowOneStepRPC.yml
      #
      #    validate:
#      - eq: ["content.code", 1435405]
#        - contains: ["content.message", "企业账户中电子合同份数不足"]



- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、不传扣费策略、不传隔离码-走历史套餐扣费但是付费方余额不足"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $signerAccountId,
          "flowConfigInfo": {
            "billConfig": {
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435403]
      - eq: ["content.message", "账户中电子合同份数不足，请购买套餐后再操作"]






- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、不传扣费策略、不传隔离码"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {

            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]


- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、不传扣费策略、不传隔离码"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {

            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]



- test:
    name: createFlowOneStepRPC - "企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、不传扣费策略、不传隔离码"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {

            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "autoExecuteType": "3",
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]






#历史回归
- test:
    name: createFlowOneStepRPC - "平台自动签（指定印章id&不指定印章id）+ 多主体手动签署+历史产品扣费"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {

            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        },
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        },
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_dy,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        },

        {
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": false,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealIds": [],
            "sealType": "",
            "signType": 1
          },
          ],
          "thirdOrderNo": ""
        },

        {
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $signerAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "0",
            "autoExecute": false,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealIds": [],
            "sealType": "",
            "signType": 1
          },
          ],
          "thirdOrderNo": ""
        }

        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]





- test:
    name: createFlowOneStepRPC - "平台自动签（指定印章id&不指定印章id）+ 多主体手动签署+走场景包年产品扣费"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        },
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        },
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_dy,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        },

        {
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": false,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealIds": [],
            "sealType": "",
            "signType": 1
          },
          ],
          "thirdOrderNo": ""
        },

        {
          "platformSign": false,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $signerAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "0",
            "autoExecute": false,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealIds": [],
            "sealType": "",
            "signType": 1
          },
          ],
          "thirdOrderNo": ""
        }

        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]





- test:
    name: createFlowOneStepRPC - "平台自动签"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_dy,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]


- test:
    name: createFlowOneStepRPC - "平台自动签-actorIndentityType=0-走场景包年扣费"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {
            "billConfig": {
              "billIsolationCode": $billIsolationCode,
              "billStrategy": $billStrategy
            },
            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": $authorizedAccountId,
            "noticeType": "",
            "signerAccountId": $signerAccountId
          },
          "signFields": [
          {
            "actorIndentityType": "0",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": $sealId_authorization,
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]


- test:
    name: createFlowOneStepRPC - "平台自动签-不传签署人签署主体-走历史产品扣费"
    variables:
      - json: {
        "appId":$app_id,
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "场景包年-一步发起rpc1",
          "initiatorAccountId": "",
          "initiatorAuthorizedAccountId": "",
          "signValidity": "",
          "contractRemind": "",
          "contractValidity": "",
          "payerAccountId": $authorizedAccountId,
          "flowConfigInfo": {

            "notifyConfig": {
              "noticeDeveloperUrl": "",
              "noticeType": "",
              "sendNotice": true
            }
          }
        },
        "signers": [
        {
          "platformSign": true,
          "signOrder": 0,
          "signerAccount": {
            "authorizedAccountId": "",
            "noticeType": "",
            "signerAccountId": ""
          },
          "signFields": [
          {
            "actorIndentityType": "2",
            "autoExecute": true,
            "fileId": $fileId1,
            "posBean": {
              "addSignTime": true,
              "posPage": "1",
              "posX": 110,
              "posY": 110,
              "signDateBean": {
                "fontSize": 12,
                "posPage": 1,
                "posX": 50,
                "posY": 100
              },
              "signDateBeanType": 1
            },
            "sealId": "",
            "sealIds": [],
            "sealType": "",
            "signType": 1
          }
          ],
          "thirdOrderNo": ""
        }
        ]
      }
    api: api/scene_Packyear/createFlowOneStepRPC.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "执行成功"]



- test:
    name: V3一步发起对外接口 - "不支持企业自动签-autoExecuteType=3&autoExecute=true&platformSign=false、传入扣费策略+隔离码"
    variables:
      - json: {
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "重定向0819",
          "initiatorAccountId":"",
          "flowConfigInfo":
            {
              "noticeType": "3,4",
              "redirectUrl": "",
              "signPlatform": "1,2,3,4",
              "billConfig": {
                "billIsolationCode": $billIsolationCode,
                "billStrategy": $billStrategy
              },
            },
          "remark": "",
          "signValidity": "",
          "payerAccountId": ""
        },
        "signers":
          [
          {
            "platformSign": false,
            "signOrder": 1,
            "signerAccount":
              {
                "authorizedAccountId": $authorizedAccountId,
                "noticeType": "",
                "signerAccountId": $signerAccountId
              },
            "signfields":
              [
              {
                "autoExecute": true ,
                "actorIndentityType":2,
                "sealId":"",
                "fileId": $fileId1,
                "posBean": {
                  "posPage": "1",
                  "posX": 550,
                  "posY": 210
                },
                "sealType": "",
                "signDateBeanType":"1",
                "signDateBean": {
                  "fontSize": 20,
                  "posX": 220,
                  "posY": 250
                },
                "signType": 1,
                "width": 150
              }
              ],
            "thirdOrderNo": "11111"
          }
          ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不允许用户自身签"]


- test:
    name: V3一步发起对外接口 - "不支持个人自动签-autoExecuteType=3&autoExecute=true&platformSign=false"
    variables:
      - json: {
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "重定向0819",
          "initiatorAccountId":"",
          "flowConfigInfo":
            {
              "noticeType": "3,4",
              "redirectUrl": "",
              "signPlatform": "1,2,3,4"

            },
          "remark": "",
          "signValidity": "",
          "payerAccountId": ""
        },
        "signers":
          [
          {
            "platformSign": false,
            "signOrder": 1,
            "signerAccount":
              {
                "authorizedAccountId": $signerAccountId,
                "noticeType": "",
                "signerAccountId": $signerAccountId
              },
            "signfields":
              [
              {
                "autoExecute": true ,
                "actorIndentityType":0,
                "sealId":"",
                "fileId": $fileId1,
                "posBean": {
                  "posPage": "1",
                  "posX": 550,
                  "posY": 210
                },
                "sealType": "",
                "signDateBeanType":"1",
                "signDateBean": {
                  "fontSize": 20,
                  "posX": 220,
                  "posY": 250
                },
                "signType": 1,
                "width": 150
              }
              ],
            "thirdOrderNo": "11111"
          }
          ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不允许用户自身签"]



- test:
    name: V3一步发起对外接口 - 成功发起平台自动签+多主体手动签署
    variables:
      - json: {
        "attachments": [
        {
          "accountId": "",
          "attachmentName": "",
          "bizFileId": "",
          "fileId": $fileId1,
          "operatorAccountId": "",
          "status": 0
        }
        ],
        "docs": [
        {
          "fileId": $fileId1,
          "fileName": "文档1"
        }
        ],
        "flowInfo": {
          "autoArchive": true,
          "autoInitiate": true,
          "businessScene": "重定向0819",
          "initiatorAccountId":"",
          "flowConfigInfo":
            {
              "noticeType": "3,4",
              "redirectUrl": "",
              "signPlatform": "1,2,3,4"

            },
          "remark": "",
          "signValidity": "",
          "payerAccountId": ""
        },
        "signers":
          [{
             "platformSign": true,
             "signOrder": 1,
             "signerAccount":
               {
                 "authorizedAccountId": $signerAccountId,
                 "noticeType": "",
                 "signerAccountId": $signerAccountId
               },
             "signfields":
               [
               {
                 "autoExecute": true ,
                 "actorIndentityType":0,
                 "sealId":"",
                 "fileId": $fileId1,
                 "posBean": {
                   "posPage": "1",
                   "posX": 550,
                   "posY": 210
                 },
                 "sealType": "",
                 "signDateBeanType":"1",
                 "signDateBean": {
                   "fontSize": 20,
                   "posX": 220,
                   "posY": 250
                 },
                 "signType": 1,
                 "width": 150
               }
               ],
             "thirdOrderNo": "11111"
           },
           {
             "platformSign": true,
             "signOrder": 1,
             "signerAccount":
               {
                 "authorizedAccountId": $signerAccountId,
                 "noticeType": "",
                 "signerAccountId": $signerAccountId
               },
             "signfields":
               [
               {
                 "autoExecute": true ,
                 "actorIndentityType":0,
                 "sealId":$sealId_authorization,
                 "fileId": $fileId1,
                 "posBean": {
                   "posPage": "1",
                   "posX": 550,
                   "posY": 210
                 },
                 "sealType": "",
                 "signDateBeanType":"1",
                 "signDateBean": {
                   "fontSize": 20,
                   "posX": 220,
                   "posY": 250
                 },
                 "signType": 1,
                 "width": 150
               }
               ],
             "thirdOrderNo": "11111"
           },
           {
             "platformSign": true,
             "signOrder": 1,
             "signerAccount":
               {
                 "authorizedAccountId": $signerAccountId,
                 "noticeType": "",
                 "signerAccountId": $signerAccountId
               },
             "signfields":
               [
               {
                 "autoExecute": true ,
                 "actorIndentityType":0,
                 "sealId":$sealId_dy,
                 "fileId": $fileId1,
                 "posBean": {
                   "posPage": "1",
                   "posX": 550,
                   "posY": 210
                 },
                 "sealType": "",
                 "signDateBeanType":"1",
                 "signDateBean": {
                   "fontSize": 20,
                   "posX": 220,
                   "posY": 250
                 },
                 "signType": 1,
                 "width": 150
               }
               ],
             "thirdOrderNo": "11111"
           },
           {
             "platformSign": false,
             "signOrder": 1,
             "signerAccount":
               {
                 "authorizedAccountId": $signerAccountId,
                 "noticeType": "",
                 "signerAccountId": $signerAccountId
               },
             "signfields":
               [
               {
                 "autoExecute": false ,
                 "actorIndentityType":0,
                 "sealId":"",
                 "fileId": $fileId1,
                 "posBean": {
                   "posPage": "1",
                   "posX": 550,
                   "posY": 210
                 },
                 "sealType": "",
                 "signDateBeanType":"1",
                 "signDateBean": {
                   "fontSize": 20,
                   "posX": 220,
                   "posY": 250
                 },
                 "signType": 1,
                 "width": 150
               }
               ],
             "thirdOrderNo": "11111"
           },
           {
             "platformSign": false,
             "signOrder": 1,
             "signerAccount":
               {
                 "authorizedAccountId": $authorizedAccountId,
                 "noticeType": "",
                 "signerAccountId": $signerAccountId
               },
             "signfields":
               [
               {
                 "autoExecute": false ,
                 "actorIndentityType":2,
                 "sealId":"",
                 "fileId": $fileId1,
                 "posBean": {
                   "posPage": "1",
                   "posX": 550,
                   "posY": 210
                 },
                 "sealType": "",
                 "signDateBeanType":"1",
                 "signDateBean": {
                   "fontSize": 20,
                   "posX": 220,
                   "posY": 250
                 },
                 "signType": 1,
                 "width": 150
               }
               ],
             "thirdOrderNo": "11111"
           }
          ]
      }
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]