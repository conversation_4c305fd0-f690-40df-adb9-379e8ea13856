- config:
    name: "0709接口测试用例-appid未配置附件上传"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf: data/个人借贷合同.pdf
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}

- test:
    name: "创建签署人账号"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_1
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId


- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:

      - accountId: $accountId
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: "文件直传创建一个文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:

      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]

      - eq: ["content.msg", "成功"]




- test:
    name: 一步发起rpc接口-个人签指定附件上传
    variables:
      - flowId: ''
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0709迭代指定附件上传"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}

      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - signValidity:
      - docs: [$doc]

    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]


- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 查询流程配置
    variables:
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - querySpaceAccountId: $accountId
      - menuId: ''
      - scenario: 1
      - batchSign: false
      - operator_id: $accountId
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.data.allowedUploadAttachment",True]


- test:
    name: 一步发起rpc接口-个人签-不指定上传
    variables:
      - flowId: ''
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0709迭代不指定附件上传"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}

      - signers: [{"allowedUploadAttachment": false, "signOrder": 0,"signerAccountId": $accountId,  "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - docs: [$doc]
      - signValidity:

    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]


- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 查询流程配置
    variables:
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - querySpaceAccountId: $accountId
      - menuId: ''
      - scenario: 1
      - batchSign: false
      - operator_id: $accountId
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.data.allowedUploadAttachment",False]

- test:
    name: 一步发起rpc接口-个人签-不传
    variables:
      - flowId: ''
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0709迭代不指定附件上传"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}

      - signers: [{"signOrder": 0, "signerAccountId": $accountId, "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - docs: [$doc]
      - signValidity: 0

    api: api/signflow/signflows/createFlowInput.yml
    #    extract:
    #      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",流程已过期]
      - eq: ["content.code", 1437169]

- test:
    name: 一步发起rpc接口-个人签-不传
    variables:
      - flowId: ''
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {fileId: $fileId1, attachmentName: "个人借贷合同.pdf"}
      - recipients: []
      - accountId: $accountId
      - authorizerId: $accountId
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "0709迭代不指定附件上传"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}

      - signers: [{"signOrder": 0, "signerAccountId": $accountId, "signerAuthorizedAccountId": $accountId,    "signerAuthorizedAccountType": 0,   "signerSignRoles": 0,  "signfields": [{"autoExecute": false,        "fileId": $fileId1,         "posBean": {},       "signType": 0,        "signerRoleType": 0  }     ]  }   ]
      - attachments: []
      - docs: [$doc]
      - signValidity:

    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]


- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 查询流程配置
    variables:
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - querySpaceAccountId: $accountId
      - menuId: ''
      - scenario: 1
      - batchSign: false
      - operator_id: $accountId
    api: api/signflow/signflows/signflowsConfig_para.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.data.allowedUploadAttachment",False]
