#轩辕api-创建机构账号
- api:
    def: xy_org_create($json)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/createByThirdPartyUserId
      method: POST
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
      json: $json
    validate:
      - eq: [status_code, 200]

#轩辕api-获取账号详情
- api:
    def: xy_org_get_info($torgId)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/$torgId
      method: GET
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
    validate:
      - eq: [status_code, 200]


#轩辕api-注销账号
- api:
    def: xy_org_del_account($orgId)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/$orgId
      method: DELETE
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
    validate:
      - eq: [status_code, 200]



#创建机构账号-使用第三方userId
- api:
    def: org_create_by_third($creator, $idNumber, $idType, $name, $thirdPartyUserId)
    request:
      url: ${ENV(footstone_user_url)}/v1/v1/organizations/createByThirdPartyUserId
      method: POST
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
        X-Tsign-Open-Auth-Mode: $auth_mode
      json:
        {
          "creator": $creator,
          "idNumber": $idNumber,
          "idType": $idType,
          "name": $name,
          "thirdPartyUserId": $thirdPartyUserId
        }
    validate:
      - eq: [status_code, 200]

#获取账号详情
- api:
    def: org_get_info_by_third($thirdPartyUserId, $thirdPartyUserType)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/getByThirdId?thirdPartyUserId=$thirdPartyUserId&thirdPartyUserType=$thirdPartyUserType
      method: GET
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
        X-Tsign-Open-Auth-Mode: $auth_mode
    validate:
      - eq: [status_code, 200]


#账号注销
- api:
    def: org_del_by_third($thirdPartyUserId, $thirdPartyUserType)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/deleteByThirdId?thirdPartyUserId=$thirdPartyUserId&thirdPartyUserType=$thirdPartyUserType
      method: DELETE
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
        X-Tsign-Open-Auth-Mode: $auth_mode
    validate:
      - eq: [status_code, 200]


#账号修改
- api:
    def: org_update_by_third($thirdPartyUserId, $thirdPartyUserType, $name)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/updateByThirdId?thirdPartyUserId=$thirdPartyUserId&thirdPartyUserType=$thirdPartyUserType
      method: POST
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
        X-Tsign-Open-Auth-Mode: $auth_mode
      json:
        {
          "name": $name
        }
    validate:
      - eq: [status_code, 200]