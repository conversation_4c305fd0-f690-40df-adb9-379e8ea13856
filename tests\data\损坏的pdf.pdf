config:
  name: 迭代case

testcases:
  0513迭代case:
    testcase: testcases/iterate_cases/20210513Sign.yml

  0422迭代case-主体名称中括号忽略全角半角:
    testcase: testcases/iterate_cases/20210422Sign.yml

  创建流程的noticeType和redirectDelayTime参数校验:
    testcase: testcases/iterate_cases/20210108config.yml

  0109迭代case-文本签:
    testcase: testcases/iterate_cases/0109Sign.yml

  0623迭代case-文本签P1:
    testcase: testcases/iterate_cases/0623Sign_P1.yml

  0623迭代case-文本签P7:
    testcase: testcases/iterate_cases/0623Sign_P7.yml

  0623迭代case-pdf验签:
    testcase: testcases/iterate_cases/0623Sign_pdfS.yml

  0402迭代case-经办人:
    testcase: testcases/iterate_cases/0402Sign.yml

  0528迭代case-印章可见-handsign接口关于sealIds字段的校验:
    testcase: testcases/iterate_cases/0528Sign_handsign.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-轩辕实名:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_xuanyuan.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-悟空非实名:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_wukong.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-悟空实名-非:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_wukong_not_in_white.yml

  0528迭代case-印章可见-getSignSeals接口关于sealIds字段的校验-悟空实名-白名单:
    testcase: testcases/iterate_cases/0528Sign_getSignSeals_wukong_in_white.yml

  0611迭代case:
    testcase: testcases/iterate_cases/0611Sign.yml

  0611迭代case-0611onePerson_100signField_V3:
    testcase: testcases/iterate_cases/0611onePerson_100signField_V3.yml

  0611迭代case-0611Sign_100person:
    testcase: testcases/iterate_cases/0611Sign_100person.yml

  0628Sign_forceWill:
    testcase: testcases/iterate_cases/0628Sign_forceWill.yml

  0628Sign_noforceWill:
    testcase: testcases/iterate_cases/0628Sign_noforceWill.yml

  0709Sign:
    testcase: testcases/iterate_cases/0709Sign.yml

  0723Sign_attachment:
    testcase: testcases/iterate_cases/0723Sign_attachment.yml

  0820:
    testcase: testcases/iterate_cases/0820Sign.yml

  0917钉签权限:
    testcase: testcases/iterate_cases/0917DingPermission.yml

  0928原文文本签:
    testcase: testcases/iterate_cases/0928dataSignOriginal.yml

  20201015迭代用例:
    testcase: testcases/iterate_cases/20201015Sign.yml

  20201119迭代用例:
    testcase: testcases/iterate_cases/20201119Sign.yml

  pc性能优化项目迭代用例:
    testcase: testcases/iterate_cases/pcPerformanceSign.yml

  印章平台三期悟空用例:
    testcase: testcases/signflow_scene/sealPlatform_autoSign_wukong.yml

  印章平台三期轩辕用例:
    testcase: testcases/signflow_scene/sealPlatform_autoSign.yml

  获取免登签署地址用例:
    testcase: testcases/flow_checkSealInfo/0225demo.yml

  批量签幂等签署用例:
    testcase: testcases/iterate_cases/20210225BatchSign.yml

  募资企业授权印章签署用例:
    testcase: testcases/flow_checkSealInfo/signer_chooseSealId.yml

  印章平台自选印章签署用例:
    testcase: testcases/flow_checkSealInfo/signer_check.yml

  日期控件签署用例:
    testcase: testcases/iterate_cases/20210318Sign.yml

  20210722迭代用例:
    testcase: testcases/iterate_cases/20210722Sign.yml
  #
  #    20210819迭代用例/薪福通:
  #        testcase: testcases/iterate_cases/20210819Sign.yml


  20210819薪福通:
    testcase: testcases/iterate_cases/20210819Salary.yml

  20210819迭代:
    testcase: testcases/iterate_cases/20210819Sign.yml

  20210902SaaS指定意愿签署:
    testcase: testcases/iterate_cases/20210902AppointedWillingSign.yml

  20210819迭代用例-H5性能优化:
    testcase: testcases/iterate_cases/20210819_H5_performance.yml

  20211014迭代指定意愿:
    testcase: testcases/iterate_cases/20211014AppointedWillingSign.yml

  20211014迭代认证状态:
    testcase: testcases/iterate_cases/20211014identityRecord.yml

  20211109未完成下载1:
    testcase: testcases/iterate_cases/20211109candownload.yml

  20211109未完成下载2:
    testcase: testcases/iterate_cases/20211109cantdownload.yml

  20211125迭代:
    testcase: testcases/iterate_cases/20211125Iterate.yml