- config:
    name: 个人和企业印章的测试
    variables:
        - idNo: "513230198904164146"
        - idType: "19"
        - person: "唐飞燕"
        - color: "RED"
        - text: "贺达"
        - sealAlias: "贺达的印章"
        - templateType: "SQUARE"
        - height: 2
        - width: 2
        - name: "北京蚂蜂窝网络科技有限公司"
        - email: "<EMAIL>"
        - orgType: "CRED_ORG_USCC"
        - orgCode: "91110105669928206J"
        - legalName: "潘圣杰"
        - legalIdNo: "361001198505208479"
        - legalIdType: "19"
        - p_seal_path: data/heda_p_seal.jpeg
        - c_seal_path: data/heda_c_seal.jpeg
        - p_fileName: heda_p_seal.jpeg
        - c_fileName: heda_c_seal
#        - p_contentMd5: ${get_file_base64_md5($p_seal_path)}
#        - c_contentMd5: ${get_file_base64_md5($c_seal_path)}
#        - contentType: "image/jpeg"
    request:
        base_url: ${ENV(footstone_api_url)}
        headers:
            Content-Type: "application/json"
- test:
    name: 个人印章操作，包含创建、查询等
    testcase: testcases/seals/p_seal_action.yml

- test:
    name: 企业印章操作，包含创建、查询等
    variables: 
       idNo: $idNo
       idType: $idType
       person: $person
       name: $name
       orgType: $orgType
       orgCode: $orgCode
       legalName: $legalName
       legalIdNo: $legalIdNo
       legalIdType: $legalIdType
       color: $color
       height: $height
       width: $width
       sealAlias: $sealAlias
       templateType: $templateType
       text: $text
       hText: $c_seal_path
       qText: $c_fileName
       c_seal_path:
       c_fileName:
    testcase: testcases/seals/c_seal_action.yml