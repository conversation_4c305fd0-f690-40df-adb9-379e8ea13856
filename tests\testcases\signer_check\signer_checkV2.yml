- config:
    name: 签署人校验权限
    variables:
      - mobile1: 18767104153
      - mobile2: ***********
      - email: <EMAIL>
      - personOid1: ${getOid($mobile1)}
      - personOid2: ${getOid($mobile2)}
      - personOid3: ${getOid($email)}
      - idType_person: CRED_PSN_CH_IDCARD
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_2: autotest2${get_randomNo()}
      - thirdPartyUserId_person_3: autotest3${get_randomNo()}
      - thirdPartyUserId_person_4: autotest4${get_randomNo()}
      - name_person_1: 陈凯丽
      - idNumber_person_1: 362323199201061068
      - app_id: ${ENV(BZQ-App-Id)}
      - path_pdf: data/个人借贷合同.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    base_url: ${ENV(footstone_api_url)}

- test:
    name:  创建账号未实名，只包含姓名1
    variables:
      - json:
          {
            "name": 测试,
            "thirdPartyUserId": $thirdPartyUserId_person_1
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - accountId1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId1

- test:
    name: 创建账号未实名，只包含邮箱2
    variables:
      - json:
          {
            "email": <EMAIL>,
            "thirdPartyUserId": $thirdPartyUserId_person_2
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - accountId2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId2


- test:
    name: 创建账号未实名，只包含手机3
    variables:
      - json:
          {
            "mobile": '***********',
            "thirdPartyUserId": $thirdPartyUserId_person_3
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - accountId3: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId3

- test:
    name:  创建账号未实名，只包含身份类型4
    variables:
      - json:
          {
            "idType": CRED_PSN_PASSPORT,
            "name": 王瑶济,
            "idNumber":"3207211993052326341",
            "thirdPartyUserId": $thirdPartyUserId_person_4
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - accountId4: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId1


- test:
    name: 一步发起-签署人包含实名信息
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $personOid1,authorizedAccountId: $personOid1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 校验签署权限-入参校验-操作人不存在
    variables:
      - flowId: $flowId
      - accountId: 1111111
      - operatorId: $personOid1
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 已被注销]


- test:
    name: 校验签署权限-入参校验-成功
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - operatorId: $personOid1
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]

- test:
    name: 校验签署权限-操作人账号已实名-姓名证件号不一致
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - operatorId: $personOid2
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]
      - contains: ["content.data.failedReason", "当前登录账号信息（姓名：王*济，证件号：320***********2634，手机号：132****1366）与发起方指定的信息（姓名：梁*红，证件号：331***********3080，手机号：187****4153）不一致，无权访问，请切换其他账号。"]


- test:
    name: 校验签署权限-操作人账号未实名-邮箱号不一致
    variables:
      - flowId: $flowId
      - accountId: $personOid1
      - operatorId: $accountId2
      - redirectUrl:
      - message: ${ENV(message5)}
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]
      - contains: ["content.data.failedReason", $message]

#- test:
#    name: 校验签署权限-操作人账号未实名-无手机号邮箱号
#    variables:
#      - flowId: $flowId
#      - accountId: $personOid1
#      - operatorId: $accountId1
#      - redirectUrl:
#    api: api/signflow/flowSigners/checkSignAccess.yml
#    validate:
#      - contains: ["content.message", 成功]
#      - contains: ["content.data.failedReason",当前登录账号信息（手机号：无数据，邮箱号：无数据）与发起方指定的签署人信息（手机号：151****0928，邮箱号：443****qq.com）不一致，请切换其他账号访问]


- test:
    name: 一步发起-签署人不包含实名信息
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId3,authorizedAccountId: $accountId3}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 校验签署权限-操作人账号未实名-无手机号
    variables:
      - flowId: $flowId
      - accountId: $accountId3
      - operatorId: $accountId1
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]
      - contains: ["content.data.failedReason",当前登录账号信息（手机号：无数据）与发起方指定的信息（手机号：132****1366）不一致，无权访问，请切换其他账号。]

- test:
    name: 一步发起-签署人不包含实名信息
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId4,authorizedAccountId: $accountId4}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 校验签署权限-操作人账号已实名-证件类型不一致
    variables:
      - flowId: $flowId
      - accountId: $accountId4
      - operatorId: $personOid1
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 成功]
      - contains: ["content.data.failedReason",当前登录账号信息（姓名：梁*红，证件号：331***********3080，证件类型：大陆身份证）与发起方指定的信息（姓名：王*济，证件号：320************6341，证件类型：护照）不一致，无权访问，请切换其他账号。]
