validate:
  - eq: [status_code, 200]
request:
  url: /v2/signflows/createAllAtOnce
  method: POST
  headers: ${get_headers($app_id)}

  json:
    {   accountId: $signerAccountId,
        businessScene: $businessScene,
        docs: [
        {
          encryption: 0,
          fileId: $fileId,
          fileName: "22",
          source: 0
        }
        ],
        signers: [
        {
          signOrder: 1,
          signerAccounts: [
          {

            signerAccountId: $signerAccountId,
            signerAccountName: $name,
            signerAuthorizerId: $signerAuthorizerId,
            signerAuthorizerName: $name_org,
            signerNickname: $nickname,
            signerAuthorizerType: $signerAuthorizerType,
            signerSignRoles: $signerSignRoles
          }
          ],
          signfields: [
          {
            autoExecute: false,
            fileId: $fileId,
            posBean: {
              addSignTime: true,
              posPage: "1",
              posX: 110,
              posY: 110,
              qrcodeSign: false,
              signTimeFormat: "yyyy-MM-dd",
              width: 150
            },
            signType: 1,
            signerRoleType: "0",
            thirdOrderNo: "111"
          }
          ]
        }

        ]}

#一步更新流程
