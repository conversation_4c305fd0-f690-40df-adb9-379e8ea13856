- config:
    name: 手动-个人签-单页签-异常场景
    base_url: ${ENV(base_url)}

    variables:
      - perIdType: CRED_PSN_CH_IDCARD
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - group: ''
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:




- test:
    name: "创建新账号1"
    api: api/user/accounts/create_accounts.yml   #无email/mobile
    variables:
      - idNo: "350725198204117350"
      - idType: "19"
      - name: "孙明睿"
      - mobile: ***********
      - email: <EMAIL>
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: [ "content.data.accountId",0 ]

- test:
    name: 获取上传文件url
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]




- test:
    name: 创建流程和文档
    variables:
      - autoArchive: false
      - configInfo:
      - contractValidity:
      - extend:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId: $personOid1
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程详情
    api: api/signflow/aggregate_sign/select_detail.yml
    variables:
      - queryAccountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",0]

- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - accountId: $personOid1
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: 查询personOid1的印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - sealId_p1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]



- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 批量添加流程签署区
    variables:
      - flowId: $flowId
      - posBean: {}
      - posBean2: {addSignTime: True, keyword: '出借方', posPage: 1, posX: 0, posY: 0, qrcodeSign: True, width: 0} #关键字签署
      - posBean4: {addSignTime: False, keyword: '', posPage: 1, posX: 0, posY: 600, qrcodeSign: True, width: 0} #添加骑缝签署
      - autoExecute: 0cl
      - signfield1: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$personOid1,$personOid1,$sealId_p1,1)} #个人签
      - signfield2: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean2,4,$personOid1,,,1)}  #关键字签署
      - signfield4: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean4,2,$personOid1,$personOid1,,1)}  #添加骑缝签署
      - signfields: [$signfield1,$signfield2,$signfield4]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldIdsaa: content.data.signfieldIds.0
      - signfieldId1: content.data.signfieldIds.1
      - signfieldId2: content.data.signfieldIds.2
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: "/v1/signflows/{flowId}/executeUrl  扩展字段无initiatorAuthorizedAccountIds"
    variables:
      - app_id: $app_Id1
      - flowId: $flowId
      - accountId: $personOid1
      - urlType: 0
      - multiSubject: true
      - organizeId:
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]




- test:
    name: 添加_更新_执行签署区-签署方式不合法signType不合法，为abc-报错 未定义的signType
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,100,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", '未定义的signType']

- test:
    name: 添加_更新_执行签署区-签署日期字体为abc-报错不支持的字体类型
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0,signDateBean: {"fontName":"Microsoft","fontSize": 120,"format": "YYYY-MM-DD hh:mm:ss","posX": 0,"posY": 0}}
      - addSignfield_3: ${gen_signfield_data_V5(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", '参数错误: 不支持的字体类型']


      #- test:
      #    name: 添加_更新_执行签署区-报错 签署区不允许重复添加
      #    variables:
      #      - accountId: $personOid1
      #      - flowId: flowId
      #      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0, signDateBeanType: "1"}
      #      - addSignfield_3: ${gen_signfield_data_V5(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      #      - addSignfields: [$addSignfield_3,$addSignfield_3]
      #      - updateSignfields: []
      #      - signfieldIds: [$signfieldIdsaa]
      #      - approvalPolicy:
      #      - dingUser:
      #    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
      #    validate:
      - eq: ["content.message", '参数错误: 报错 签署区不允许重复添加']

- test:
    name: 添加_更新_执行签署区-签署主体类型不合法-报错 未定义的actorIndentityType
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(10,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", '未定义的actorIndentityType']


- test:
    name: 添加_更新_执行签署区-印章类别不合法,为abc-报错  未定义的sealType
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,abc)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", '未定义的sealType']


- test:
    name: 添加_更新_执行签署区-签署页码超过文档页数
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '100', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
#    validate:
#      - contains: ["content.message", '文档页数']


- test:
    name: 添加_更新_执行签署区-查询流程文档不存在
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V5(0,0,abc,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
#    extract:
#      - docSignResult_fileId: content.data.docSignResult.0.fileId
#      - signfieldSignResult_signfieldId: content.data.signfieldSignResult.0.signfieldId
#    validate:
#      - contains: ["content.message", "文档"]

- test:
    name: 添加_更新_执行签署区-posPage小于0-报错盖章位置超出文件范围，无法盖章
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: -1, posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", '参数错误: 盖章位置超出文件范围，无法盖章']
      - eq: ["content.code", 1435002]

- test:
    name: 添加_更新_执行签署区-posX为空-报错盖章位置超出文件范围，无法盖章
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1',  posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", '参数错误: 盖章位置超出文件范围，无法盖章']
      - eq: ["content.code", 1435002]

- test:
    name: 添加_更新_执行签署区-posX<0-报错盖章位置超出文件范围，无法盖章
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: -400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", '参数错误: 盖章位置超出文件范围，无法盖章']
      - eq: ["content.code", 1435002]

- test:
    name: 添加_更新_执行签署区-posY为空-报错盖章位置超出文件范围，无法盖章
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", '参数错误: 盖章位置超出文件范围，无法盖章']
      - eq: ["content.code", 1435002]

- test:
    name: 添加_更新_执行签署区-posY小于0-报错盖章位置超出文件范围，无法盖章
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: -200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", '参数错误: 盖章位置超出文件范围，无法盖章']
      - eq: ["content.code", 1435002]



- test:
    name: 添加_更新_执行签署区-posPage为abc-报错盖章位置超出文件范围，无法盖章
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: 'abc', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: [$signfieldIdsaa]
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", '参数错误: 盖章位置超出文件范围，无法盖章']
      - eq: ["content.code", 1435002]
