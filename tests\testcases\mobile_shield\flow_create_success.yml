- config:
    name: "e签盾流程创建-成功场景"
    base_url: ${ENV(footstone_mobile_shield_url)}
    variables:
      - app_id1: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: "文件上传 - 个人借贷合同ckl.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同ckl.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]






#验证接口1 - /v1/signflows
- test:
    name: "创建e签盾签署流程 -  /v1/signflows"
    variables:
      - autoArchive: false
      - businessScene: "创建e签盾流程 - /v1/signflows"
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":2}
      - contractValidity: "*************"
      - initiatorAccountId: $accountId_ckl
      - payerAccountId: null
    api: api/mobile-shield/signflows_create.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建现有普通签署流程 -  /v1/signflows"
    variables:
      - autoArchive: false
      - businessScene: "创建现有流程 -  /v1/signflows"
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":1}
      - contractValidity: "*************"
      - initiatorAccountId: $accountId_ckl
      - payerAccountId: null
    api: api/mobile-shield/signflows_create.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]




#验证接口2 - /v1/signflows/createWithSignDocs
- test:
    name: "创建e签盾签署流程 -  createWithSignDocs"
    variables:
      - autoArchive: false
      - businessScene: "创建e签盾流程 -createWithSignDocs "
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":2}
      - contractValidity: "*************"
      - initiatorAccountId: $accountId_ckl
      - createWay: "normal"
      - payerAccountId: null
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/mobile-shield/createWithSignDocs.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建现有普通签署流程 -  createWithSignDocs"
    variables:
      - autoArchive: false
      - businessScene: "创建现有签署流程 -createWithSignDocs "
      - configInfo: {"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"1,2,3","redirectUrl":"https://xh5.xfangl.com/open-inspection/#/MyWallet?encrypt\u003d%s","signPlatform":"1,2","mobileShieldWay":1}
      - contractValidity: "*************"
      - initiatorAccountId: $accountId_ckl
      - createWay: "normal"
      - payerAccountId: null
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/mobile-shield/createWithSignDocs.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]




#验证接口3 - createAllAtOnce
- test:
    name: "创建e签盾签署流程 -  createAllAtOnce"
    variables:
      - json: {"accountId":$accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"创建e签盾签署流程 -  createWithSignDocs","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":2},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建现有签署流程 -  createAllAtOnce"
    variables:
      - json: {"accountId":$accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"创建e签盾签署流程 -  createWithSignDocs","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



#验证接口4 - createFlowOneStep
- test:
    name: "创建e签盾签署流程 -  createFlowOneStep"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "创建e签盾签署流程 -  createFlowOneStep",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":2 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建现有签署流程 -  createFlowOneStep"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "创建现有签署流程 -  createFlowOneStep",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]






#异常场景 - 流程类型+签名域类型组合
#验证接口3 - createAllAtOnce
- test:
    name: "创建e签盾签署流程并添加个人签名域 -  createAllAtOnce"
    variables:
      - json: {"accountId":$accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"创建e签盾签署流程 -  createWithSignDocs","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":2},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

- test:
    name: "创建e签盾签署流程并添加法人签名域 -  createAllAtOnce"
    variables:
      - json: {"accountId":$accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"创建e签盾签署流程 -  createWithSignDocs","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":2},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"3","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

- test:
    name: "创建e签盾签署流程并添加经办人签名域 -  createAllAtOnce"
    variables:
      - json: {"accountId":$accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"创建e签盾签署流程 -  createWithSignDocs","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":2},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]


- test:
    name: "创建现有签署流程并且添加4种签名域类型 -  createAllAtOnce"
    variables:
      - signers: [{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]},{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]},{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"3","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$sixian_organize_id,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]},{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}]
      - json: {"accountId":$accountId_ckl,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"创建e签盾签署流程 -  createWithSignDocs","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers": $signers}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



#验证接口4 - createFlowOneStep
- test:
    name: "创建e签盾签署流程并添加法人签名域 -  createFlowOneStep"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "创建e签盾签署流程 -  createFlowOneStep",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":2 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 3, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

- test:
    name: "创建e签盾签署流程并添加经办人签名域 -  createFlowOneStep"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "创建e签盾签署流程 -  createFlowOneStep",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":2 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 4, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

- test:
    name: "创建e签盾签署流程并添加个人签名域 -  createFlowOneStep"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "创建e签盾签署流程 -  createFlowOneStep",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":2 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 目前e签盾仅支持机构公章手动签署"]

- test:
    name: "创建e签盾签署流程并添加个人签名域 autoExecute :true-  createFlowOneStep"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "创建e签盾签署流程 -  createFlowOneStep",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":2 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", "不支持自动签署"]
      #- test:
      #    name: "创建现有签署流程并且添加4种签名域类型 -  createFlowOneStep"
      #    variables:
      #        - doc : {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      #        - attachments: []
      #        - docs: [$doc]
      #        - copiers: []
      #        - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "创建现有签署流程 -  createFlowOneStep",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      #        - signers1: {signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }
      #        - signers2: {signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 3, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 773 }
      #        - signers3: {signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 4, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 727 }
      #        - signers4: {signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 717 }
      #        - signers: [$signers1,$signers2,$signers3,$signers4]
      #
      #    api: api/mobile-shield/createFlowOneStep.yml
      #    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", "成功"]






















