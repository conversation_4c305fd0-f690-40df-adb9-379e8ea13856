- config:
        name: "创建1109用例可以签署中下载"
        base_url: ${ENV(footstone_api_url)}
        variables:
            - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
            -   path_pdf: data/个人借贷合同.pdf
            -   path_pdf1: data/劳动合同书.pdf
            -   path_pdf2: data/劳动合同书12.pdf
            -   group:
            -   app_id: ${ENV(gan_appid_download)}
            -   fileId1: ${get_file_id($app_id,$path_pdf1)}
            -   imagfileId: "5d134d1a5033458293a122bded604950"

-   test:
        name: "获取gan_appid_download的appSecret"
        variables:
            -   app_id: ${ENV(gan_appid_download)}
        api: api/iterate_cases/getAppInfo.yml
        extract:
            -   app_secret: content.appSecret
        validate:
            -   eq: ["status_code", 200]

-  test:
        name: "创建签署人账号 - gan"
        variables:
            - app_id: ${ENV(gan_appid_download)}
            - app_secret: $app_secret
            - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
            - name: 甘舰戈
            - idNumber: ******************
            - idType: "CRED_PSN_CH_IDCARD"
            - email:
            - mobile: ***********
        api: api/iterate_cases/p_createByThirdPartyUserId.yml
        extract:
          - accountId_gan: content.data.accountId
          - yuzan_accountId: content.data.accountId
-   test:
        name: "创建梁贤红账号"
        variables:
            -   app_id: ${ENV(gan_appid_download)}
            -   app_secret: $app_secret
            -   thirdPartyUserId: caogu20191111411321213
            -   name: 梁贤红
            -   idNumber: 331082199211223080
            -   idType: "CRED_PSN_CH_IDCARD"
            -   email:
            -   mobile: ***********
        api: api/iterate_cases/p_createByThirdPartyUserId.yml
        extract:
            -   caogu_accountId: content.data.accountId

-   test:
        name: "使用caogu_accountId创建机构"
        variables:
            -   app_id: ${ENV(gan_appid_download)}
            -   app_secret: $app_secret
            -   thirdPartyUserId: sixian201911141132
            -   creator: $caogu_accountId
            -   idNumber: 91341324MA2RMB1W3T
            -   idType: CRED_ORG_USCC
            -   name: 泗县深泉纯净水有限公司
            -   orgLegalIdNumber: 331082199211223080
            -   orgLegalName: 梁贤红
        api: api/iterate_cases/o_createByThirdPartyUserId.yml
        extract:
            -   sixian_organize_id: content.data.orgId

-   test:
        name: 静默签署场景-签署授权:个人账户
        variables:
            -   accountId: $yuzan_accountId
            -   type: silent
            -   deadline: "2022-12-31 23:59:59"
        api: api/signflow/signAuth/signAuth.yml
        validate:
            -   eq: ["content.code",0]
            -   eq: ["status_code", 200]


-   test:
        name: "获取对应的实名组织详情"
        variables:
            -   app_id: ${ENV(gan_appid_download)}
            -   app_secret: $app_secret
            -   orgId: $sixian_organize_id
        extract:
            -   standard_sixian_organize_id: content.data.base.ouid
        api: api/iterate_cases/getFinalRealnameOrgInfo.yml
        validate:
            -   eq: ["content.code",0]

-   test:
        teardown_hooks:
            - ${teardown_seals($response)}
        name: "查询实名组织下的企业印章"
        variables:
            -   app_id: ${ENV(gan_appid_download)}
            -   app_secret: $app_secret
            -   orgId: "8fbe5926547e40149978fb8c5a448394"
        api: api/iterate_cases/searchOrganizationsSeal.yml
        extract:
            -   org_sealId1: org_sealId1
            -   org_sealId2: org_sealId2
            -   org_sealId3: org_sealId3
            -   legal_sealId1: legal_sealId1
            -   cancellation_sealId: cancellation
        validate:
            -   eq: ["content.code",0]
            -   eq: ["content.message", "成功"]



- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
        - app_id: ${ENV(gan_appid_download)}
        -   app_secret: $app_secret
        - accountId: $yuzan_accountId
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: ["status_code", 200]


-   test:
        name: 一步发起指定证书id用户手动签-成功
        variables:
            -   app_id: ${ENV(gan_appid_download)}
            -   app_secret: $app_secret
            -   doc: {encryption: 0,fileId: "bc4c3e9d907146b29e6e75fd1c16fc56", fileName: '文件1', source: 0}
            -   attachments: [{"attachmentName":"附件","fileId":$imagfileId}]
            -   docs: [$doc]
            -   copiers: []
            -   flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
            -   signers:  [
         {
            "signOrder": 1,
            "signerAccount":
            {
                "signerAccountId": $yuzan_accountId,
                "authorizedAccountId":  $yuzan_accountId
            },
              "signfields":
                [
                  {
                    "autoExecute": false ,
                    "actorIndentityType": 0,
                    "sealId": "",
                    "fileId": "bc4c3e9d907146b29e6e75fd1c16fc56",
                    "posBean": {
                      "addSignTime": true,
                      "posPage": "5",
                      "posX": 444,
                      "posY": 270
                    },
                    "assignedPosbean": "true",
                    "sealType": "",
                    "signDateBeanType": "1",
                    "signDateBean": {
                      "format": "yyyy       MM       dd",
                      "posPage": "5",
                      "fontSize": 14,
                      "posX": 343,
                      "posY": 200,
                      "fontName": "simsun"
                    },
                    "signType": 1,
                    "width": 136
                  } ,
                  {
                    "autoExecute": false ,
                    "actorIndentityType": 0,
                    "sealId": "",
                    "fileId": "bc4c3e9d907146b29e6e75fd1c16fc56",
                    "posBean": {
                      "addSignTime": true,
                      "posPage": "5",
                      "posX": 444,
                      "posY": 370
                    },
                    "assignedPosbean": "true",
                    "sealType": "",
                    "signDateBeanType": "1",
                    "signDateBean": {
                      "format": "yyyy-MM-dd",
                      "posPage": "5",
                      "fontSize": 14,
                      "posX": 343,
                      "posY": 100,
                      "fontName": "simsun"
                    },
                    "signType": 1,
                    "width": 136
                  },
                  {
                    "autoExecute": false ,
                    "actorIndentityType": 0,
                    "sealId": "",
                    "fileId": "bc4c3e9d907146b29e6e75fd1c16fc56",
                    "posBean": {
                      "addSignTime": true,
                      "posPage": "5",
                      "posX": 444,
                      "posY": 470
                    },
                    "assignedPosbean": "true",
                    "sealType": "",
                    "signDateBeanType": "1",
                    "signDateBean": {
                      "format": "yyyy-MM-dd",
                      "posPage": "5",
                      "fontSize": 14,
                      "posX": 343,
                      "posY": 200000,
                      "fontName": "simsun"
                    },
                    "signType": 1,
                    "width": 136
                  }
                ],
            "thirdOrderNo": "企业2"
        }
    ]
        api: api/signflow/signflows/createFlowOneStep.yml
        extract:
          - sign_flow_one: content.data.flowId
        validate:
            -   eq: ["content.code",0]
            -   contains: ["content.message",'成功']

-   test:
        name: "获取签署链接"
        variables:
            -   app_secret: $app_secret
            -   flowId: $sign_flow_one
            -   accountId: $yuzan_accountId
            -   urlType: 0
            -   multiSubject: true
            -   organizeId: $sixian_organize_id
            -   compressContext: true
        api: api/iterate_cases/executeUrl.yml
        extract:
            -   shortUrl: content.data.shortUrl
            -   code: content.code
            -   message: content.message
        validate:
            -   eq: ["content.code",0]

