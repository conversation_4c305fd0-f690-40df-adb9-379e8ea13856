- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(base_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: ***********
      - path_pdf: 个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - idNo1: 130202199104016781
      - name1: esigntest-测试用户1
      - thirdPartyUserId1: esigntest-130202199104016781
      - idNo2: 230124200712193556
      - name2: esigntest-测试用户2
      - thirdPartyUserId2: esigntest-230124200712193556
      - idNo3: 230803201510286771
      - name3: esigntest-测试用户3
      - thirdPartyUserId3: esigntest-230803201510286771
      - client_id: pc
- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: 130202199104016781
      - name: esigntest-测试用户1
      - thirdPartyUserId: esigntest-13020219912gfrd04016781
    api: api/user/account_third/create_account_appid.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230124200712193556
      - name: esigntest-测试用户2
      - thirdPartyUserId: esigntest-23012443234c22vbnn1219
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建组织：个人账户2创建组织"
    variables:
      - accountId: $personOid2
      - orgType: CRED_ORG_CODE
      - orgCode: ********-X
      - legalIdNo:
      - legalName:
      - name: $orgName
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "用户1创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章1
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户2创建个人模版印章"
    variables:
      - accountId: $personOid2
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章2
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建企业模板印章"
    variables:
      - orgId: $orgOid1
      - color: RED
      - height: 150
      - width: 150
      - alias: 企业模板印章
      - type: TEMPLATE_ROUND
      - central: STAR
      - hText: 上弦文
      - qText: 下弦文
    api: api/seals/seals/create_c_template_seal_appid.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 获取文件信息
    variables:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 批量签署-指定位置签署场景-普通用户创建流程
    variables:
      - autoArchive: True
      - businessScene: 创建流程
      - initiatorAuthorizedAccountId: $orgOid1
      - initiatorAccountId: $personOid1
    api: api/signflow/signflows/signflowsCreateForOrganize.yml
    extract:
      - flowId7: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 添加流程文档：多文档
    variables:
      - flowId: $flowId7
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1,$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 查询企业印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_c_seals.yml
    variables:
      - orgId: $orgOid1
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 批量添加流程签署区
    variables:
      - flowId: $flowId7
      - posBean1: {addSignTime: True, key: '', posPage: '1', posX: 200, posY: 600, qrcodeSign: False, width: 159}
      - posBean2: {addSignTime: True, key: '', posPage: '2', posX: 300, posY: 600, qrcodeSign: False, width: 159}
      - signfield1: ${gen_signfield_data_V2(1,0,$fileId1,$flowId7,$posBean1,1,$personOid1,$orgOid1,$o_sealId,1)}
      - signfield2: ${gen_signfield_data_V2(1,0,$fileId2,$flowId7,$posBean2,1,$personOid1,$orgOid1,$o_sealId,1)}
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
      - signfieldId2: content.data.signfieldIds.1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId7
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

#创建流程2
- test:
    name: 普通用户创建流程
    variables:
      - autoArchive: True
      - businessScene: 创建流程
      - initiatorAuthorizedAccountId: $orgOid1
      - initiatorAccountId: $personOid1
    api: api/signflow/signflows/signflowsCreateForOrganize.yml
    extract:
      - flowId8: content.data.flowId
    teardown: ${flowId_collection($flowId8)}
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 添加流程文档：单文档
    variables:
      - flowId: $flowId8
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 批量添加流程签署区
    variables:
      - flowId: $flowId8
      - posBean1: {addSignTime: True, key: '', posPage: '1', posX: 300, posY: 300, qrcodeSign: False, width: 0}
      - posBean2: {addSignTime: True, key: '', posPage: '1', posX: 300, posY: 300, qrcodeSign: true, width: 0}
      - signfield1: ${gen_signfield_data_V2(1,0,$fileId1,$flowId8,$posBean1,1,$personOid1,$orgOid1,$o_sealId,1)}
      - signfield2: ${gen_signfield_data_V2(1,0,$fileId1,$flowId8,$posBean2,1,$personOid1,$orgOid1,$o_sealId,1)}
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId3: content.data.signfieldIds.0
      - signfieldId4: content.data.signfieldIds.1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId8
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 批量获取签署文档和签署区
    variables:
      - flowIds: [$flowId7,$flowId8]
      - accountId: $personOid1
    api: api/signflow/signfields/batch_docs_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 批量执行流程签署区
    variables:
      - flowId: $flowId8
      - signfieldIds: [$signfieldId3,$signfieldId4]
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]
      - eq: ["content.data.executeResults.1.failedReason",null]
      - eq: ["status_code", 200]

- test:
    name: 查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId8
      - signfieldIds:
      - accountId: $personOid1
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 批量执行流程签署区
    variables:
      - flowId: $flowId8
      - signfieldIds: [$signfieldId3,$signfieldId4]
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",签署失败：当前流程不是“签署中”的状态。]
      - eq: ["status_code", 200]

- test:
    name: 查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId8
      - signfieldIds:
      - accountId:
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId8
      - accountId:
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]
      - eq: ["status_code", 200]

- test:
    name: 查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId8
      - accountId:
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]
      - eq: ["status_code", 200]