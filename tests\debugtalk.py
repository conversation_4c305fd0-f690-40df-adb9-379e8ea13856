import base64
import configparser
import datetime
import hashlib
import json
import os
import random
import re
import string
import time

# import jsonnow_time
import requests

from tools.ElasticSearch import ElasticSearchFactory
from tools.单流程30个主体 import getSignUrl



try:
    import redis
except Exception as e:
    print("开始执行安装redis==4.2.2 任务")
    val = os.system('pip install redis==4.2.2 -i https://mirrors.aliyun.com/pypi/simple/')
    import redis  # 二次引入

# is_simple_on为true表示不用传密钥？
# account_url 用户中心 创建用户账号的
# account_url_proxy

env = os.environ['env']
is_simple_on = os.environ['is_simple_on']
account_url = os.environ['account_url']
account_url_proxy = os.environ['account_url_proxy']
code1 = os.environ['code1']
footstone_doc_url = os.environ['footstone_doc_url']
footstone_user_url = os.environ['footstone_user_url']
x_group = os.environ['envCode']
oss_file_cache = f'oss_file_{env}.ini'
oss_file_cache_config = configparser.ConfigParser()
oss_file_cache_config.read(oss_file_cache, encoding='utf-8')
bzq_app_id = os.environ['BZQ-App-Id']

def getSign():
    getSignUrl(签署类型=1,是否注销证书=False,是否项目环境=x_group,appid版本=1)
    getSignUrl(签署类型=1,是否注销证书=True,是否项目环境=x_group,appid版本=1)
    getSignUrl(签署类型=1, 是否注销证书=False, 是否项目环境=x_group, appid版本=3)

def redis_val(flowId):
    # configparser = {"host": "r-bp17fa8af56d5ca4.redis.rds.aliyuncs.com",

    configparser = {"host": "r-bp15gbqovak5u050p4.redis.rds.aliyuncs.com",
                    "password": "timevale#123"

                    }
    r = redis.Redis(**configparser)
    # r.set("123","123213")
    key = "flowManager:flowBaseInfoCacheKey:" + flowId
    val = r.get(key)
    print("开始再redis里查询执行key")
    if val:
        val = val.decode("utf8")
        val = json.loads(val)
        status = val.get("status")
        return status
    else:
        return val


def check_es(process_id):
    process_id = str(process_id)
    global env
    temp = env
    if temp != 'pro' and temp != 'moni':
        temp = 'test'
    elif temp == 'pro':
        temp = 'open'
    elif temp == 'moni':
        temp = 'sml'
    es = ElasticSearchFactory().build(temp.upper())
    return es.process_id_exist(process_id)


def wait_till_finish(process_id, status=8):
    process_id = str(process_id)
    global env
    temp = env
    if temp != 'pro' and temp != 'moni':
        temp = 'test'
    elif temp == 'pro':
        temp = 'open'
    elif temp == 'moni':
        temp = 'sml'
    es = ElasticSearchFactory().build(temp.upper())
    return es.wait_till_finish(process_id, status)


def get_headers(app_id, operator_id=None, client_id=None, tenant_id=None, language=None):
    headers = {"Content-Type": "application/json", "X-Tsign-Open-App-Id": app_id, "X-Tsign-Open-Auth-Mode": "simple",
               "X-Tsign-Service-Group": x_group,"x-tsign-gray-tag": x_group, "filter-result": "false"}
    if operator_id is not None:
        headers["X-Tsign-Open-Operator-Id"] = operator_id
    if client_id is not None:
        headers["X-Tsign-Client-Id"] = client_id
        headers["filter-result"] = "true"
    if tenant_id is not None:
        headers["X-Tsign-Open-Tenant-Id"] = tenant_id
    if language is not None:
        headers["X-Tsign-Open-Language"] = language
    return headers


now_time = datetime.datetime.now()

# 为什么加一个tests？
sep = os.path.sep  # 路径分隔符-
cur_dir = os.path.abspath('.')
# 如果执行路径不是在tests中
if not cur_dir.endswith("tests"):
    cur_dir = cur_dir + sep + "tests" + sep


# 默认数据路径都放在data下
def open_file(local_path):
    file_path = os.path.join(cur_dir, local_path)
    fd = open(file_path, 'rb')
    return fd


# 获取文件的md5值
def get_file_md5_hex(local_path):
    file_path = os.path.join(cur_dir, local_path)
    fd = open(file_path, 'rb')
    m = hashlib.md5()
    while True:
        d = fd.read(8096)
        if not d:
            break
        m.update(d)
    md5 = m.hexdigest()
    return md5


# 获取经过base64转换的md5值, https://yq.aliyun.com/articles/27523?spm=5176.11065265.1996646101.searchclickresult.3b6d4025K8qbp3
def get_file_base64_md5(local_path):
    file_path = os.path.join(cur_dir, local_path)
    fd = open(file_path, 'rb')
    m = hashlib.md5()
    while True:
        d = fd.read(8096)
        if not d:
            break
        m.update(d)
    byte = base64.b64encode(m.digest())
    return bytes.decode(byte, encoding='utf-8')


# 获取文件的大小
def get_file_size(local_path):
    file_path = os.path.join(cur_dir, local_path)
    size = os.path.getsize(file_path)
    return size


# 获取list 长度
def get_list_len(lst):
    size = len(lst)
    return size


# 引入tools的sign_struc_gen.py，再创建同名函数，case脚本调用debugtalk该方法，该方法再调用tools的sign_struc_gen.py中的同名方法
import tools.sign_struc_gen as signStrucGen


# 个人手动签署，支持单页、骑缝、不限位置
def gen_signfield_data_V1(fileId, flowId, posBean, signType, signerAccountId):
    return signStrucGen.gen_signfield_data_V1(fileId, flowId, posBean, signType, signerAccountId)


# 支持指定签约主体，支持手动、自动签署，支持单页、骑缝、不限位置
def gen_signfield_data_V2(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType):
    return signStrucGen.gen_signfield_data_V2(actorIndentityType, autoExecute, fileId, flowId, posBean, signType,
                                              signerAccountId, authorizedAccountId, sealId, sealType)


# 支持指定签约主体，支持手动、自动签署，支持单页、骑缝、不限位置,添加签名域顺序参数order
def gen_signfield_data_V3(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType, order):
    return signStrucGen.gen_signfield_data_V3(actorIndentityType, autoExecute, fileId, flowId, posBean, signType,
                                              signerAccountId, authorizedAccountId, sealId, sealType, order)


# 支持指定签约主体，支持手动、自动签署，支持单页、骑缝、不限位置,添加签名域数量num
def gen_signfield_data_V4(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType, num):
    return signStrucGen.gen_signfield_data_V4(actorIndentityType, autoExecute, fileId, flowId, posBean, signType,
                                              signerAccountId, authorizedAccountId, sealId, sealType, num)


# 根据V2为基础修改，方便对一些字段的增删
def gen_signfield_data_V5(actorIndentityType, autoExecute, fileId, flowId, posBean, signType, signerAccountId,
                          authorizedAccountId, sealId, sealType, **kwargs):
    return signStrucGen.gen_signfield_data_V5(actorIndentityType, autoExecute, fileId, flowId, posBean, signType,
                                              signerAccountId, authorizedAccountId, sealId, sealType, **kwargs)


def gen_signfield_update_data(authorizedAccountId, extendFieldDatas, posBean, sealFileKey, sealId, signfieldId):
    return signStrucGen.gen_signfield_update_data(authorizedAccountId, extendFieldDatas, posBean, sealFileKey, sealId,
                                                  signfieldId)


def dynamic_func_demo(var):
    import tests.tools.dynamic_func as imported
    return imported.fun2(var)


import tools.cardGen as cardGen


# 获取随机18位身份证号
def get_idNo():
    return cardGen.get_idNo()


# 获取随机组织机构代码
def get_orgCode():
    return cardGen.get_orgCode()


# 获取随机数
def get_randomNo():
    return random.randint(100000, 999999)


def get_randomNo_16():
    return random.randint(****************, ****************)


def get_randomNo_32():
    return random.randint(****************0000000000000000, ********************************)


def random_str(slen=32):
    seed = "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    sa = []
    for i in range(slen):
        sa.append(random.choice(seed))
    return ''.join(sa)


def random_letter(slen=32):
    seed = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    sa = []
    for i in range(slen):
        sa.append(random.choice(seed))
    return ''.join(sa)


def teardown_hook_sleep_N_secs(response, n_secs):
    """ sleep n seconds after request
    """
    # if response.status_code == 200:
    #     time.sleep(0.1)
    # else:
    #     time.sleep(n_secs)
    time.sleep(n_secs)


# 与hook_sleep_n_secs有什么不同？
# def sleep_N_secs(response, n_secs):
#     """ sleep n seconds after request
#     """
#     # if response.status_code == 200:
#     #     time.sleep(0.1)
#     # else:
#     #     time.sleep(n_secs)
#     time.sleep(n_secs)


def get_timestamp_13(var):
    timestamp = int(round(time.time() * 1000))
    return timestamp + int(var)


# 用来做什么？一次不是只有一个flowid吗
def flowId_collection(flowId):
    flowId_list = []
    flowId_list.append(flowId)
    return flowId_list


def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)


# 断言 - 看返回结果是否包含某个value
def containItems(results, value, key):
    contain = False;
    for i in range(len(results)):
        if results[i][key] == value:
            contain = True
    return contain


# 断言 - 看返回结果是否包含某个key
def isContainKey(results, key):
    tmp = results
    ret = False
    for k, v in tmp.items():
        if k == key:
            ret = True
            break
        else:
            if type(v) == type(results):
                ret = isContainKey(v, key)
                if ret:
                    ret = True
                    break
            else:
                continue
    return ret


def isContainKeyForApi(results, key):
    if is_simple_on == "false":
        key = True
    tmp = results
    ret = False
    for k, v in tmp.items():
        if k == key:
            ret = True
            break
        else:
            if type(v) == type(results):
                ret = isContainKey(v, key)
                if ret:
                    ret = True
                    break
            else:
                continue
    return ret


# 断言判定
def results_isExpected(resulets, conditionKey, conditionValue, expectedKey, expectedValue):
    contain = False
    for expectedDic in reversed(resulets):
        if expectedDic[conditionKey] == conditionValue:
            if expectedDic[expectedKey] == expectedValue:
                contain = True
    return contain


def getOid(mobile):
    url = account_url_proxy + "/v1/accounts/checkMobileEmail"
    jData = {"principal": mobile}
    r = requests.post(url, json=jData, headers=get_headers(bzq_app_id))
    result = json.loads(r.text)
    oid = result["data"]["accountId"]
    return oid


def getOrgOid(oid):
    url = account_url_proxy + "/v1/accounts/" + oid + "/mainOrg"
    r = requests.get(url, headers=get_headers(bzq_app_id))
    result = json.loads(r.text)
    organId = result["data"]["organId"]
    return organId


def createOid(thirdPartyUserId, name):
    url = account_url_proxy + "/v1/accounts/createByThirdPartyUserId"
    jData = {"thirdPartyUserId": thirdPartyUserId,
             "name": name}
    r = requests.post(url, json=jData, headers=get_headers(bzq_app_id))
    result = json.loads(r.text)
    oid = result["data"]["accountId"]
    return oid


def get_randomString():
    salt = ''.join(random.sample(string.ascii_letters + string.digits, 20))
    return salt


def get_timestamp(delay=0):
    import time
    return str(int((time.time() + delay * 24 * 60 * 60) * 1000))


# 在指定appid下创建个人oid
def getOid_appid(app_id, mobile):
    url = account_url_proxy + "/v1/accounts/checkMobileEmail"
    jData = {"principal": mobile}
    headers = get_headers(app_id)
    r = requests.post(headers=headers, url=url, json=jData)
    result = json.loads(r.text)
    oid = result["data"]["accountId"]
    return oid


def teardown_compare_1D(response=None):
    if json.loads(response.text)['data']['endTime'] - json.loads(response.text)['data']['startTime'] == ********:
        response.result = True
    else:
        response.result = False


def teardown_compare_1Y(response=None):
    if json.loads(response.text)['data']['endTime'] - json.loads(response.text)['data']['startTime'] == ***********:
        response.result = True
    else:
        response.result = False


def teardown_re_find(response=None):
    batchSignUrl = json.loads(response.text)['data']['batchSignUrl']
    batchSerialId = re.findall("batchSerialId=(.*)&tsign_source_type", batchSignUrl)[0]
    response.batchSerialId = batchSerialId


def teardown_seals_chaoshi(response=None):
    if response:
        seals = json.loads(response.text)['data']['seals']
        org_sealIds = []
        legal_sealIds = []
        invaild_sealIds = []
        for i in seals:
            if i['sealType'] == 1:
                org_sealIds.append(i['sealId'])
            elif i['sealType'] == 2:
                legal_sealIds.append(i['sealId'])
            else:
                invaild_sealIds.append(i['sealId'])
        list11 = [org_sealIds[0], org_sealIds[1]]
        list22 = [org_sealIds[1], org_sealIds[2]]
        list33 = [org_sealIds[0], org_sealIds[1], org_sealIds[2]]
        list44 = [org_sealIds[0], org_sealIds[1], org_sealIds[2], org_sealIds[3]]
        list55 = [org_sealIds[0], org_sealIds[3]]
        sealsId1 = org_sealIds[0]
        sealsId4 = org_sealIds[3]
        sealsId1_list = [org_sealIds[0]]
        response.list11 = list11
        response.list22 = list22
        response.list33 = list33
        response.list44 = list44
        response.list55 = list55
        response.org_sealIds = org_sealIds
        response.sealsId1 = sealsId1
        response.sealsId4 = sealsId4
        response.sealsId1_list = sealsId1_list


def teardown_giveEvidence(response=None):
    if response:
        configList = json.loads(response.text)['data']['pageConfig']['configList']
        giveEvidence = False
        if configList != None and len(configList) > 0:
            for i in configList:
                if i['name'] == '出证':
                    giveEvidence = True
        response.giveEvidence = giveEvidence


def teardown_download(response=None):
    if response:
        configList = json.loads(response.text)['data']['pageConfig']['configList']
        download = False
        for i in configList:
            if i['name'] == '下载':
                download = True
        response.download = download


def teardown_seals(response=None):
    if response:
        seals = json.loads(response.text)['data']['seals']
        org_sealIds = []
        legal_sealIds = []
        invaild_sealIds = []
        cancellations = []
        for i in seals:
            if i['sealType'] == 1:
                org_sealIds.append(i['sealId'])
            elif i['sealType'] == 2:
                legal_sealIds.append(i['sealId'])
            elif i['sealType'] == 6:
                cancellations.append(i['sealId'])
            else:
                invaild_sealIds.append(i['sealId'])
        response.org_sealId1 = org_sealIds[0]
        response.org_sealId2 = org_sealIds[1]
        list1 = []
        list1.append(org_sealIds[0])
        list1.append(org_sealIds[1])
        list2 = []
        list2.append(org_sealIds[1])
        list2.append(org_sealIds[2])
        list3 = []
        list3.append(org_sealIds[0])
        list3.append(org_sealIds[1])
        list3.append(org_sealIds[2])
        response.list1 = list1
        response.list2 = list2
        response.list3 = list3
        leg_org = list1 + legal_sealIds
        response.leg_org = leg_org
        response.org_sealId3 = org_sealIds[2]
        response.org_sealId4 = org_sealIds[3]
        response.org_all = org_sealIds + legal_sealIds
        response.legal_sealId1 = legal_sealIds[0]
        response.cancellation = cancellations[0]


# 返回所有的印章列表
def teardown_seals_total(response=None, org_all=None):
    if response:
        organSeals = json.loads(response.text)['data']['officialSeals'][0]['organSeals']
        legalSeals = json.loads(response.text)['data']['officialSeals'][0]['legalSeals']
        list1 = []
        for i in organSeals:
            list1.append(i['sealId'])
        for j in legalSeals:
            list1.append(j['sealId'])
        check_list = [x for x in org_all if x not in list1]
        if len(check_list) == 0:
            response.isPass = True
        else:
            response.isPass = False


# 返回企业印章列表
def teardown_seals_list(response=None, list1=None):
    if response:
        organSeals = json.loads(response.text)['data']['officialSeals'][0]['organSeals']
        list2 = []
        for i in organSeals:
            list2.append(i['sealId'])
        check_list = [x for x in list2 if x not in list1]
        if len(check_list) == 0:
            response.isPass = True
        else:
            response.isPass = False


# 返回企业+法人印章列表
def teardown_seals_list_leg(response=None, list1=None):
    if response:
        organSeals = json.loads(response.text)['data']['officialSeals'][0]['organSeals']
        legalSeals = json.loads(response.text)['data']['officialSeals'][0]['legalSeals']
        list2 = []
        for i in organSeals:
            list2.append(i['sealId'])
        for i in legalSeals:
            list2.append(i['sealId'])
        check_list = [x for x in list1 if x not in list2]
        if len(check_list) == 0:
            response.isPass = True
        else:
            response.isPass = False


# 返回两个企业印章列表
def teardown_seals_list_two(response=None, list1=None, list11=None):
    if response:
        organizationName = json.loads(response.text)['data']['officialSeals'][0]['organizationName']
        if organizationName == '泗县深泉纯净水有限公司':
            organSeals_sixian = json.loads(response.text)['data']['officialSeals'][0]['organSeals']
        else:
            organSeals_chaoshi = json.loads(response.text)['data']['officialSeals'][0]['organSeals']
        organizationName = json.loads(response.text)['data']['officialSeals'][1]['organizationName']
        if organizationName == '泗县深泉纯净水有限公司':
            organSeals_sixian = json.loads(response.text)['data']['officialSeals'][1]['organSeals']
        else:
            organSeals_chaoshi = json.loads(response.text)['data']['officialSeals'][1]['organSeals']
        org1_list = []
        org2_list = []
        for i in organSeals_sixian:
            org1_list.append(i['sealId'])
        for i in organSeals_chaoshi:
            org2_list.append(i['sealId'])
        check_list1 = [x for x in list1 if x not in org1_list]
        check_list2 = [x for x in list11 if x not in org2_list]
        if len(check_list1) == 0 and len(check_list2) == 0:
            response.isPass = True
        else:
            response.isPass = False


local_path_files = os.path.join('data', "fild_ids.json")
file_path_files = os.path.join(cur_dir, local_path_files)
f = open(file_path_files, "r+", encoding="utf8")
print("开始打开fileids的文件")
content = f.read()
get_file_id_list = json.loads(content)


def get_file_id(app_id: string, file_name: string):
    print("查找开始执行")
    key_list = str(app_id) + file_name
    file_id = get_file_id_list.get(key_list)
    if file_id:
        print("找到文件 使用缓存")
        return file_id
    else:
        print("未找到文件  走上传流程")
        file_name = os.path.basename(file_name)
        if not file_id:
            file_id, upload_result = upload_file_to_oss(app_id, file_name)
            upload_success = True if upload_result['errCode'] == 0 else False
            if upload_success:
                get_file_id_list[key_list] = file_id
                aa = json.dumps(get_file_id_list, ensure_ascii=False)
                f = open(file_path_files, "w+", encoding="utf8")
                f.write(aa)
                f.close()
                print(key_list)
                print("写入完成")

        return file_id


def upload_file_to_oss(app_id: string, file_name: string, content_type="application/pdf"):
    file_name = os.path.basename(file_name)
    file_path = os.path.join('data', file_name)
    file_md5 = get_file_base64_md5(file_path)
    file_size = get_file_size(file_path)

    url = footstone_doc_url + "/v1/files/getUploadUrl"
    headers = get_headers(app_id, None, "pass-test", None)
    body = {
        "contentMd5": file_md5,
        "contentType": content_type,
        "fileName": file_name,
        "fileSize": file_size,
        "accountId": ""
    }
    r = requests.post(url, json=body, headers=headers)
    result = r.json()
    uploadUrl = result['data']['uploadUrl']
    fileId = result['data']['fileId']

    file_abs_path = os.path.join(cur_dir, file_path)
    upload_headers = {
        "Content-MD5": file_md5,
        "Content-Type": content_type
    }
    upload_result = requests.put(uploadUrl, data=open(file_abs_path, 'rb'), headers=upload_headers)
    return fileId, upload_result.json()


def toString(content):
    return str(content)


def strToInt():
    return int(code1)


# 全部参数转为字符串，包含则返回True
def contain_keys(data, key):
    temp = str(data)
    if str(key) in temp:
        return True
    return False


# def substring(temp, index1, index2):
#     return temp[temp.index(index1) + len(index1):temp.index(index2)]

def substring(temp, index1, index2):
    return temp[temp.index(index1) + len(index1):temp.index(index2)]


##生成n个fileId
def get_fileId(appid, file_path, n):
    file_list = []
    url = footstone_user_url + "/v1/files/getUploadUrl"
    content_md5 = get_file_base64_md5(file_path)

    headers = get_headers(appid)
    json_data = {"contentMd5": content_md5, "contentType": "application/pdf", "fileName": "个人借贷合同.pdf",
                 "fileSize": 2542}

    for i in range(n):
        res = requests.post(url=url, headers=headers, json=json_data)
        file_id = json.loads(res.text)["data"]["fileId"]
        upload_file_to_oss(appid, file_path)

        file_list.append(file_id)
    return file_list


# 生成n个固定格式的fileId，用于添加到签署流程中
def get_used_doc(appid, file_path, n):
    print("获取docs")
    docs = []
    for i in range(n):
        res = upload_file_to_oss(appid, file_path)
        doc = {"encryption": 0, "fileId": res[0], "fileName": "个人借贷合同.pdf", "source": 0}
        docs.append(doc)

    return docs


def isContainFlowId(signFlowInfos=None, flowId=None):
    ret = False
    if signFlowInfos:
        flowId_list = []
        for i in signFlowInfos:
            flowId_list.append(i["signFlowId"])
        if flowId in flowId_list:
            ret = True
        else:
            ret = False
    return ret


# 获取已授权的企业章
def get_authorized_org_seal_id(seals):
    for seal in seals.get('officialSeals')[0].get('organSeals'):
        if seal.get('sealUserFlag') is True:
            return seal.get('sealId')
    return None


# 根据关键字和长度截取数据
def get_value_by_keyword(context, keyword, length):
    index_from = context.index(keyword) + len(keyword)
    index_to = index_from + length
    return context[index_from:index_to]


# #判断参数是否为空
# def CertIsNull(response):
#     if json.loads(response)['certId'] is None:
#         return True
#     else:
#         return False

# import json
# def getCertId(no,flowNodeFieldBeanList):
#     """
#     返回证书id
#     """
#     extendBean =flowNodeFieldBeanList[no]
#     # extendBean =flowNodeFieldBeanList[no]['flowNodeFieldBean']['extendFieldBean']['extendField']['PAAS']
#     print(extendBean)
#
#     j=json.loads(extendBean)
#     return j.get("certId")
# for flowNodeFieldBean in flowNodeFieldBeanList:
#     extendBean = flowNodeFieldBean['extendFieldBean']['extendField']['PAAS']
#     print(extendBean)
#     j=json.loads(extendBean)
#     return j.get("certId")

# getCertId(1,[{'gmtCreate': 1613814941226, 'gmtModified': 1613814941658, 'finishDatetime': *************, 'sealId': 'c060f155-f4ca-4488-9af6-327dad59d33a', 'docId': '8693cb9e9f29476a82d60f255599b09d', 'posbean': {'posPage': '1', 'key': None, 'posX': 110.0, 'posY': 110.0, 'width': 150.0, 'qrcodeSign': False, 'addSignTime': False, 'signDateBean': None, 'signTimeFormat': None, 'signDateBeanType': 0}, 'certType': '', 'certId': '', 'sealType': '', 'signType': '1', 'flowId': '6dc1c9d1ae50469c91089f8c02504abe', 'sealFilekey': '', 'templateType': '', 'nodeFieldId': 'd784e397d3964831a7787beefbd57497', 'statusDescription': '', 'taskExecutorAccoutIdList': [{'accountType': 0, 'accountId': 'cfead67e683a47ae944691cbd75c416d'}, {'accountType': 1, 'accountId': '813a0345c2d24ce29d6a6c096e2a0220'}, {'accountType': 2, 'accountId': 'b13815d9431d4437938a7ce75bbf894d'}], 'taskSubjectAccountIdList': [{'accountType': 0, 'accountId': 'cfead67e683a47ae944691cbd75c416d'}, {'accountType': 1, 'accountId': '813a0345c2d24ce29d6a6c096e2a0220'}, {'accountType': 2, 'accountId': 'b13815d9431d4437938a7ce75bbf894d'}], 'taskOperatorAccountIdList': None, 'taskOperatorAuthorizerIdList': None, 'taskTransferAccountIdList': None, 'status': 1, 'nodeFieldType': 0, 'rank': 1, 'extendFieldBean': {'extendField': {'PAAS': '{"actorIndentityType":2,"allowedUploadAttachment":null,"approvalInfos":null,"approvalNoticeUrl":null,"approvalSerialId":null,"assignedPosbean":true,"assignedSeal":true,"autoExecute":true,"autoExecuteType":1,"executeFailedReason":null,"externalFieldDatas":{"EXECUTOR_NAME":"esigntest北京科兴中维生物技术有限公司（印章平台推送企业）","AUTHORIZED_ORGAN_NAME":"esigntest北京科兴中维生物技术有限公司（印章平台推送企业）"},"finishedCallback":false,"handDrawnWay":null,"hidden":false,"mobileShieldTaskId":null,"mobileShieldTaskInfo":null,"noticeUrl":null,"rushsignTime":null,"sealApplyApprovalFlowId":null,"sealIds":null,"signFinishNotice":false,"signStartNotice":null,"signfieldCustomStatus":1,"synchExecute":false,"thirdOrderNo":"777","transfer":false,"transferAccountIds":null,"transferSubjectAccountIds":null}'}}, 'version': 1}, {'gmtCreate': *************, 'gmtModified': *************, 'finishDatetime': *************, 'sealId': '********-9ce4-4153-b8f5-30c45d3f3af4', 'docId': '8693cb9e9f29476a82d60f255599b09d', 'posbean': {'posPage': '1', 'key': None, 'posX': 110.0, 'posY': 210.0, 'width': 150.0, 'qrcodeSign': False, 'addSignTime': False, 'signDateBean': None, 'signTimeFormat': None, 'signDateBeanType': 0}, 'certType': '', 'certId': '', 'sealType': '', 'signType': '1', 'flowId': '6dc1c9d1ae50469c91089f8c02504abe', 'sealFilekey': '', 'templateType': '', 'nodeFieldId': 'd12843ff8fa34d618259fa5b7c7de25d', 'statusDescription': '', 'taskExecutorAccoutIdList': [{'accountType': 0, 'accountId': 'cfead67e683a47ae944691cbd75c416d'}, {'accountType': 1, 'accountId': '813a0345c2d24ce29d6a6c096e2a0220'}, {'accountType': 2, 'accountId': 'b13815d9431d4437938a7ce75bbf894d'}], 'taskSubjectAccountIdList': [{'accountType': 0, 'accountId': 'cfead67e683a47ae944691cbd75c416d'}, {'accountType': 1, 'accountId': '813a0345c2d24ce29d6a6c096e2a0220'}, {'accountType': 2, 'accountId': 'b13815d9431d4437938a7ce75bbf894d'}], 'taskOperatorAccountIdList': None, 'taskOperatorAuthorizerIdList': None, 'taskTransferAccountIdList': None, 'status': 1, 'nodeFieldType': 0, 'rank': 1, 'extendFieldBean': {'extendField': {'PAAS': '{"actorIndentityType":2,"allowedUploadAttachment":null,"approvalInfos":null,"approvalNoticeUrl":null,"approvalSerialId":null,"assignedPosbean":true,"assignedSeal":true,"autoExecute":true,"autoExecuteType":1,"executeFailedReason":null,"externalFieldDatas":{"EXECUTOR_NAME":"esigntest北京科兴中维生物技术有限公司（印章平台推送企业）","AUTHORIZED_ORGAN_NAME":"esigntest北京科兴中维生物技术有限公司（印章平台推送企业）"},"finishedCallback":false,"handDrawnWay":null,"hidden":false,"mobileShieldTaskId":null,"mobileShieldTaskInfo":null,"noticeUrl":null,"rushsignTime":null,"sealApplyApprovalFlowId":null,"sealIds":null,"signFinishNotice":false,"signStartNotice":null,"signfieldCustomStatus":1,"synchExecute":false,"thirdOrderNo":"777","transfer":false,"transferAccountIds":null,"transferSubjectAccountIds":null}'}}, 'version': 1}])


if __name__ == '__main__':
    get_file_id(**********, '劳动合同书.pdf')
