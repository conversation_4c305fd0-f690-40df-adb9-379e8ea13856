- config:
    name: "悟空轩辕整合"
    base_url: ${ENV(base_url)}
    variables:
      - xuanyuan_sixian: ${ENV(mobile_shield_wukong_appid_oid)}
      - xuanyuan_sixian_name: ${ENV(xuanyuan_sixian_name)}
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - fileId: ${ENV(fileId)}
      - oid_wang_tsign: ${ENV(oid_wang_tsign)}
      - phone_wang: ${ENV(phone_wang)}
      - mail_wang: ${ENV(mail_wang)}
      - none_tsign_account_id: ${ENV(none_tsign_account_id)}
      - accountId_withRealName_withoutAuth: ${ENV(accountId_withRealName_withoutAuth)}

- test:
    name: 分布发起流程
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $oid_wang_tsign
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-签署人传个人oid，签署主体传同一个oid
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": "0",signerAccountId: "$oid_wang_tsign","authorizedAccountId": $oid_wang_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 查詢签署区-流程来源appid与当前appid一致
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId: content.data.signfields.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: [content.data.signfields.0.signfieldId,0]

- test:
    name: 查詢签署区-流程来源appid与当前appid不一致-白名单appid可以查询
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_wiliness)}
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: [content.data.signfields.0.signfieldId,0]

- test:
    name: 查詢签署区-流程来源appid与当前appid不一致-非白名单appid无法查询
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS)}
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: [content.message, 当前项目下，签署流程不存在]

- test:
    name: 删除签署区-流程来源appid与当前appid不一致-非白名单appid无法删除
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS)}
      - signfieldId: $signfieldId
    api: api/signflow/signfields/signfieldsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: [content.message, 当前项目下，签署流程不存在]

- test:
    name: 未删除，依然可以查询
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: [content.message, 成功]
      - len_gt: [content.data.signfields.0.signfieldId,0]

- test:
    name: 删除签署区-流程来源appid与当前appid-删除成功
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - signfieldId: $signfieldId
    api: api/signflow/signfields/signfieldsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: [content.message, 成功]

- test:
    name: 删除后无数据
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: [content.message, 成功]
      - eq: [content.data.signfields, []]