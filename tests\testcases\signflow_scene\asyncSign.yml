- config:
    name: 异步签署
    base_url: ${ENV(base_url)}
    variables:
      - tPUserId: autotest1212${get_randomNo()}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf: data/个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}

- test:
    name: 使用第三方userId 创建账号
    variables:
      - json:
          {
            "name": 梁贤红,
            "thirdPartyUserId": $tPUserId,
            "idNumber":"331082199211223080",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********"
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - personOid1: content.data.accountId
    output:
      - $personOid1

- test:
    name: 一步发起
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $personOid1,authorizedAccountId: $personOid1}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId11: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]

- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - accountId: $personOid1
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: 查询personOid1的印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - sealId_p1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]



- test:
    name: 添加_更新_执行签署区
    variables:
      - app_id: $app_Id1
      - accountId: $personOid1
      - flowId: $flowId11
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: ''
      - approvalPolicy:
      - dingUser:
      - operator_id: $personOid1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    extract:
      - serviceId: content.data.serviceId
    validate:
      - eq: [status_code, 200]

- test:
    name: 查询异步签署结果
    api: api/signflow/signflows/requsetResult.yml
    variables:
      - app_id: $app_Id1
      - service_Id: $serviceId
    validate:
      - eq: [status_code, 200]


- test:
    name: 查询异步签署结果
    api: api/signflow/signflows/requsetResult.yml
    variables:
      - app_id: $app_Id1
      - service_Id: $serviceId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

