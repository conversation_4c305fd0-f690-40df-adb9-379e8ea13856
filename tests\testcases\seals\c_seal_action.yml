- config:
    name: "企业印章相关的各种操作"
    ###def: c_seal_action($idNo,$idType,$person,$name,$orgType, $orgCode,$legalName,$legalIdNo,$legalIdType,$color,$height,$width,$sealAlias,$templateType,$text,$hText,$qText,$c_seal_path,$c_fileName)
    variables:
      - fileSize: ${get_file_size($p_seal_path)}
    request:
      "base_url": "${ENV(footstone_api_url)}"

- test:
    name: "创建一个新账号"
    api: api/user/accounts/create_accounts.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建一个新组织"
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]
    output:
      - $orgId

- test:
    name: "创建企业模版印章"
    api: api/seals/seals/create_c_template_seal_appid.yml
    variables:
      - templateType: "TEMPLATE_ROUND"
    extract:
      - sealId: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: "查询企业印章列表"
    api: api/seals/seals/select_c_seals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.total",1]


- test:
    name: 获取上传文件url
    api: api/file_template/files/get_uploadUrl.yml
    variables:
      - fileSize: ${get_file_size($c_seal_path)}
      - contentMd5: ${get_file_base64_md5($c_seal_path)}
      - contentType: "image/jpeg"
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 上传文件到oss
    variable_binds:
      - binary: ${open_file($c_seal_path)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Md5: ${get_file_base64_md5($c_seal_path)}
        Content-Type: "image/jpeg"
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: "创建企业图片印章"
    api: api/seals/seals/create_c_image_seal.yml
    variables:
      - sealAlias: "贺达的图片印章"
      - sealType: 3
      - type: FILEKEY
    extract:
      - sealId: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: "查询企业印章列表"
    api: api/seals/seals/select_c_seals.yml
    extract:
      - sealId1: content.data.seals.0.sealId
      - sealId2: content.data.seals.1.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.total",2]

- test:
    name: "清除企业印章"
    api: api/seals/seals/delete_c_seal.yml
    parameters:
      - sealId:
      - $sealId1
      - $sealId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "注销上面创建的组织"
    api: api/user/organizations/delete_organizations.yml

- test:
    name: "注销上面创建的个人账号"
    api: api/user/accounts/delete_accounts.yml