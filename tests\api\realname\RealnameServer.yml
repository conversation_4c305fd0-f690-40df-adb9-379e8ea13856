- api:   #企业信息校验
    def: orgInfoAuth($com_name,$codeORG,$codeREG,$codeUSC,$legalName)
    request:
      url: $realname_organization_url/orgInfoAuth/model
      method: POST
      json:
        name: $com_name
        codeORG: $codeORG
        codeREG: $codeREG
        codeUSC: $codeUSC
        legalName: $legalName

- api:   #银行四要素
    def: bankFour($name,$idno,$mobile,$cardno)
    request:
      url: ${ENV(realname_url)}/bankFourWithAuthCode/model
      method: POST
      json:
        name: $name
        idno: $idno
        mobile: $mobile
        cardno: $cardno

- api:   #短信验证码
    def: validateAuthCode($serviceId,$authcode)
    request:
      url: /validateAuthCode/model
      method: POST
      json:
        serviceId: $serviceId
        authcode: $authcode

- api:   #获取刷脸URL
    def: faceAuthByUrl($name,$idno,$from,$faceAuthMode,$callbackUrl)
    request:
      url: /faceAuthByUrl/model
      method: POST
      json:
        name: $name
        idno: $idno
        from: $from
        faceAuthMode: $faceAuthMode
        callbackUrl: $callbackUrl

- api:   #查询刷脸认证结果
    def: queryFaceAuthByUrlResult($serviceId)
    request:
      url: /queryFaceAuthByUrlResult/model
      method: POST
      json:
        serviceId: $serviceId
        needFile: true

- api:   #企业打款
    def: toPayOrg($serviceId,$com_name,$bank,$cardno_org)
    request:
      url: $realname_organization_url/toPayOrg/model
      method: POST
      json:
        serviceId: $serviceId
        name: $com_name
        bank: $bank
        cardno: $cardno_org
        subbranch: 平安银行杭州高新支行
        provice: 浙江省
        city: 杭州市
        notifyUrl: http://172.20.62.10:8080/testnotify/msgRecive

- api:   #校验金额
    def: payAuth($serviceId,$cash)
    request:
      url: $realname_organization_url/payAuth/model
      method: POST
      json:
        serviceId: $serviceId
        cash: $cash

- api:   #打款查询
    def: payQuery($serviceId)
    request:
      url: $realname_organization_url/payQuery/model
      method: POST
      json:
        serviceId: $serviceId

- api:   #获取实名认证开放服务地址
    def: getOpenRealnameUrl($oid,$orgOid,$projectId,$callbackUrl,$realNameObject)
    request:
      url: /getOpenRealnameUrl/model
      method: POST
      json:
        oid: $oid
        orgOid: $orgOid
        projectId: $projectId
        redirectUrl: $callbackUrl
        type:
        realNameObject: $realNameObject
        showResultPage: 1

- api:   #运营商三要素
    def: telecomThreeWithAuthCode($name,$idno,$mobile)
    request:
      url: ${ENV(realname_url)}/telecomThreeWithAuthCode/model
      method: POST
      headers:
        Content-Type: application/json
      json:
        name: $name
        idno: $idno
        mobile: $mobile

- api:   #运营商三要素的短信验证码
    def: telecomThreeValidateAuthCode($serviceId,$authcode)
    request:
      url: ${ENV(realname_url)}/telecomThreeValidateAuthCode/model
      method: POST
      json:
        serviceId: $serviceId
        authcode: $authcode

- api:   #发起企业芝麻的URL
    def: orgAuthGetUrl($serviceId,$name,$codeORG,$codeREG,$codeUSC,$legalName,$legalIdno,$schema,$callbackUrl)
    request:
      url: /orgAuthGetUrl/model
      method: POST
      json:
        serviceId: $serviceId
        name: $name
        codeORG: $codeORG
        codeREG: $codeREG
        codeUSC: $codeUSC
        legalName: $legalName
        legalIdno: $legalIdno
        schema: $schema
        callbackUrl: $callbackUrl

- api:   #发起个人人工实名
    def: psnArtificial($name,$idno,$mobile,$cardno)
    request:
      url: /psnArtificial/model
      method: POST
      json:
        name: $name
        idno: $idno
        certType: 1
        certValidityTermBegin: '2018-01-01'
        certValidityTermEnd: '2028-01-01'
        certFront: certFront
        certBack: certBack
        holdCertFront: holdCertFront
        holdCertBack: holdCertBack
        mobile: $mobile
        cardno: $cardno
        dataSource: dataSource
        nationality: 1

- api:   #发起企业人工实名
    def: orgArtificial($name,$codeUSC,$codeREG,$codeORG,legalName,legalIdno,legalMobile,agentMobile)
    request:
      url: /orgArtificial/model
      method: POST
      json:
        name: $name
        codeUSC: $codeUSC
        codeREG: $codeREG
        codeORG: $codeORG
        address: '浙江省杭州市西湖区西斗门路3号天堂软件园D幢19楼'
        openTo: '2022-12-17'
        bizLicensePhoto: 'x'
        orgCodeCertPhoto: 'x'
        taxRegCertPhoto: 'x'
        legalName: $legalName
        legalIdno: $legalIdno
        legalMobile: $legalMobile
        legalCertType: 19
        legalCertFront: 'x'
        legalCertBack: 'x'
        legalCertBegin: "2018-01-01"
        legalCertEnd: "2028-01-01"
        legalNationality: "1"
        legalHoldCertFront: "x"
        legalHoldCertBack: "x"
        agentName: "李俊涛"
        agentIdno: "******************"
        agentCertType: "19"
        agentMobile: $agentMobile
        agentCertFront: "x"
        agentCertBack: "x"
        agentHoldCertFront: "x"
        agentHoldCertBack: "x"
        proxyPhoto: "x"
        operatorType: "x"
        dataSource: "dataSource"

- api:   #保存实名认证事务和单次业务关联关系
    def: saveIdentityTransactionRef($identityTransactionId,$serviceId)
    request:
      url: /saveIdentityTransactionRef/model
      method: POST
      json:
        identityTransactionId: $identityTransactionId
        serviceId: $serviceId

- api:   #查询实名认证事务最近的单次实名业务
    def: queryIdentityTransactionRef($identityTransactionId,$serviceId)
    request:
      url: /queryIdentityTransactionRef/model
      method: POST
      json:
        identityTransactionId: $identityTransactionId
        serviceId: $serviceId

- api:   #重新发起企业核身接口
    def: copyOrgAuth($serviceId)
    request:
      url: /copyOrgAuth/model
      method: POST
      json:
        serviceId: $serviceId

- api:   #取消企业核身认证接口
    def: cancelOrgAuth($serviceId)
    request:
      url: /cancelOrgAuth/model
      method: POST
      json:
        serviceId: $serviceId

- api:   #查询支行列表
    def: getBankNameByCityAndProvince($keyWord)
    request:
      url: /getBankNameByCityAndProvince/model
      method: POST
      json:
        keyWord: $keyWord
        cityName: 杭州
        provinceName: 浙江省

- api:   #查询认证结果
    def: queryAuthResult($serviceId)
    request:
      url: /queryAuthResult/model
      method: POST
      json:
        serviceId: $serviceId

- api:   #查询法人签署状态
    def: queryOrgFrSignAuthResult($serviceId)
    request:
      url: /queryOrgFrSignAuthResult/model
      method: POST
      json:
        serviceId: $serviceId
