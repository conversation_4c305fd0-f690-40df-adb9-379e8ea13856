- config:
    name: 一步发起备注签署区
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_SaaS_with_willing)}
      - account_id_sx: ${ENV(xuanyuan_sixian)}
      - account_id: ${ENV(none_tsign_account_id)}
      - account_id_tsign: ${ENV(account_id1_in_tsign)}
      - group: ${ENV(envCode)}
      - urlType: 2
      - multiSubject: true
      - organizeId: ''
      - compressContext: ''
      - operatorid: ''
      - image_for_sign: ${ENV(image_for_sign)}
      - phone_wang: ${ENV(phone_wang)}
      - name_wang: ${ENV(name_wang)}
      - oid_wang_tsign: ${ENV(oid_wang_tsign)}

- test:
    name: v2一步发起流程，企业签，发起失败
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签约主体只支持个人]

- test:
    name: v2一步发起流程，法人签，发起失败
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":3,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签约主体只支持个人]

- test:
    name: v2一步发起流程，经办人签，发起失败
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":4,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签约主体只支持个人]

- test:
    name: v2一步发起流程，inputType 传1（手绘输入），aiCheck 传0（不开启）
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.flowId, 32]

- test:
    name:  获取批量签署url-inputType为1手绘时，不允许获取批量签链接
    variables:
      - json: {"clientType": "H5","operatorId": $account_id,"redirectUrl": "","signFlowIds": [$flow_id]}
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "成功"] # 升级后V3批量签，可获取批量签链接，进入到不可批量签列表

- test:
    name: 获取签署详情，inputType 为1（手绘输入），aiCheck 为0（不开启）
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 0]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一']

- test:
    name: v2一步发起流程，inputType 传1（手绘输入），aiCheck 传1（开启ai校验）
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":1,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.flowId, 32]

- test:
    name: 获取签署详情，inputType 为1（手绘输入），aiCheck 为1（不开启）
    variables:
      - flowId: $flow_id
      - queryAccountId: ''

    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一']

- test:
    name: v2一步发起流程，inputType 传1（手绘输入），aiCheck 传2（开启ai强校验）
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.flowId, 32]

- test:
    name: 获取签署详情，inputType 为1（手绘输入），aiCheck 为2（不开启）
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 2]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一']

- test:
    name: v2一步发起流程，inputType 传1（手绘输入），aiCheck 传3
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":3,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", ai校验不符合要求]

- test:
    name: v2一步发起流程，inputType为2时，remarkContent 不生效
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":2,"remarkContent":"😊","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - len_eq: ["content.data.flowId", 32]

- test:
    name:  获取批量签署url-inputType为2键盘输入时，允许获取批量签链接
    variables:
      - json: {"clientType": "H5","operatorId": $account_id,"redirectUrl": "","signFlowIds": [$flow_id]}
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "成功"]


- test:
    name: 转交备注签署区-手机号跟姓名不匹配
    variables:
      - json: {"flowId":"$flow_id","originalAccountId":"$account_id","transferToAccount":"$phone_wang","transferToAccountName":"张三"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1439004]
      - eq: ["content.message", 请输入正确的姓名和账号]

- test:
    name: 转交备注签署区-手机号为空
    variables:
      - json: {"flowId":"$flow_id","originalAccountId":"$account_id","transferToAccountName":"$name_wang"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", transferToAccount不能为空]

- test:
    name: 转交备注签署区-姓名为空
    variables:
      - json: {"flowId":"$flow_id","originalAccountId":"$account_id","transferToAccount":"$phone_wang"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", transferToAccountName不能为空]

- test:
    name: 转交备注签署区
    variables:
      - json: {"flowId":"$flow_id","originalAccountId":"$account_id","transferToAccount":"$phone_wang","transferToAccountName":"$name_wang"}
    api: api/v3/remark-signfields/transfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取签署详情，remarkContent 正确
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 2]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 2]
      - str_eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", null]
      - eq: ["content.data.signers.0.signerAccountId", $oid_wang_tsign]
      - eq: ["content.data.signers.0.signerAuthorizedAccountId", $oid_wang_tsign]
      - eq: ["content.data.signers.0.signerAccount", $phone_wang]
      - eq: ["content.data.signers.0.signerName", $name_wang]

- test:
    name: v2一步发起流程，inputType为3时，发起失败
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":3,"remarkContent":"😊","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 输入类型不符合要求]

- test:
    name: v2一步发起流程，inputType为1时，remarkContent 为特殊字符，无法发起
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"😊","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容包含非法文字]

- test:
    name: v2一步发起流程，remarkContent 传入空格
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":" 一二 ","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容包含非法文字]

- test:
    name: v2一步发起流程，remarkContent 传入101个字符
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十1","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容过长]

- test:
    name: v2一步发起流程，remarkContent 传入100个字符
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: [content.data.flowId, 32]

- test:
    name: 获取签署详情，remarkContent 正確
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 2]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十']

- test:
    name: v2一步发起流程，signType 为0，无法发起
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":0,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 签署类型只支持单页签名]

- test:
    name: v2一步发起流程，signType 为2，无法发起
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":2,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 签署类型只支持单页签名]

- test:
    name: v2一步发起流程，signType 为4，无法发起
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":4,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 签署类型只支持单页签名]

- test:
    name: v3一步发起流程，正常发起
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId",32]

- test:
    name: v3一步发起流程，企业签
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"2","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签约主体只支持个人]

- test:
    name: v3一步发起流程，法人签，发起失败
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"3","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签约主体只支持个人]

- test:
    name: v3一步发起流程，经办人签，发起失败
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"4","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签约主体只支持个人]

- test:
    name: v3一步发起流程，inputType 传1（手绘输入），aiCheck 传0（不开启）
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId",32]

- test:
    name: 获取签署详情，inputType 为1（手绘输入），aiCheck 为0（不开启）
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 0]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一']

- test:
    name: v3一步发起流程，inputType 传1（手绘输入），aiCheck 传1（开启）
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":1,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId",32]

- test:
    name: 获取签署详情，inputType 为1（手绘输入），aiCheck 为1（开启）
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一']

- test:
    name: v3一步发起流程，inputType 传1（手绘输入），aiCheck 传2（开启ai强校验）
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId",32]

- test:
    name: 获取签署详情，inputType 为1（手绘输入），aiCheck 为1（开启）
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 2]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一']

- test:
    name: v3一步发起流程，inputType 传1（手绘输入），aiCheck 传3
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":3,"inputType":1,"remarkContent":"一","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", ai校验不符合要求]

- test:
    name: v3一步发起流程，inputType=1时，remarkContent 传入特殊符号，无法发起
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"😊","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容包含非法文字]

- test:
    name: v3一步发起流程，inputType=2时，remarkContent不生效
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":2,"remarkContent":"😊","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - len_eq: ["content.data.flowId", 32]

- test:
    name: 获取签署详情，remarkContent 正确
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 2]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 2]
      - str_eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", null]

- test:
    name: v3一步发起流程，remarkContent 传入空格
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":" 一二 ","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容包含非法文字]

- test:
    name: v3一步发起流程，remarkContent 传入101个字符
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十1","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容过长]

- test:
    name: v3一步发起流程，remarkContent 传入100个字符
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId",32]

- test:
    name: v3一步发起流程，signType 为0，无法发起
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":0,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署类型只支持单页签名]

- test:
    name: v3一步发起流程，signType 为2，无法发起
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":2,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署类型只支持单页签名]

- test:
    name: v3一步发起流程，signType 为4，无法发起
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":4,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437305]
      - contains: ["content.message",签署区坐标信息异常]

- test:
    name: 获取签署详情，remarkContent 正确
    variables:
      - flowId: $flow_id
      - queryAccountId: ''
      - operatorid: ''
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.inputType", 1]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.aiCheck", 2]
      - eq: ["content.data.signDocs.0.signfields.0.remarkFieldConfig.remarkContent", '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十']

- test:
    name: 备注签署获得签署链接-aiCheck为3，无法发起
    variables:
      - aiCheck: 3
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一'
      - remarkFieldHeight: 200
      - remarkFieldWidth: 500
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", ai校验不符合要求]

- test:
    name: 备注签署获得签署链接-inputType为3，无法发起
    variables:
      - aiCheck: 1
      - inputType: 3
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一'
      - remarkFieldHeight: 200
      - remarkFieldWidth: 500
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 输入类型不符合要求]

- test:
    name: 备注签署获得签署链接-remarkFieldHeight为-1，无法发起
    variables:
      - aiCheck: 1
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一'
      - remarkFieldHeight: -1
      - remarkFieldWidth: 500
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 长宽不符合要求]

- test:
    name: 备注签署获得签署链接-remarkFieldWidth为-1，无法发起
    variables:
      - aiCheck: 1
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一'
      - remarkFieldHeight: 500
      - remarkFieldWidth: -1
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 长宽不符合要求]

- test:
    name: 备注签署获得签署配置,remarkFlowId 不存在
    variables:
      - appId: $app_id
      - remarkFlowId: '12345678901234567890123456789012'
    api: api/signflow/remark_sign/v3_remark_config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438702]
      - contains: ["content.message", 当前备注手绘链接已失效，请重试]

- test:
    name: 备注签署上传图片-remarkFlowId不存在
    variables:
      - appId: $app_id
      - fileId: 1
      - remarkFlowId: '11'
    api: api/signflow/remark_sign/v3_remark_images_upload.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438702]
      - contains: ["content.message", 当前备注手绘链接已失效，请重试]

- test:
    name: 备注签署下载图片-remarkFlowId 不存在
    variables:
      - remarkFlowId: 123
    api: api/signflow/remark_sign/v3_remark_images_download_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438703]
      - contains: ["content.message", 当前流程未完成或已过期，请完成备注后重试或者重新发起备注流程]

- test:
    name: 备注签署获得签署链接-成功
    variables:
      - aiCheck: 0
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一'
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    extract:
      - remarkFlowId: content.data.remarkFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - len_eq: ["content.data.remarkFlowId", 32]

- test:
    name: 备注签署获得签署配置
    variables:
      - appId: $app_id
      - remarkFlowId: $remarkFlowId
    api: api/signflow/remark_sign/v3_remark_config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.aiCheck", 0]
      - eq: ["content.data.inputType", 1]
      - str_eq: ["content.data.redirectUrl", None]
      - eq: ["content.data.remarkContent", '一']
      - eq: ["content.data.remarkFieldHeight", 300]
      - eq: ["content.data.remarkFieldWidth", 200]

- test:
    name: 备注签署获得签署配置,appId与remarkFlowId不匹配
    variables:
      - appId: 123456
      - remarkFlowId: $remarkFlowId
    api: api/signflow/remark_sign/v3_remark_config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438702]
      - contains: ["content.message", 当前备注手绘链接已失效，请重试]

- test:
    name: 备注签署获得签署链接-inputType为2，remarkContent不生效
    variables:
      - aiCheck: 0
      - inputType: 2
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一'
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    extract:
      - remarkFlowId: content.data.remarkFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - len_eq: ["content.data.remarkFlowId", 32]

- test:
    name: 备注签署获得签署配置
    variables:
      - appId: $app_id
      - remarkFlowId: $remarkFlowId
    api: api/signflow/remark_sign/v3_remark_config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - str_eq: ["content.data.remarkContent", None]

- test:
    name: 备注签署获得签署链接-inputType为1，remarkContent为100个字符
    variables:
      - aiCheck: 0
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十'
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    extract:
      - remarkFlowId: content.data.remarkFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - len_eq: ["content.data.remarkFlowId", 32]

- test:
    name: 备注签署获得签署配置
    variables:
      - appId: $app_id
      - remarkFlowId: $remarkFlowId
    api: api/signflow/remark_sign/v3_remark_config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.data.remarkContent", '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十']

- test:
    name: 备注签署获得签署链接-inputType为1，remarkContent存在特殊字符，发起失败
    variables:
      - aiCheck: 0
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一二😊三'
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容包含非法文字]

- test:
    name: 备注签署获得签署链接-inputType为1，remarkContent为101个字符，发起失败
    variables:
      - aiCheck: 0
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十1'
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 内容过长]

- test:
    name: 备注签署上传图片-fileId不存在
    variables:
      - appId: $app_id
      - fileId: 1
      - remarkFlowId: $remarkFlowId
    api: api/signflow/remark_sign/v3_remark_images_upload.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435608]

- test:
    name: 备注签署上传图片-正常流程
    variables:
      - appId: $app_id
      - fileId: $image_for_sign
      - remarkFlowId: $remarkFlowId
    api: api/signflow/remark_sign/v3_remark_images_upload.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: 备注签署下载图片-成功
    variables:
      - remarkFlowId: $remarkFlowId
    api: api/signflow/remark_sign/v3_remark_images_download_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: 备注签署获得签署链接-空格开头，无法发起
    variables:
      - aiCheck: 0
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: ' 一二三'
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 首尾不允许有空格]

- test:
    name: 备注签署获得签署链接-空格结尾，无法发起
    variables:
      - aiCheck: 0
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一二三 '
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 首尾不允许有空格]

- test:
    name: 备注签署获得签署链接-中间带空格，可以发起
    variables:
      - aiCheck: 0
      - inputType: 1
      - notifyUrl: ''
      - redirectUrl: ''
      - remarkContent: '一 二  三'
      - remarkFieldHeight: 300
      - remarkFieldWidth: 200
    api: api/signflow/remark_sign/v3_remark_url.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

- test:
    name: v2一步发起流程，不允许输入空格
    variables:
      - json: {"docs":[{"fileId": $file_id,"fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","initiatorAccountId":"$account_id_sx","initiatorAuthorizedAccountId":"$account_id_sx","flowConfigInfo":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","redirectUrl":"","signPlatform":"","willTypes":["CODE_SMS","SIGN_PWD"],"countdown":0,"redirectDelayTime":3}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id","authorizedAccountId":"$account_id","noticeType":""},"signfields":[{"fieldType":2,"assignedPosbean":false,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$file_id","posBean":{"posPage":"1","posX":200,"posY":200},"remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一 二","remarkFieldHeight":200,"remarkFieldWidth":500},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":150,"posY":150},"signType":1,"width":150}],"thirdOrderNo":"11111"}]}
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 内容包含非法文字]

- test:
    name: v3一步发起流程，不允许输入空格
    variables:
      - json: {"docs":[{"fileId":"$file_id","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id_tsign","initiatorAuthorizedAccountId":"$account_id_tsign","remark":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id_tsign","signerAccountId":"$account_id_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一 二","remarkFieldHeight":200,"remarkFieldWidth":500},"fileId":"$file_id","handDrawnWay":"0","posBean":{"posPage":"1","posX":500,"posY":500},"sealType":"0","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 内容包含非法文字]

- test:
    name: 分布发起流程
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id_tsign
      - initiatorAuthorizedAccountId: $account_id_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $file_id, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加备注签署区，带空格无法发起
    variables:
      - flowId: $sign_flowId
      - json: {"signfields":[{"fileId":"$file_id","forceReadTime":0,"signerAccountId":"$account_id_tsign","actorIndentityType":"0","authorizedAccountId":"$account_id_tsign","assignedPosbean":false,"signDateBeanType":"1","fieldType":2,"sealBizTypes":"","remarkFieldConfig":{"aiCheck":0,"inputType":1,"remarkContent":"一 八","remarkFontSize":100,"remarkFieldHeight":500,"remarkFieldWidth":500},"signDateBean":{"posPage":"1","posX":"344","posY":"464","width":300},"handDrawnWay":"0","order":1,"posBean":{"posPage":"2","posX":"444","posY":"225"},"sealType":"0","sealId":"","signType":0}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435002]
      - contains: [content.message, 内容包含非法文字]