- config:
    name: "签署指定证书场景"
    base_url: ${ENV(base_url)}
    variables:
      - id_No: '320721199305232634'
      - id_No2: '362324199301130629'
      - Name: 王瑶济
      - app_id1: ${ENV(appid_cert)}
      - app_Id1: ${ENV(appid_cert)}
      - app_Id2: ${ENV(appid_cert2)}
      - app_id2: ${ENV(appid_cert2)}
      - id_Type: CRED_PSN_CH_IDCARD
      - mobile: 13260881366
      - Name2: 测试
      - Name3: 梁贤红
      - id_No3: 331082199211223080
      - certid_expired: ${ENV(certid_expired)}
      - path_pdf: 个人借贷合同.pdf
      - fileId: ${get_file_id($app_Id1,$path_pdf)}
      - group: ''
      - BZQ: ${ENV(BZQ-App-Id)}


    request:
      base_url: "${ENV(footstone_api_url)}"


- test:
    name: 查询企业
    variables:
      - app_id: $BZQ
      - name: 泗县梁贤红乐福超市
      - realName: ACCEPT
    api: api/user/organizations/get_organizations_by_name_realname.yml
    extract:
      - orgOid: content.data.items.0.ouid

- test:
    name: 创建账号1
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_id1
      - idNo: $id_No
      - mobile: $mobile
      - name: $Name
      - thirdPartyUserId: wyj-320721199305232634
    extract:
      - oid1: content.data.accountId

- test:
    name: "制证1"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId1: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

- test:
    name: "制证2"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name3
      - idType: $id_Type
      - idNumber: $id_No3
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId2: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]


- test:
    name: "制证3"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id2
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId3: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]


- test:
    name: "制证4"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id2
      - name: 泗县梁贤红乐福超市
      - idType: CRED_ORG_USCC
      - idNumber: 92341324MA2NX3TR55
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId4: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]


- test:
    name: 静默授权
    variables:
      - app_id: $app_Id1
      - accountId: $oid1
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


      #- test:
      #    name: 一步发起用户自动签-不指定证书
      #    variables:
      #        - app_id: $app_id1
      #        - doc : {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      #        - attachments: []
      #        - docs: [$doc]
      #        - copiers: []
      #        - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      #        - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      #    api: api/mobile-shield/createFlowOneStep.yml
      #    validate:
#      - eq: ["content.code",1435002]
#       - contains: ["content.message",'参数错误: 指定证书不存在：111']

- test:
    name: 一步发起用户自动签-指定证书id不存在
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: "1111", autoExecute: true, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书不存在：111']
- test:
    name: 一步发起指定证书id用户手动签-证书id不存在
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: "1111", autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书不存在：111']

- test:
    name: 一步发起指定证书id用户自动签-成功
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: $certId1, autoExecute: true, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 一步发起指定证书id用户手动签-成功
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: $certId1, autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 一步发起指定证书id法人签-成功
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $orgOid}, signfields: [ { certId: $certId2, autoExecute: false, actorIndentityType: 3, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 一步发起指定证书id企业签-成功
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $orgOid}, signfields: [ { certId: $certId4, autoExecute: false, actorIndentityType: 2, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']


- test:
    name: 一步发起指定证书id用户手动签-证书id信息不匹配
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: $certId2, autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']

- test:
    name: 一步发起指定证书id法人签-证书id信息不匹配
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $orgOid}, signfields: [ { certId: $certId1, autoExecute: false, actorIndentityType: 3, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']

- test:
    name: 一步发起指定证书id企业签-证书id信息不匹配
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oid1,authorizedAccountId: $orgOid}, signfields: [ { certId: $certId2, autoExecute: false, actorIndentityType: 2, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']

      #- test:
      #    name: 一步发起指定证书id用户手动签-APPid不匹配
      #    variables:
      #        - app_id: $app_id2
      #        - doc : {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      #        - attachments: []
      #        - docs: [$doc]
      #        - copiers: []
      #        - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      #        - signers: [{signOrder: 1,signerAccount: {signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: $certId3, autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      #    api: api/mobile-shield/createFlowOneStep.yml
      #
      #    validate:
#      - eq: ["content.code",1435002]
#       - contains: ["content.message",'参数错误: 指定证书和主体账号所属应用不一致']

- test:
    name: "一步到位创建流程-对内，certid不存在"
    variables:
      - app_id: $app_id1
      - json: {"accountId":$oid1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$oid1,"signerAccountName":"","signerAuthorizerId":$oid1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "111","fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}

    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书不存在：111']

- test:
    name: "一步到位创建流程-对内，certid不匹配"
    variables:
      - app_id: $app_id1
      - json: {"accountId":$oid1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$oid1,"signerAccountName":"","signerAuthorizerId":$oid1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": $certId2,"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}

    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']


- test:
    name: "一步到位创建流程-对内经办人签署，certid不匹配"
    variables:
      - app_id: $app_id1
      - json: {"accountId":$oid1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$oid1,"signerAccountName":"","signerAuthorizerId":$orgOid,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": $certId2,"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"2","thirdOrderNo":""}]}],"recipients":[]}

    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']


      #- test:
      #    name: "一步到位创建流程-对内，appid不匹配"
      #    variables:
      #        - app_id: $app_id2
      #        #        - json: {"accountId":$oid1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$oid1,"signerAccountName":"","signerAuthorizerId":$oid1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": $certId3,"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
      #    api: api/mobile-shield/createAllAtOnce.yml
      #    validate:
#      - eq: ["content.code",1435002]
#       - contains: ["content.message",'参数错误: 指定证书和主体账号所属应用不一致']




- test:
    name: 创建个人模板印章，悟空
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id2
      - accountId: $oid1
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId2: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]



- test:
    name: 创建流程和文档
    variables:
      - app_id: $app_id1
      - autoArchive: false
      - configInfo:
      - contractValidity:
      - extend:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId: $oid1
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - docs_1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_id1
      - flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 批量添加流程签署区-certid不存在
    variables:
      - app_id: $app_id1
      - flowId: $flowId
      - posBean: {}
      - autoExecute: 0
      - signfield1: {certId: '111',actorIndentityType: 0,assignedItem: 0,assignedPosbean: false,assignedSeal: false,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: '',sealType: 1,signType: 0,signerAccountId: $oid1}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书不存在']

- test:
    name: 批量添加流程签署区-certid不匹配
    variables:
      - app_id: $app_id1
      - flowId: $flowId
      - posBean: {}
      - autoExecute: 0
      - signfield1: {certId: $certId2,actorIndentityType: 0,assignedItem: 0,assignedPosbean: false,assignedSeal: false,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: '',sealType: 1,signType: 0,signerAccountId: $oid1}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']

      #- test:
      #    name: 批量添加流程签署区-appid不匹配
      #    variables:
      #      - app_id: $app_id1
      #      - flowId: $flowId
      #      - posBean : {}
      #      - autoExecute: 0
      #      - signfield1: {certId: $certId3,actorIndentityType: 0,assignedItem: 0,assignedPosbean: false,assignedSeal: false,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: '',sealType: 1,signType: 0,signerAccountId: $oid1}
      #      - signfields: [$signfield1]
      #    api: api/signflow/signfields/signfieldsCreate.yml
      #
      #    validate:
#      - eq: ["content.code",1435002]
#       - contains: ["content.message",'参数错误: 指定证书和主体账号所属应用不一致']

- test:
    name: 添加用户手动签署-certid不匹配
    variables:
      - app_id: $app_id1
      - flowId: $flowId
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: {'certId': $certId2,  'fileId':$fileId, 'signerAccountId':$oid1, 'actorIndentityType':'0', 'authorizedAccountId':$oid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':0}
      - signfields: [$signfield_file1]
    api: signfieldsCreate_handSign($flowId, $signfields)
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']
- test:
    name: 添加用户手动签署-certid已过期
    variables:
      - app_id: $app_id1
      - flowId: $flowId
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: {'certId': $certid_expired,  'fileId':$fileId, 'signerAccountId':$oid1, 'actorIndentityType':'0', 'authorizedAccountId':$oid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':0}
      - signfields: [$signfield_file1]
    api: signfieldsCreate_handSign($flowId, $signfields)
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已过期']

      #- test:
      #    name: 添加用户手动签署-appid不匹配
      #    variables:
      #      - app_id: $app_id1
      #      - flowId: $flowId
      #      - posBean: {'posPage':'1','posX':300, 'posY':600}
      #      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      #      - signfield_file1: {'certId': $certId3,  'fileId':$fileId, 'signerAccountId':$oid1, 'actorIndentityType':'0', 'authorizedAccountId':$oid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':0}
      #      - signfields: [$signfield_file1]
      #    api: signfieldsCreate_handSign($flowId, $signfields)
      #    validate:
#      - eq: ["content.code",1435002]
#       - contains: ["content.message",'参数错误: 指定证书和主体账号所属应用不一致']

- test:
    name: 静默授权
    variables:
      - app_id: $app_Id1
      - accountId: $oid1
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
- test:
    name: 用户自动签署_证书不存在
    variables:
      - flowId: $flowId
      - app_id: $app_Id1
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'certId': '1111', 'fileId':$fileId, 'authorizedAccountId': $oid1, 'order':1,  'posBean':$posBean, 'sealId':null,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书不存在']

- test:
    name: 批量添加流程签署区
    variables:
      - app_id: $app_id1
      - flowId: $flowId
      - posBean: {}
      - autoExecute: 0
      - signfield1: {actorIndentityType: 0,assignedItem: 0,assignedPosbean: false,assignedSeal: false,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: '',sealType: 1,signType: 0,signerAccountId: $oid1}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]
      - eq: ["status_code", 200]


- test:
    name: 更新签署区并执行签署-certid不匹配
    variables:
      - app_id: $app_Id1
      - flowId: $flowId
      - accountId: $oid1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      - addSignfield:  {certId: $certId2,actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']


- test:
    name: 更新签署区并执行签署-certid不存在
    variables:
      - app_id: $app_Id1
      - flowId: $flowId
      - accountId: $oid1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      - addSignfield:  {certId: '111',actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书不存在']

- test:
    name: 更新签署区并执行签署-certid不存在
    variables:
      - app_id: $app_Id1
      - flowId: $flowId
      - accountId: $oid1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      - addSignfield:  {certId: $certId1,actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: "一步到位创建流程-对内经办人签署"
    variables:
      - app_id: $app_id1
      - json: {"accountId":$oid1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$oid1,"signerAccountName":"","signerAuthorizerId":$orgOid,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": '',"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"2","thirdOrderNo":""}]}],"recipients":[]}

    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - flowId2: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: "查询签署区"
    variables:
      - app_id: $app_id1
      - accountId: $oid1
      - signfieldIds: None
      - flowId: $flowId2
    extract:
      - signfieldId2: content.data.signfields.0.signfieldId
    api: api/iterate_cases/search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 更新签署区并执行签署-证书不匹配
    variables:
      - app_id: $app_Id1
      - flowId: $flowId2
      - accountId: $oid1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      - addSignfield:  {certId: $certId2,actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId2,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId2]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 证书与签署主体不匹配']
- test:
    name: 更新签署区并执行签署
    variables:
      - app_id: $app_Id1
      - flowId: $flowId2
      - accountId: $oid1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      - addSignfield:  {certId: $certId1,actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId2,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId2]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']
      #- test:
      #    name: 更新签署区并执行签署-appid不匹配
      #    variables:
      #      - app_id: $app_Id1
      #      - flowId: $flowId
      #      - accountId: $oid1
      #      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      #      - addSignfield:  {certId: $certId3,actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      #      - addSignfields: [$addSignfield]
      #      - updateSignfields: []
      #      - signfieldIds: [$signfieldId0]
      #      - dingUser: {}
      #      - approvalPolicy: 0
      #    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
      #    validate:
#      - eq: ["content.code",1435002]
#       - contains: ["content.message",'参数错误: 指定证书和主体账号所属应用不一致']

- test:
    name: "吊销成功"
    api: api/user/cert-service/revokeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 更新签署区并执行签署-已吊销
    variables:
      - app_id: $app_Id1
      - flowId: $flowId
      - accountId: $oid1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      - addSignfield:  {certId: $certId1,actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已吊销']

- test:
    name: 更新签署区并执行签署-已过期
    variables:
      - app_id: $app_Id1
      - flowId: $flowId
      - accountId: $oid1
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: false, width: 0}
      - addSignfield:  {certId: $certid_expired,actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $oid1 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId, flowId: $flowId,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId2,sealType: 1,signType: 1,signerAccountId: $oid1}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已过期']

- test:
    name: 添加用户手动签署-certid已吊销
    variables:
      - app_id: $app_id1
      - flowId: $flowId
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - posBean_side: {'posPage':'1-2','posX':300, 'posY':600}
      - signfield_file1: {'certId': $certId1,  'fileId':$fileId, 'signerAccountId':$oid1, 'actorIndentityType':'0', 'authorizedAccountId':$oid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1,'handDrawnWay':0}
      - signfields: [$signfield_file1]
    api: signfieldsCreate_handSign($flowId, $signfields)
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已吊销']

- test:
    name: "一步到位创建流程-对内，certid已吊销"
    variables:
      - app_id: $app_id1
      - json: {"accountId":$oid1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$oid1,"signerAccountName":"","signerAuthorizerId":$oid1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": $certId1,"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}

    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已吊销']

- test:
    name: 一步发起指定证书id用户手动签-证书id已吊销
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: $certId1,autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已吊销']

- test:
    name: "一步到位创建流程-对内，certid已过期"
    variables:
      - app_id: $app_id1
      - json: {"accountId":$oid1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"0","signerAccount":"","signerAccountId":$oid1,"signerAccountName":"","signerAuthorizerId":$oid1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": $certid_expired,"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}

    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已过期']

- test:
    name: 一步发起指定证书id用户手动签-证书id已过期
    variables:
      - app_id: $app_id1
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }

      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $oid1,authorizedAccountId: $oid1}, signfields: [ { certId: $certid_expired,autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 指定证书已过期']



