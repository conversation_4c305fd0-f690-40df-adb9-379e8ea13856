- config:
    name: "发起签署接口优化 - 无核验的签署流程"
    base_url: ${ENV(footstone_signflow_initiate_url)}
    variables:
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(mobile_shield_wukong_appid)}
      - path_pdf1: data/个人借贷合同.pdf
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}
      - appid_oid: ${ENV(mobile_shield_wukong_appid_oid)}

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying2019111411321231
      - creator: $caogu_accountId
      - idNumber: 1111111111111111171
      - idType: CRED_ORG_UNKNOWN
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId


- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "静默授权 - 陈凯丽"
    variables:
      - accountId: $accountId_ckl
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]

- test:
    name: "静默授权 - 泗县深泉"
    variables:
      - accountId: $sixian_organize_id
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]

- test:
    name: "静默授权 - 东营伟信"
    variables:
      - accountId: $dongying_organize_id
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]


- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]




#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 签署人等传空 - actorIndentityType传""
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体类型非法："]


- test:
    name: createFlowOneStep - 平台自动签 - 签署人等传空 - actorIndentityType不传
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.flowId

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId1
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $appid_oid]
      - eq: ["content.data.signfields.0.authorizedAccountId", $appid_oid]
      - eq: ["content.data.signfields.0.actorIndentityType", 2]

- test:
    name: createFlowOneStep - 平台自动签 - 签署人等传错
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "123",authorizedAccountId: "中文"}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "7", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435201]
      - eq: ["content.message", "用户中心查询账户失败: open user不存在. ouid:123"]



- test:
    name: createFlowOneStep - 平台自动签 - 签署人等传非当前平台oid
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "1781f30830d14899a5ef7cfd5e1b73f6",authorizedAccountId: "1781f30830d14899a5ef7cfd5e1b73f6"}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId3: content.data.flowId

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId3
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $appid_oid]
      - eq: ["content.data.signfields.0.authorizedAccountId", $appid_oid]
      - eq: ["content.data.signfields.0.actorIndentityType", 2]

- test:
    name: createFlowOneStep - 平台自动签 - 签署人等传当前平台oid
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: $appid_oid,authorizedAccountId: $appid_oid}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId4: content.data.flowId

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId4
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $appid_oid]
      - eq: ["content.data.signfields.0.authorizedAccountId", $appid_oid]
      - eq: ["content.data.signfields.0.actorIndentityType", 2]



#用户手动/自动签
- test:
    name: createFlowOneStep - 用户自动/手动签 - 个人签正常发起 - 自动签
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId5: content.data.flowId

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId5
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.actorIndentityType", 0]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 个人签正常发起 - 手动签
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 个人签正常发起 - 签署主体不传
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: "", actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



- test:
    name: createFlowOneStep - 用户自动/手动签 - 个人签正常发起 - actorIndentityType传0
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



- test:
    name: createFlowOneStep - 用户自动/手动签 - 个人签正常发起 - actorIndentityType不传
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId6: content.data.flowId

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId6
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.actorIndentityType", 0]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 企业签正常发起 - 自动签
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 企业签正常发起 - 手动签 - actorIndentityType不传
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: "",  fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 企业签正常发起 - 手动签 - actorIndentityType传3
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", actorIndentityType: "3",  fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId7: content.data.flowId

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flowId7
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.signerAccountId", $accountId_ckl]
      - eq: ["content.data.signfields.0.authorizedAccountId", $sixian_organize_id]
      - eq: ["content.data.signfields.0.actorIndentityType", 3]




#异常场景
- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 个人签 - 签署主体不传 - actorIndentityType传1
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "1", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]



- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid不传 - 签署主体oid不传 - actorIndentityType传0
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: "", actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署人账号不能为空"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid不传- 签署主体oid不传 - actorIndentityType传2
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: "", actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署人账号不能为空"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid不传 - 签署主体为企业 - actorIndentityType传0
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid不传 - 签署主体为个人 - actorIndentityType传2
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署人账号不能为空"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid不传 - 签署主体为企业 - actorIndentityType传2
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid不传 - 签署主体为企业 - actorIndentityType传3
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "3", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid非个人用户oid - 签署主体不传 - actorIndentityType不传
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $sixian_organize_id, authorizedAccountId: "" }, signfields: [ { certId: "", autoExecute: true, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署人不能为机构类型"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid非个人用户oid - 签署主体传个人oid
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $sixian_organize_id, authorizedAccountId: $accountId_ckl }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署人不能为机构类型"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署人oid非个人用户oid - 签署主体传另外一家企业oid
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $sixian_organize_id, authorizedAccountId: $dongying_organize_id }, signfields: [ { certId: "", autoExecute: "", actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署人不能为机构类型"]


- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确 - 签署主体传与签署人不同的个人oid - 手动签
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: $caogu_accountId }, signfields: [ { certId: "", autoExecute: "", actorIndentityType: "0", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不支持代他人签署"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确  - 签署主体传与签署人不同的个人oid - 自动签 -actorIndentityType不传
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: $caogu_accountId }, signfields: [ { certId: "", autoExecute: true, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 不支持代他人签署"]


- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确 - 签署主体企业 - actorIndentityType不传
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: $sixian_organize_id }, signfields: [ { certId: "", autoExecute: true, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确 - 签署主体企业 - actorIndentityType传1
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: $sixian_organize_id }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: "1", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]


- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确 - 签署主体不传 - actorIndentityType传2
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: "" }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确 - 签署主体不传 - actorIndentityType传3
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: "" }, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "3", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]

- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确 - 签署主体个人 - actorIndentityType传4
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: $accountId_ckl }, signfields: [ { certId: "", autoExecute: true, actorIndentityType: "4", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]


- test:
    name: createFlowOneStep - 用户自动/手动签 - 发起失败 - 签署主体oid不正确 - 签署主体个人 - actorIndentityType传2
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl, authorizedAccountId: $accountId_ckl }, signfields: [ { certId: "", autoExecute: false, actorIndentityType: "2", fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 签署主体和签署区主体类型不一致"]















