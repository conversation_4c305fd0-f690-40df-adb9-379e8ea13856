# 创建流程并添加文档, 支持自动发起
- api:
    def: createWithSignDocs($autoArchive, $businessScene, $configInfo, $contractValidity, $createWay,$docs, $extend, $initiatorAccountId, $payerAccountId, $signValidity)
    request:
      url: /v1/signflows/createWithSignDocs
      method: POST
      headers: ${get_headers($app_id)}
      json:
        autoArchive: $autoArchive
        autoInitiate: true
        businessScene: $businessScene
        configInfo: $configInfo
        contractValidity: $contractValidity
        createWay: $createWay
        docs: $docs
        extend: $extend
        initiatorAccountId: $initiatorAccountId
        payerAccountId: $payerAccountId
        signValidity: $signValidity

# 添加或更新签署区并执行签署
- api:
    def: addUpdateExecute_sign($flowId,$addSignfields,$updateSignfields,$signfieldIds,$approvalPolicy,$dingUser,$accountId)
    request:
      url: ${ENV(item_url)}/v3/signflows/$flowId/signfields/add-Update-Execute
      method: PUT
      headers: ${get_headers($app_id)}
      json:
        async: true
        accountId: $accountId
        addSignfields: $addSignfields
        updateSignfields: $updateSignfields
        signfieldIds: $signfieldIds
        approvalPolicy: $approvalPolicy
        dingUser: $dingUser

# 查询流程详情
- api:
    def: select_detail($flowId,$queryAccountId)
    request:
      url: ${ENV(item_url)}/v1/signflows/$flowId/detail?queryAccountId=$queryAccountId
      method: GET
      headers: ${get_headers($app_id)}


# 获取签署文档和签署区
- api:
    def: docs_signfields($flowId,$accountId)
    request:
      url: /v1/signflows/$flowId/docs-signfields?accountId=$accountId
      method: GET
      headers: ${get_headers($app_id)}

## 添加或更新签署区并执行签署
#- api:
#    def: addUpdateExecute_sign($flowId,$dingCorpId,$dingIsvAppId,$dingUserId,$signfieldIds,$accountId,$fileId,$flowId,$posPage,$sealFileKey,$sealId,$sealType,$signerAccountId,$autoExecute)
#    request:
#      url: /v1/signflows/$flowId/signfields/add-Update-Execute
#      method: PUT
#      headers: ${get_headers($app_id)}
#      json:
#        addSignfields:
#      - actorIndentityType: 0
#        assignedItem: true
#        assignedPosbean: true
#        assignedSeal: true
#        authorizedAccountId: $accountId
#        autoExecute: $autoExecute
#        extendFieldDatas: {}
#        fileId: $fileId
#        flowId: $flowId
#        order: 0
#        posBean:
#          addSignTime: true
#          posPage: $posPage
#          posX: 0
#          posY: 0
#          qrcodeSign: true
#          signDateBean: {fontSize: 0, format: yyyy-MM-dd, posPage: 0, posX: 0, posY: 0}
#          width: 0
#        sealFileKey: $sealFileKey
#        sealId: $sealId
#        sealType: $sealType
#        signType: 0
#        signerAccountId: $signerAccountId
#        approvalPolicy: 0
#        dingCorpId: $dingCorpId
#        dingIsvAppId: $dingIsvAppId
#        dingUserId: $dingUserId
#        signfieldIds: $signfieldIds
#        updateSignfields:
#      - authorizedAccountId: $accountId
#        extendFieldDatas: {}
#        posBean:
#          addSignTime: true
#          posPage: $posPage
#          posX: 0
#          posY: 0
#          qrcodeSign: true
#          signDateBean: {fontSize: 0, format: yyyy-MM-dd, posPage: 0, posX: 0, posY: 0}
#          width: 0
#        sealFileKey: $sealFileKey
#        sealId: $sealId
#        signfieldId: string

# 批量添加流程参与人, 企业主体不存在时会根据名称创建企业账号
#- api:
#    def: rcpts-accounts($flowId,$recipientAccountId,$recipientIdentityAccountId,$recipientIdentityAccountName,$identity_type,$roleType)
#    request:
#      url: /v1/$flowId/rcpts-accounts
#      method: PUT
#      headers: ${get_headers($app_id)}
#      json:
#        cover: true
#        recipients:
#      - {recipientAccountId: $$recipientAccountId, recipientIdentityAccountId: $recipientIdentityAccountId, recipientIdentityAccountName: $recipientIdentityAccountName,
#        recipientIdentityAccountType: $identity_type, roleType: $roleType}
