- config:
    name: 自动签流程发起-指定跨企业印章
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_small: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_with_password: ${ENV(file_with_password_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - liangxianhong_stan: ${ENV(orgdy_legal_oid)}
      - yuzan_stan: ${ENV(oid_wang_tsign)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - ckl_saas_oid: ${ENV(standardoid)}
      - ganning_saas_oid: ${ENV(gn_oid_tsign)}
      - sealId_sx_acrossEnterprise: ${ENV(sealId_sx_acrossEnterprise)}
      - sealId_sx_acrossEnterprise_old: ${ENV(sealId_sx_acrossEnterprise_old)}
      - sealId_dy_acrossEnterprise: ${ENV(sealId_dy_acrossEnterprise)}
      - sealId_dy_acrossEnterprise_old: ${ENV(sealId_dy_acrossEnterprise_old)}
      - sealId_sx: ${ENV(sealId_sx)}
      - sealId2_sx: ${ENV(sealId2_sx)}
      - sealId_dy: ${ENV(sealId_dy_approval)}
      - sealId_zl: ${ENV(sealId_zl)}
      - sealId_gn: ${ENV(sealId_gn)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}

#跨企业印章签署：yuzan是泗县管理员可以直接签署-ckl不存在二级授权走审批-甘宁存在二级授权直接签署
- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: fhg123456ws
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建甘宁账号"
    variables:
      - thirdPartyUserId: z111zzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId


- test:
    name: "创建签署人王瑶济账号"
    variables:
      - thirdPartyUserId: yuzan20191r5222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_yuzan: content.data.accountId


- test:
    name: "创建机构-泗县深泉纯净水有限公司"
    variables:
      - thirdPartyUserId: sixian2041132
      - creator: $accountId_yuzan
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: 静默授权
    variables:
      - accountId: $sixian_organize_id
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: 静默授权
    variables:
      - accountId: $accountId_yuzan
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: 静默授权
    variables:
      - accountId: $accountId_ckl
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

#分步静默签署
- test:
    name: "分步创建流程 - 添加平台自动签&用户自动签"
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 分步创建流程 - 添加平台自动签&用户自动签
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档
    variables:
      - doc1: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {fileId: $file_with_password}
      - docs: [$doc1,$doc2]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "添加平台自动签签署区-指定老的跨企业授权印章-成功"
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':100}
      - signfield: { 'fileId':$file_small, 'order':1,  'posBean':$posBean, 'sealId':$sealId_dy_acrossEnterprise_old,'signType':1}
      - signfields: [$signfield]
      - flowId: $sign_flowId1
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]


- test:
    name: "添加平台自动签签署区-指定新的跨企业授权印章-成功"
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':200}
      - signfield: { 'fileId':$file_small, 'order':1,  'posBean':$posBean, 'sealId':$sealId_dy_acrossEnterprise,'signType':1}
      - signfields: [$signfield]
      - flowId: $sign_flowId1
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]


- test:
    name: "添加平台自动签签署区-指定新的跨企业授权印章-无权限-失败"
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':200}
      - signfield: { 'fileId':$file_small, 'order':1,  'posBean':$posBean, 'sealId': $sealId2_sx,'signType':1}
      - signfields: [$signfield]
      - flowId: $sign_flowId1
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437182]
      - eq: ["content.message",未获取该企业有效的印章授权]


- test:
    name: "添加平台自动签签署区-指定本企业印章-成功"
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':300}
      - signfield: { 'fileId':$file_small, 'order':1,  'posBean':$posBean, 'sealId':$sealId_dy,'signType':1}
      - signfields: [$signfield]
      - flowId: $sign_flowId1
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId3: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]


- test:
    name: "添加平台自动签签署区-不指定印章-成功"
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':400}
      - signfield: { 'fileId':$file_small, 'order':1,  'posBean':$posBean, 'sealId':"",'signType':1}
      - signfields: [$signfield]
      - flowId: $sign_flowId1
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId4: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]


- test:
    name: "添加企业自动签签署区-指定老的跨企业授权印章-失败"
    variables:
      - flowId: $sign_flowId1
      - posBean: {'posPage':'2','posX':100, 'posY':100}
      - signfield: {'certId': '', 'fileId':$file_small, 'authorizedAccountId': $sixian_organize_id, 'order':1,  'posBean':$posBean, 'sealId':$sealId_sx_acrossEnterprise_old,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 印章和签署主体不匹配']

- test:
    name: "添加企业自动签签署区-指定新的跨企业授权印章-失败"
    variables:
      - flowId: $sign_flowId1
      - posBean: {'posPage':'2','posX':100, 'posY':100}
      - signfield: {'certId': '', 'fileId':$file_small, "signerAccountId":$accountId_yuzan, "actorIndentityType":"2",'authorizedAccountId': $sixian_organize_id, 'order':1,  'posBean':$posBean, 'sealId':$sealId_sx_acrossEnterprise,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 印章和签署主体不匹配']



- test:
    name: "添加企业自动签签署区-指定本企业印章-成功"
    variables:
      - flowId: $sign_flowId1
      - posBean: {'posPage':'2','posX':100, 'posY':100}
      - signfield: {'certId': '', 'fileId':$file_small, 'authorizedAccountId': $sixian_organize_id, 'order':1,  'posBean':$posBean, 'sealId':$sealId_sx,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId5: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",'成功']

- test:
    name: "添加企业自动签签署区-不指定印章-成功"
    variables:
      - flowId: $sign_flowId1
      - posBean: {'posPage':'2','posX':100, 'posY':200}
      - signfield: {'certId': '', 'fileId':$file_small, 'authorizedAccountId': $sixian_organize_id, 'order':1,  'posBean':$posBean, 'sealId':"",'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId6: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",'成功']

- test:
    name: "添加个人自动签签署区-不指定印章-成功"
    variables:
      - flowId: $sign_flowId1
      - posBean: {'posPage':'2','posX':100, 'posY':200}
      - signfield: {'certId': '', 'fileId':$file_small, 'authorizedAccountId': $accountId_ckl, 'order':1,  'posBean':$posBean, 'sealId':"",'signType':2}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId7: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",'成功']

#开启签署流程
- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(6)}

- test:
    name: "添加个人自动签签署区-文件已加密无法签署"
    variables:
      - flowId: $sign_flowId1
      - posBean: {'posPage':'1','posX':100, 'posY':200}
      - signfield: {'certId': '', 'fileId':$file_with_password, 'authorizedAccountId': $accountId_ckl, 'order':1,  'posBean':$posBean, 'sealId':"",'signType':2}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437317]
      - contains: ["content.message",'签署失败']

- test:
    name: "添加企业自动签签署区-文件已加密无法签署"
    variables:
      - flowId: $sign_flowId1
      - posBean: {'posPage':'1','posX':100, 'posY':100}
      - signfield: {'certId': '', 'fileId':$file_with_password, 'authorizedAccountId': $sixian_organize_id, 'order':1,  'posBean':$posBean, 'sealId':$sealId_sx,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437317]
      - contains: ["content.message",'签署失败']

- test:
    name: "添加平台自动签签署区-文件已加密无法签署"
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':400}
      - signfield: { 'fileId':$file_with_password, 'order':1,  'posBean':$posBean, 'sealId':"",'signType':1}
      - signfields: [$signfield]
      - flowId: $sign_flowId1
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437317]
      - contains: ["content.message",'签署失败']

- test:
    name: 归档流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]



#一步发起自动签署流程
- test:
    name: createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, "sealId": $sealId2_sx,actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437182]
      - eq: ["content.message", "未获取该企业有效的印章授权"]


- test:
    name: createFlowOneStep - 平台自动签 - 指定老的跨企业授权印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, "sealId": $sealId_dy_acrossEnterprise_old,actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.flowId


- test:
    name: createFlowOneStep - 平台自动签 - 指定新的跨企业授权印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, "sealId": $sealId_dy_acrossEnterprise,actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId2: content.data.flowId

- test:
    name: createFlowOneStep - 平台自动签 - 不指定跨企业授权印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, "sealId": "",actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId3: content.data.flowId

- test:
    name: createFlowOneStep - 平台自动签 - 指定本企业印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: ""}, signfields: [ { certId: "", autoExecute: true, "sealId": $sealId_dy,actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId4: content.data.flowId


- test:
    name: createFlowOneStep - 企业自动签 - 指定本企业印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { "signerAccountId":$accountId_yuzan, 'authorizedAccountId': $sixian_organize_id}, signfields: [ { certId: "", autoExecute: true, "sealId": $sealId_sx,actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId5: content.data.flowId

- test:
    name: createFlowOneStep - 企业自动签 - 指定新的跨企业印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { "signerAccountId":$accountId_yuzan, 'authorizedAccountId': $sixian_organize_id}, signfields: [ { certId: "", autoExecute: true, "sealId": $sealId_sx_acrossEnterprise,actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 印章和签署主体不匹配']


- test:
    name: createFlowOneStep - 企业自动签 - 指定老的跨企业印章
    variables:
      - doc: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 指定无权限的跨企业印章",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { "signerAccountId":$accountId_yuzan, 'authorizedAccountId': $sixian_organize_id}, signfields: [ { certId: "", autoExecute: true, "sealId": $sealId_sx_acrossEnterprise_old,actorIndentityType: "2", fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 印章和签署主体不匹配']


#API3.0一步发起自动签
- test:
    name: "create-by-file平台自动签 - 指定老的跨企业授权印章"
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": $ckl_saas_oid
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": $file_small,
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "平台自动签 - 指定老的跨企业授权印章",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": []
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "barrierCode": "",
              "chargeMode": "0",
              "orderType":""

            },
            "noticeConfig": {
              "noticeTypes": "1"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": "",
              "redirectUrl": ""
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [

          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "orgSignerInfo": {
              "orgId": $account_id_dy_tsign,
              "orgName": ""

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": $file_small,
              "normalSignFieldConfig": {
                "assignedSealId": $sealId_dy_acrossEnterprise_old,
                "autoSign": true,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 80,
                  "positionY": 150
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 1
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']

- test:
    name: "create-by-file平台自动签 - 指定新的跨企业授权印章"
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_dy_tsign,
              "transactor": {
                "psnId": $ckl_saas_oid
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": $file_small,
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "平台自动签 - 指定新的跨企业授权印章",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": []
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "barrierCode": "",
              "chargeMode": "0",
              "orderType":""

            },
            "noticeConfig": {
              "noticeTypes": "1"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": "",
              "redirectUrl": ""
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [

          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "orgSignerInfo": {
              "orgId": $account_id_dy_tsign,
              "orgName": ""

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": $file_small,
              "normalSignFieldConfig": {
                "assignedSealId": $sealId_dy_acrossEnterprise,
                "autoSign": true,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 80,
                  "positionY": 150
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 1
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']

- test:
    name: "create-by-file平台自动签 - 指定本企业印章"
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": $ckl_saas_oid
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": $file_small,
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "平台自动签 - 指定本企业印章",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": []
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "barrierCode": "",
              "chargeMode": "0",
              "orderType":""

            },
            "noticeConfig": {
              "noticeTypes": "1"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": "",
              "redirectUrl": ""
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [

          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "orgSignerInfo": {
              "orgId": $account_id_dy_tsign,
              "orgName": ""

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": $file_small,
              "normalSignFieldConfig": {
                "assignedSealId": $sealId_dy,
                "autoSign": true,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 80,
                  "positionY": 150
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 1
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']


- test:
    name: "create-by-file平台自动签 - 不指定印章"
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": $ckl_saas_oid
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": $file_small,
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "平台自动签 - 不指定印章",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": []
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "barrierCode": "",
              "chargeMode": "0",
              "orderType":""

            },
            "noticeConfig": {
              "noticeTypes": "1"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": "",
              "redirectUrl": ""
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [

          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "orgSignerInfo": {
              "orgId": $account_id_dy_tsign,
              "orgName": ""

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": $file_small,
              "normalSignFieldConfig": {
                "assignedSealId": "",
                "autoSign": true,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 80,
                  "positionY": 150
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 1
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']

- test:
    name: "create-by-file平台自动签 - 指定无跨企业授权记录印章"
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": $ckl_saas_oid
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": $file_small,
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "平台自动签 - 指定无跨企业授权记录印章",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": []
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "barrierCode": "",
              "chargeMode": "0",
              "orderType":""

            },
            "noticeConfig": {
              "noticeTypes": "1"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": "",
              "redirectUrl": ""
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [

          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "orgSignerInfo": {
              "orgId": $account_id_dy_tsign,
              "orgName": ""

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": $file_small,
              "normalSignFieldConfig": {
                "assignedSealId": $sealId2_sx,
                "autoSign": true,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 80,
                  "positionY": 150
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 1
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1437182]
      - eq: [content.message, '未获取该企业有效的印章授权']


- test:
    name: "create-by-file企业自动签 - 拦截"
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": $ckl_saas_oid
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": $file_small,
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "企业自动签",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": []
            },
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "barrierCode": "",
              "chargeMode": "0",
              "orderType":""

            },
            "noticeConfig": {
              "noticeTypes": "1"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": "",
              "redirectUrl": ""
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [

          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "orgSignerInfo": {
              "orgId": $account_id_sx_tsign,
              "orgName": ""

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": $file_small,
              "normalSignFieldConfig": {
                "assignedSealId": "",
                "autoSign": true,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 80,
                  "positionY": 150
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 1
          }
          ]
        }

    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1435011]
      - contains: [content.message, '不允许自动签署']