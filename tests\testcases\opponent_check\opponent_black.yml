- config:
    name: "e签盾流程创建-更新-添加签名域"
    base_url: ${ENV(opponent_entity_url)}
    variables:
      - app_id1: ${ENV(opponent_appid)}
      - app_id: ${ENV(opponent_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(opponent_appid)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - opponent_check_org_oid: ${ENV(opponent_check_org_oid)}
      - opponent_black_org1: ${ENV(opponent_black_org1)}
      - opponent_black_org2: ${ENV(opponent_black_org2)}
      - opponent_black_person1: ${ENV(opponent_black_person1)}
      - opponent_black_person2: ${ENV(opponent_black_person2)}
      - opponent_black_person3: ${ENV(opponent_black_person3)}
      - opponent_black_person4: ${ENV(opponent_black_person4)}

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "查询泗县企业印章"
    variables:
      - app_id: $app_id1
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - sixian_org_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询校验方企业印章"
    variables:
      - app_id: $app_id1
      - orgId: $opponent_black_org1
    api: api/seals/seals/select_c_seals.yml
    extract:
      - opponent_blackorg_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "查询校验方企业印章"
    variables:
      - app_id: $app_id1
      - orgId: $opponent_check_org_oid
    api: api/seals/seals/select_c_seals.yml
    extract:
      - opponent_org_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]



#验证发起
- test:
    name: "一步发起createAllAtOnce - 发起主体个人 - 签署人是黑名单1"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $opponent_black_person1,"initiatorAuthorizedAccountId":$opponent_black_person1,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"相对方校验路程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$opponent_check_org_oid,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "一步发起createAllAtOnce - 发起主体企业 - 签署人是黑名单2"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $opponent_check_org_oid,"initiatorAuthorizedAccountId":$opponent_check_org_oid,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"1","signerAccount":"","signerAccountId":$opponent_black_person2,"signerAccountName":"","signerAuthorizerId":$opponent_black_person2,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createAllAtOnce - 发起主体企业 - 签署主体是黑名单3"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"initiatorAuthorizedAccountId":$opponent_check_org_oid,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":2},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":2,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$opponent_black_org1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createAllAtOnce - 发起主体企业 - 发起人是黑名单4"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $opponent_black_person2,"initiatorAuthorizedAccountId":$opponent_check_org_oid,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createAllAtOnce - 发起主体企业 - 抄送人是黑名单5"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"initiatorAuthorizedAccountId":$opponent_check_org_oid,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[{recipientAccountId: $opponent_black_person2,recipientIdentityAccountType: 1 ,recipientIdentityAccountId: $sixian_organize_id}]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createAllAtOnce - 发起主体企业 - 抄送主体是黑名单6"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"initiatorAuthorizedAccountId":$opponent_check_org_oid,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[{recipientAccountId: $accountId_ckl,recipientIdentityAccountType: 1 ,recipientIdentityAccountId: $opponent_black_org1}]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createAllAtOnce - 发起主体企业 - 无黑名单7"
    variables:
      - json: {"accountId":$accountId_ckl,"initiatorAccountId": $accountId_ckl,"initiatorAuthorizedAccountId":$opponent_check_org_oid,"createWay":"normal","autoInitiate":true,"autoArchive":false,"businessScene":"e签盾签署流程","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay":1},"docs":[{"fileId":$fileId1}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":"1","signerAccount":"","signerAccountId":$accountId_ckl,"signerAccountName":"","signerAuthorizerId":$accountId_ckl,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"certId": "","fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[{recipientAccountId: $accountId_ckl,recipientIdentityAccountType: 0 ,recipientIdentityAccountId:,$accountId_ckl}]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]




- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单8"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $opponent_black_person3,authorizedAccountId: $opponent_black_person3}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单 - 法人签9"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $opponent_black_person3,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 3, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 签署主体是黑名单 - 企业签10"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $opponent_black_org1}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 签署主体是黑名单 -经办人签11"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $opponent_black_org1}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 4, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 发起人是黑名单12"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $opponent_black_person3 ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 抄送人是黑名单13"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{copierAccountId: $opponent_black_person3 , copierIdentityAccountId: $opponent_black_person3, copierIdentityAccountType: 0 }]
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 抄送人是黑名单14"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{copierAccountId: $opponent_black_person3 , copierIdentityAccountId: $sixian_organize_id, copierIdentityAccountType: 2 }]
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]


- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 抄送主体是黑名单15"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{copierAccountId: $accountId_ckl , copierIdentityAccountId: $opponent_black_org1, copierIdentityAccountType: 2 }]
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 签署人是黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]


- test:
    name: "一步发起createFlowOneStep - 发起主体企业 - 无黑名单16"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{copierAccountId: $accountId_ckl , copierIdentityAccountId: $sixian_organize_id, copierIdentityAccountType: 2}]
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体企业 - 无黑名单1",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "一步发起createFlowOneStep - 发起主体是企业 - 无黑名单17"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{copierAccountId: $accountId_ckl , copierIdentityAccountId: $accountId_ckl, copierIdentityAccountType: 0} ]
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $opponent_check_org_oid, autoArchive: true, autoInitiate: true, businessScene: "一步发起createFlowOneStep - 发起主体是企业 - 无黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $sixian_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



#验证开启
- test:
    name: 创建流程 - v1/signflows
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $opponent_check_org_oid
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl

    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署人在发起主体黑名单18"
    variables:
      - app_id: $app_id1
      - flowId: $flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person2,"actorIndentityType":"0","authorizedAccountId":$opponent_black_person2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程19"
    variables:
      - app_id: $app_id1
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]


- test:
    name: 创建流程 - v1/signflows20
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $opponent_black_person2
      - initiatorAuthorizedAccountId: $opponent_check_org_oid
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl

    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId2
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 发起人在发起主体黑名单21"
    variables:
      - app_id: $app_id1
      - flowId: $flowId2
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId": $accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程22"
    variables:
      - app_id: $app_id1
      - flowId: $flowId2
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: 创建流程 - v1/signflows23
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $opponent_check_org_oid
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl

    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档24
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId3
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署主体在发起主体黑名单 - 企业签25"
    variables:
      - app_id: $app_id1
      - flowId: $flowId3
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $opponent_black_org2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程26"
    variables:
      - app_id: $app_id1
      - flowId: $flowId3
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]

- test:
    name: 创建流程 - v1/signflows27
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $opponent_check_org_oid
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl

    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId4
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署主体在发起主体黑名单 - 法人签28"
    variables:
      - app_id: $app_id1
      - flowId: $flowId4
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"3","authorizedAccountId": $opponent_black_org2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程29"
    variables:
      - app_id: $app_id1
      - flowId: $flowId4
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]


- test:
    name: 创建流程 - v1/signflows30
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $opponent_check_org_oid
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl

    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId5: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId5
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署人发起主体黑名单 - 经办人签31"
    variables:
      - app_id: $app_id1
      - flowId: $flowId5
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person2,"actorIndentityType":"4","authorizedAccountId": $sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程32"
    variables:
      - app_id: $app_id1
      - flowId: $flowId5
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]



- test:
    name: 创建流程 - v1/signflows - 发起主体是个人33
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $opponent_black_person2
      - initiatorAuthorizedAccountId: $opponent_black_person2
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl

    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId6: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId6
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署人在发起人的黑名单34"
    variables:
      - app_id: $app_id1
      - flowId: $flowId6
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person4,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程35"
    variables:
      - app_id: $app_id1
      - flowId: $flowId6
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 创建流程 - v1/signflows - 抄送人在发起主体黑名单36
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $opponent_check_org_oid
      - payerAccountId:
      - signValidity:
      - copierAccountId: $opponent_black_person2
      - copierIdentityAccountId: $opponent_black_person2

    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId7: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId7
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 37"
    variables:
      - app_id: $app_id1
      - flowId: $flowId7
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId": $accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加流程抄送人 - 抄送人在签署主体黑名单 38"
    variables:
      - app_id: $app_id1
      - flowId: $flowId7
      - recipients: [{"recipientAccountId": $opponent_black_person2,"recipientIdentityAccountId": $sixian_organize_id,"recipientIdentityAccountType":1,"roleType": 2}]
    api: api/mobile-shield/recipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程 - 分布发起时，抄送人在发起主体黑名单39"
    variables:
      - app_id: $app_id1
      - flowId: $flowId7
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - eq: ["content.message", "检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同"]





#验证签署
#同一签署区 - 签署人在签署主体黑名单
- test:
    name: 创建流程 - 签署人在签署主体黑名单40
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId8: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId8
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署人在签署主体黑名单 - 企业签41"
    variables:
      - app_id: $app_id1
      - flowId: $flowId8
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person2,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程42"
    variables:
      - app_id: $app_id1
      - flowId: $flowId8
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: 签署43
    variables:
      - accountId: $opponent_black_person2
      - flowId: $flowId8
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId1, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person2}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


- test:
    name: 签署44
    variables:
      - accountId: $opponent_black_person2
      - flowId: $flowId8
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId1, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person2}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]




#不同签署区 - 签署人在签署主体黑名单
- test:
    name: 创建流程 - 签署人在签署主体黑名单45
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId9: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId9
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署人在签署主体黑名单 - 企业签46"
    variables:
      - app_id: $app_id1
      - flowId: $flowId9
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person2,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署人在校验方黑名单 - 个人签47"
    variables:
      - app_id: $app_id1
      - flowId: $flowId9
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person2,"actorIndentityType":"0","authorizedAccountId": $opponent_black_person2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId3: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId9
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: 签署48
    variables:
      - accountId: $opponent_black_person2
      - flowId: $flowId9
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId2, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person2}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


- test:
    name: 签署49
    variables:
      - accountId: $opponent_black_person2
      - flowId: $flowId9
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId2, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person2}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]





#不同签署区 - 签署主体A在签署主体B的黑名单
- test:
    name: 创建流程 - 签署主体A在签署主体B的黑名单50
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId10: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId10
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签51"
    variables:
      - app_id: $app_id1
      - flowId: $flowId10
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId4: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 签署人在校验方黑名单 - 企业签52"
    variables:
      - app_id: $app_id1
      - flowId: $flowId10
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person1,"actorIndentityType":"2","authorizedAccountId": $opponent_black_org1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId5: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId10
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: 签署 - 由黑名单的主体签署53
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId10
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId4, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]

- test:
    name: v3签署 - 由黑名单的主体签署54
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId10
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId4, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


- test:
    name: 签署 - 无黑名单的主体签署55
    variables:
      - accountId: $opponent_black_person1
      - flowId: $flowId10
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_black_org1, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_blackorg_sealId, signfieldId: $signfieldId5, signerOperatorAuthorizerId: $opponent_black_org1, signerOperatorId: $opponent_black_person1}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.signfieldSignResult.0.optResult",0]



#发起主体在签署主体的黑名单
- test:
    name: 创建流程 - 发起主体在签署主体的黑名单56
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $opponent_black_org1
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId12: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId12
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签57"
    variables:
      - app_id: $app_id1
      - flowId: $flowId12
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId7: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



- test:
    name: "开启签署流程58"
    variables:
      - app_id: $app_id1
      - flowId: $flowId12
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: 签署 - 发起主体在签署主体黑名单59
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId12
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId7, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]

- test:
    name: v3签署 - 发起主体在签署主体黑名单60
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId12
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId7, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


#发起人在签署主体的黑名单
- test:
    name: 创建流程 - 发起人在签署主体的黑名单61
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $opponent_black_person2
      - initiatorAuthorizedAccountId:
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId11: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId11
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签62"
    variables:
      - app_id: $app_id1
      - flowId: $flowId11
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId9: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId11
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: 签署 - 发起人在签署主体黑名单63
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId11
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId9, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


- test:
    name: v3签署 - 发起人在签署主体黑名单64
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId11
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId9, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


#抄送人在签署主体的黑名单
- test:
    name: 创建流程 - 抄送人在签署主体的黑名单65
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - initiatorAuthorizedAccountId:
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId13: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId13
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签67"
    variables:
      - app_id: $app_id1
      - flowId: $flowId13
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId10: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "添加流程抄送人 - 抄送人在签署主体黑名单68 "
    variables:
      - app_id: $app_id1
      - flowId: $flowId13
      - recipients: [{"recipientAccountId": $opponent_black_person2,"recipientIdentityAccountId": $opponent_black_person2,"recipientIdentityAccountType":0,"roleType": 2}]
    api: api/mobile-shield/recipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId13
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: 签署 - 抄送人在签署主体黑名单70
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId13
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId10, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: v3签署 - 抄送人在签署主体黑名单71
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId13
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId10, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]

#抄送主体在签署主体的黑名单
- test:
    name: 创建流程 - 抄送人在签署主体的黑名单72
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - initiatorAuthorizedAccountId:
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId14: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId14
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签"
    variables:
      - app_id: $app_id1
      - flowId: $flowId14
      - signfields: [{"fileId":$fileId1,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId11: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "添加流程抄送人 - 抄送人在签署主体黑名单73 "
    variables:
      - app_id: $app_id1
      - flowId: $flowId14
      - recipients: [{"recipientAccountId": $accountId_ckl,"recipientIdentityAccountId": $opponent_black_org1,"recipientIdentityAccountType":1,"roleType": 2}]
    api: api/mobile-shield/recipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId14
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}


- test:
    name: 签署 - 抄送人在签署主体黑名单75
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId14
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId11, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]

- test:
    name: v3签署 - 抄送人在签署主体黑名单76
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId14
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId11, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $accountId_ckl}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


#全员黑名单
- test:
    name: 创建流程 - 抄送人在签署主体的黑名单77
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $opponent_black_person2
      - initiatorAuthorizedAccountId: $opponent_black_org1
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId15: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId15
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签78"
    variables:
      - app_id: $app_id1
      - flowId: $flowId15
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person3,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId12: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "添加流程抄送人 - 抄送人在签署主体黑名单79 "
    variables:
      - app_id: $app_id1
      - flowId: $flowId15
      - recipients: [{"recipientAccountId": $opponent_black_person3,"recipientIdentityAccountId": $opponent_black_org2,"recipientIdentityAccountType":1,"roleType": 2}]
    api: api/mobile-shield/recipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId15
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}



- test:
    name: 签署 - 抄送人在签署主体黑名单80
    variables:
      - accountId: $opponent_black_person3
      - flowId: $flowId15
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId12, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person3}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


- test:
    name: v3签署 - 抄送人在签署主体黑名单81
    variables:
      - accountId: $opponent_black_person3
      - flowId: $flowId15
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId12, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person3}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]


#正常发起 - 开启 - 签署
- test:
    name: 创建流程 - 正常发起 - 开启 - 签署82
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $sixian_organize_id
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId16: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId16
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签"
    variables:
      - app_id: $app_id1
      - flowId: $flowId16
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person1,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId14: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "添加流程抄送人 "
    variables:
      - app_id: $app_id1
      - flowId: $flowId16
      - recipients: [{"recipientAccountId": $accountId_ckl,"recipientIdentityAccountId": $accountId_ckl,"recipientIdentityAccountType":0,"roleType": 2}]
    api: api/mobile-shield/recipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId16
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $opponent_black_person1
      - app_id: $app_id1
      - flowId: $flowId16
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $opponent_black_person1
      - bizId: $bizId
      - app_id: $app_id1
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 签署 - 正常签署83
    variables:
      - accountId: $opponent_black_person1
      - flowId: $flowId16
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId14, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person1}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.signfieldSignResult.0.optResult",0]


#正常发起 - 开启 -v3签署
- test:
    name: 创建流程 - 正常发起 - 开启 - v3签署84
    variables:
      - app_id: $app_id1
      - extend:
      - autoArchive: true
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $sixian_organize_id
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId17: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
      - app_id: $app_id1
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $flowId17
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "添加签署区 - 企业签"
    variables:
      - app_id: $app_id1
      - flowId: $flowId17
      - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person1,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId16: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "添加流程抄送人 "
    variables:
      - app_id: $app_id1
      - flowId: $flowId17
      - recipients: [{"recipientAccountId": $accountId_ckl,"recipientIdentityAccountId": $accountId_ckl,"recipientIdentityAccountType":0,"roleType": 2}]
    api: api/mobile-shield/recipients.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: $app_id1
      - flowId: $flowId17
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $opponent_black_person1
      - app_id: $app_id1
      - flowId: $flowId17
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $opponent_black_person1
      - bizId: $bizId
      - app_id: $app_id1
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 签署 - 正常签署
    variables:
      - accountId: $opponent_black_person1
      - flowId: $flowId17
      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
      - addSignfields: []
      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId16, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person1}
      - updateSignfields: [$updateSignfield]
      - signfieldIds: []
      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
      - dingUser:   #钉钉用户数据，钉钉审批时必传
      - app_id: $app_id1
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]

      #验证自动签不受黑名单影响
      #- test:
      #    name: createFlowOneStep - 发起人、发起主体、签署人、签署主体A、抄送人、抄送主体均在签署主体B黑名单
      #    variables:
      #        - doc : {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      #        - attachments: []
      #        - docs: [$doc]
      #        - copiers: [{copierAccountId: $opponent_black_person3 , copierIdentityAccountId: $opponent_black_org1, copierIdentityAccountType: 2 }]
      #        - flowInfo: { initiatorAccountId: $opponent_black_person2 ,initiatorAuthorizedAccountId: $opponent_black_org2,autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep - 发起人、发起主体、签署人、签署主体A、抄送人、抄送主体均在签署主体B黑名单",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      #        - signers: [{platformSign: false, signOrder: 1,signerAccount: {signerAccountId: $opponent_black_person4,authorizedAccountId: $opponent_check_org_oid }, signfields: [ { certId: "", autoExecute: true, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },{platformSign: false, signOrder: 1,signerAccount: {signerAccountId: $accountId_ckl,authorizedAccountId: $opponent_black_org1 }, signfields: [ { certId: "", autoExecute: true, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 310,   posY: 310  }, signType: 1,   width: 150 }],  thirdOrderNo: 747 }]
      #    api: api/mobile-shield/createFlowOneStep.yml
      #    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      #    extract:
      #        - flowId88: content.data.flowId
      #    teardown_hooks:
      #        - ${hook_sleep_n_secs(20)}
      #
      #- test:
      #    name: 查询签署区信息
      #    variables:
      #        - flowId: $flowId88
      #    api: api/mobile-shield/signflows_search_signfields.yml
      #    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", "成功"]
#      - eq: ["content.data.signfields.0.status", 4]
#      - eq: ["content.data.signfields.0.statusDescription", 执行完成]