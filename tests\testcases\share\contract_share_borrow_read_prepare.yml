- config:
    name: "分享 - 合同借阅"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - path_pdf: 个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}

- test:
    name: "创建流程发起人即签署人账号，注，这个人需要有分享权限"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 130627198311032236
      - mobile: ***********
      - name: 卜伟强
      - thirdPartyUserId: weiqiang1983
    extract:
      - initatorAccountId: content.data.accountId


- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $initatorAccountId
    extract:
      - sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司（请将此企业升级为高级版以上，才有分享功能）"
    variables:
      - thirdPartyUserId: dongying201211
      - creator: $initatorAccountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId

- test:
    name: "获取对应的实名组织详情"
    variables:
      - orgId: $dongying_organize_id
    extract:
      - dongying_realname_orgId: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "创建非分享对象账号 - 王默默，非实名"
    variables:
      - thirdPartyUserId: autotest-wmm
      - name: 王默默
      - idNumber: 320721199305231234
      - idType: CRED_PSN_CH_IDCARD
      - email: <EMAIL>
      - mobile:
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - no_share_accountId: content.data.accountId

- test:
    name: "创建分享对象账号 - 王瑶济，非实名"
    variables:
      - thirdPartyUserId: 1234567gfdsadfgh
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - share_accountId1_norealname: content.data.accountId


- test:
    name: "创建第二个分享对象账号曾艳，并实名"
    variables:
      - thirdPartyUserId: fengjiu20191114199132191431252002
      - name: 曾艳
      - idNumber: 362324199301130629
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - share_accountId2_realname: content.data.accountId


- test:
    name: 签署流程分享测试，企业发起，企业需要是高级版以上才有分享功能
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest合同分享",flowConfigInfo:
        {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" },
                    "initiatorAccountId": $initatorAccountId,
                    "initiatorAuthorizedAccountId": $dongying_realname_orgId }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $initatorAccountId,authorizedAccountId: $initatorAccountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId_shared: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["status_code", 200]

- test:
    name: 获取实名地址场景：悟空实名：获取个人实名地址
    variables:
      - app_Id1: $app_id
      - flowId: $sign_flowId_shared
      - accountId: $initatorAccountId
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
        # 为什么要判断data.type = 0
      - eq: ["content.data.type", "0"]

- test:
    name: 发起实名流程
    variables:
      - accountId: $initatorAccountId
      - name: 卜伟强
      - notifyUrl: www.baidu.com
      - mobileNo: ***********
    extract:
      - realnameflowId: content.data.flowId
    api: api/realname/realname/telecom3Factors.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]


- test:
    name: 实名验证码
    variables:
      - flowId: $realnameflowId
      - authcode: 123456
    api: api/realname/realname/telecom3FactorsCode.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: verifycode
    variables:
      - flowId: $realnameflowId
      - authcode: 123456
    api: api/realname/verifyCode.yml
    extract:
      - verifycode: content.data.verifyCode
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

#意愿认证
- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $initatorAccountId
      - flowId: $sign_flowId_shared
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $initatorAccountId
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.code",0]

- test:
    name: "添加或更新签署区并执行签署"
    variables:
      - flowId: $sign_flowId_shared
      - accountId: $initatorAccountId
      - addSignfields:  [
      {"fileId":$fileId1, "flowId": $sign_flowId_shared, "sealId":$sealId1,
       "signerAccountId":$initatorAccountId , "authorizedAccountId":$initatorAccountId,
       "signerOperatorAuthorizerId":$initatorAccountId,"signerOperatorId":$initatorAccountId,
       "signWillingType":0,"assignedPosbean":true,
       "posBean":{"posX":206,"posY":504,"addSignTime":false,"width":150,"signDateBeanType":0,"posPage":2}
      }]
      - updateSignfields: []
      - signfieldIds: []
    api: api/iterate_cases/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "流程参与人查询 -detail_V2"
    setup_hooks:
      - ${sleep_N_secs(3)}
    variables:
      - flowId: $sign_flowId_shared
      - queryAccountId: $initatorAccountId
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",2]
    extract:
      - processId: content.data.processId

- test:
    api: api/saas-common-manage/createShare.yml
    name: 文件分享创建
    variables:
    accountId: $initatorAccountId
    needShareCode: true
    needShareQrCode: false
    resourceId: $processId
    resourceType: 'PROCESS'
    shareType: 2
    shareOperateType: 'BORROW_READ'
    # shareOperateType: 'CHECK' # 文件核验
    # shareOperateType: 'NORMAL_SHARE' # 催办
    subjectId: $dongying_realname_orgId
    router: 'share'
    shareEndTime: ${get_timestamp_13(3600000)}
    shareTargets:
      [
      {
        targetOperatorOid: $share_accountId1_norealname,
        targetSubjectOid: $share_accountId1_norealname,
        targetAccount: $share_accountId1_norealname,
        targetAccountType: 0
      },
      {
        targetOperatorOid: $share_accountId2_realname,
        targetSubjectOid: $share_accountId2_realname,
        targetAccount: $share_accountId2_realname,
        targetAccountType: 0
      }
      ]
    validate:
      - eq: [status_code, 200]
      - contains:
          - content.message
          - '成功'
    extract:
      resourceShareId: content.data.resourceShareId

- test:
    name: "分享参与人（未实名）查询 - 有resourceShareId -signDocDetail"
    variables:
      - flowId: $sign_flowId_shared
      - queryAccountId: $share_accountId1_norealname
      - resourceShareId: $resourceShareId
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.flowDocs.0.docStatus",2]


- test:
    name: "分享参与人（已实名）查询 - 有resourceShareId -signDocDetail"
    variables:
      - flowId: $sign_flowId_shared
      - queryAccountId: $share_accountId2_realname
      - resourceShareId: $resourceShareId
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.flowDocs.0.docStatus",2]



- test:
    name: "非分享参与人查询 - 有resourceShareId -detail_V2"
    variables:
      - flowId: $sign_flowId_shared
      - queryAccountId: $no_share_accountId
      - resourceShareId: $resourceShareId
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",2]






