- config:
    name: "0528接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_Id1: ${ENV(xuanyuan_realname)}
      - app_Id2: ${ENV(wukong_realname_not_in_white)}
      - app_Id3: ${ENV(wukong_no_realname)}
      - fileId1: ${get_file_id($app_Id1,$path_pdf1)}
      - fileId2: ${get_file_id($app_Id2,$path_pdf1)}
      - fileId3: ${get_file_id($app_Id3,$path_pdf1)}

- test:
    name: "xuanyuan_realname下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds字段
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $standard_sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_open_sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "个人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "不限-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"1","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "法人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"3","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "经办人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"4","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "企业-sealIds字段中存在不是实名组织中的企业列表中的印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1,$org_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'印章和签署主体不匹配']

- test:
    name: "企业-sealIds字段中全部印章的类型是企业公章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1,$org_sealId2]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "企业-sealIds字段中存在印章的类型是法人章-报错"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId1,$legal_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'不支持的印章类型法定代表人章']

- test:
    name: "企业-sealIds字段中存在印章的类型是作废章-报错"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$cancellation_sealId]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 不支持的印章类型']

- test:
    name: "wukong_realname下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - thirdPartyUserId: yuzan2019152224212341113
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId1: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - thirdPartyUserId: caogu201911114113212133232
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId1: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - thirdPartyUserId: sixian20191114112344323344
      - creator: $caogu_accountId1
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id1: content.data.orgId

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - orgId: $sixian_organize_id1
    extract:
      - standard_sixian_organize_id1: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - orgId: $standard_sixian_organize_id1
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId11: org_sealId1
      - org_sealId22: org_sealId2
      - org_sealId33: org_sealId3
      - legal_sealId11: legal_sealId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建签署流程2"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - autoArchive: true
      - businessScene: 悟空实名签校验sealIds字段
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId1
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - docs: [{'fileHash': '', 'fileId': $fileId2, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - orgId: $sixian_organize_id1
      - alias: 印章1
      - central: STAR
      - color: RED
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    extract:
      - org1_open_sealId2: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - orgId: $sixian_organize_id1
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org1_open_sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "个人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId2,"signerAccountId":$yuzan_accountId1,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org1_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "不限-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId2,"signerAccountId":$yuzan_accountId1,"actorIndentityType":"1","authorizedAccountId":$sixian_organize_id1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org1_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']


      #- test:
      #    name: "法人-报错“不支持指定印章列表的任务类型”"
      #    variables:
      #        - app_id: ${ENV(wukong_realname_not_in_white)}
      #        #        - flowId: $sign_flowId1
      #        - signfields: [{"fileId":$fileId2,"signerAccountId":$yuzan_accountId1,"actorIndentityType":"3","authorizedAccountId":$sixian_organize_id1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
      #    api: api/iterate_cases/signfieldsCreate_handSign.yml
      #    validate:
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "经办人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId2,"signerAccountId":$yuzan_accountId1,"actorIndentityType":"4","authorizedAccountId":$sixian_organize_id1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org1_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "企业-sealIds字段中存在不是企业列表中的印章"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId2,"signerAccountId":$yuzan_accountId1,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org1_open_sealId1,$org_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'印章和签署主体不匹配']

- test:
    name: "企业-sealIds字段中全部印章的类型是企业公章"
    variables:
      - app_id: ${ENV(wukong_realname_not_in_white)}
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId2,"signerAccountId":$yuzan_accountId1,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org_sealId11,$org_sealId22]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "wukong_no_realname下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - thirdPartyUserId: yuzan2019152224212341113234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId2: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - thirdPartyUserId: caogu20188832121332322222
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId2: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - thirdPartyUserId: sixian20191114113233443444
      - creator: $caogu_accountId2
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id2: content.data.orgId


- test:
    name: "创建签署流程3"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - autoArchive: true
      - businessScene: 悟空非实名签校验sealIds字段
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId2
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId3, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId2
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - orgId: $sixian_organize_id2
      - alias: 印章1
      - central: STAR
      - color: RED
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    extract:
      - org2_open_sealId2: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - orgId: $sixian_organize_id2
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org2_open_sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "个人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId3,"signerAccountId":$yuzan_accountId2,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org2_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "不限-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId3,"signerAccountId":$yuzan_accountId2,"actorIndentityType":"1","authorizedAccountId":$sixian_organize_id2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org2_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "经办人-报错“不支持指定印章列表的任务类型”"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId3,"signerAccountId":$yuzan_accountId2,"actorIndentityType":"4","authorizedAccountId":$sixian_organize_id2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org2_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 不支持指定印章列表的任务类型']

- test:
    name: "企业-sealIds字段中存在不是企业列表中的印章"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId3,"signerAccountId":$yuzan_accountId2,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org2_open_sealId1,$org_open_sealId1]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'印章和签署主体不匹配']

- test:
    name: "企业-sealIds字段中全部印章的类型是企业公章"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId3,"signerAccountId":$yuzan_accountId2,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id2,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":[$org2_open_sealId1,$org2_open_sealId2]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
