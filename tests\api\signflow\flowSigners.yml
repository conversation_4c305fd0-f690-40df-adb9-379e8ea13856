#流程签署人

#查询流程签署人
- api:
    def: signersSelect($flowId)
    request:
      url: /v1/signflows/$flowId/signers
      method: GET
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]

#签署人催签
- api:
    def: rushsign($flowId, $accountId, $noticeTypes, $rushsignAccountId)
    request:
      url: /v1/signflows/$flowId/signers/rushsign
      method: PUT
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
        noticeTypes: $noticeTypes
        rushsignAccountId: $rushsignAccountId
    validate:
      - eq: [status_code, 200]

#校验签署权限
- api:
    def: checkSignAccess($flowId,$accountId,$operatorId,$redirectUrl)
    request:
      url: /v1/signflows/$flowId/accounts/$accountId/checkSignAccess
      method: POST
      headers: ${get_headers($app_id)}
      json:
        operatorId: $operatorId
        redirectUrl: $redirectUrl

    validate:
      - eq: [status_code, 200]

#获取签署人信息
- api:
    def: getSignerInfo($flowId,$accountId,$operatorId)
    request:
      url: /v1/signflows/$flowId/accounts/$accountId/getSignerInfo
      method: GET
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]

#补全信息
- api:
    def: completedSignerInfo($flowId,$accountId,$cardNo,$cardNoType,$name,$operatorId)
    request:
      url: /v1/signflows/$flowId/accounts/$accountId/completedSignerInfo
      method: POST
      headers: ${get_headers($app_id)}
      json:
        cardNo: $cardNo
        cardNoType: $cardNoType
        name: $name
        operatorId: $operatorId

    validate:
      - eq: [status_code, 200]

#信息反馈
- api:
    def: feedbackSignerInfo($flowId,$accountId,$cardNo,$cardNoType,$name,$operatorId)
    request:
      url: /v1/signflows/$flowId/accounts/$accountId/feedbackSignerInfo
      method: POST
      headers: ${get_headers($app_id)}
      json:
        cardNo: $cardNo
        cardNoType: $cardNoType
        name: $name
        operatorId: $operatorId

    validate:
      - eq: [status_code, 200]




#获取上传文件url  #contentMd5, contentType, fileName, fileSize必填
- api:
    def: get_uploadUrl1($contentMd5, $contentType, $fileName, $fileSize,$accountId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/getUploadUrl
      method: POST
      headers: ${get_headers($app_id)}
      json:
        contentMd5: $contentMd5
        contentType: $contentType
        fileName: $fileName
        fileSize: $fileSize
        accountId: $accountId
