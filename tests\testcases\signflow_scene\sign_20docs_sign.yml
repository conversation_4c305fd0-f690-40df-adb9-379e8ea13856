- config:
    name: 多文档盖多个印章签署
    ###def: sign_20docs_sign($personOid,$flowId)
    "request":
      "base_url": "${ENV(footstone_api_url)}"
      "headers":
        "Content-Type": "application/json"

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    extract:
      - p_sealId: content.data.seals.0.sealId
    validate:
      - eq: [ status_code, 200 ]

- test:
    name: 批量签署
    variables:
      - posBean: { addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0 }
      - addSignfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield2: ${gen_signfield_data_V2(0,0,$fileId2,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield3: ${gen_signfield_data_V2(0,0,$fileId3,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield4: ${gen_signfield_data_V2(0,0,$fileId4,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield5: ${gen_signfield_data_V2(0,0,$fileId5,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield6: ${gen_signfield_data_V2(0,0,$fileId6,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield7: ${gen_signfield_data_V2(0,0,$fileId7,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield8: ${gen_signfield_data_V2(0,0,$fileId8,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield9: ${gen_signfield_data_V2(0,0,$fileId9,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield10: ${gen_signfield_data_V2(0,0,$fileId10,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield11: ${gen_signfield_data_V2(0,0,$fileId11,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield12: ${gen_signfield_data_V2(0,0,$fileId12,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield13: ${gen_signfield_data_V2(0,0,$fileId13,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield14: ${gen_signfield_data_V2(0,0,$fileId14,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield15: ${gen_signfield_data_V2(0,0,$fileId15,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield16: ${gen_signfield_data_V2(0,0,$fileId16,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield17: ${gen_signfield_data_V2(0,0,$fileId17,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield18: ${gen_signfield_data_V2(0,0,$fileId18,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield19: ${gen_signfield_data_V2(0,0,$fileId19,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfield20: ${gen_signfield_data_V2(0,0,$fileId20,$flowId,$posBean,1,$personOid,$personOid,$p_sealId,1)}
      - addSignfields: [ $addSignfield1,$addSignfield2,$addSignfield3,$addSignfield4,$addSignfield5,$addSignfield6,$addSignfield7,$addSignfield8,$addSignfield9,$addSignfield10,$addSignfield11,$addSignfield12,$addSignfield13,$addSignfield14,$addSignfield15,$addSignfield16,$addSignfield17,$addSignfield18,$addSignfield19,$addSignfield20 ]
      - updateSignfields: [ ]
      - signfieldIds: [ $signfieldId1,$signfieldId2,$signfieldId3,$signfieldId4,$signfieldId5,$signfieldId6,$signfieldId7,$signfieldId8,$signfieldId9,$signfieldId10,$signfieldId11,$signfieldId12,$signfieldId13,$signfieldId14,$signfieldId15,$signfieldId16,
                        $signfieldId17,$signfieldId18,$signfieldId19,$signfieldId20 ]
      - accountId: $personOid
      - dingUser: { }
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - client_id: pc
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.flowStatus",2 ]