- config:
    name: 模板相关的测试
    parameters:
      - contentMd5-contentType-fileName-file_path:
          - ["${get_file_base64_md5($path_pdf)}", "application/pdf", "个人借贷合同.pdf", $path_pdf]
    variables:
      - fileSize: 111
      - templateName: "测试模板"
      #        - templateTypeId: "1"
      - path_pdf: "data/个人借贷合同.pdf"
      - pageNo: 1
      - pageSize: 100
      - fileMimeType: application/pdf
      #        - templateFormKeys: ["出借方","借款方"]
      #- {出借方: '1',借款方: '2018-12-21'}
    request:
      base_url: ${ENV(footstone_api_url)}
      headers:
        Content-Type: application/json
- test:
    name: 创建模板，获取模板列表
    variables:
      contentMd5: $contentMd5
      contentType: $contentType
      fileName: $fileName
      fileSize: $fileSize
      file_path: $file_path
      fileKey: $fileKey
      templateName: $templateName
      pageNo: $pageNo
      pageSize: $pageSize
    testcase: testcases/file_template/template_action.yml