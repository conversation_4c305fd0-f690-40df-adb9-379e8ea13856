- config:
    name: "延期收费证书接口校验"
    variables:
      - id_No: '331082199211223080'
      - Name: 梁贤红
      - app_id1: ${ENV(appid_cert)}
      - app_id2: ${ENV(appid_NO_cert)}
      - app_id3: ${ENV(appid_NO_postpone)}
      - id_Type: CRED_PSN_CH_IDCARD
      - certid_expired2: ${ENV(certid_expired2)}
      - certid_revoke: ${ENV(certid_revoke)}
    request:
      base_url: "${ENV(footstone_api_url)}"

- test:
    name: "证书id不存在"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: '1111'
      - validDuration: null
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书不存在"]


- test:
    name: "制证1"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId1: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

- test:
    name: "延期：appid为空"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: ''
      - certId: $certId1
      - validDuration: null
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "参数错误: 应用id不能为空"]

- test:
    name: "延期：appid不匹配"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: $app_id2
      - certId: $certId1
      - validDuration: null
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书不在此appId下"]

- test:
    name: "吊销成功"
    api: api/user/cert-service/revokeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "延期：证书id已吊销"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
      - validDuration: null
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书记录不存在"]


- test:
    name: "制证非区块链1年有效期"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId1: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

- test:
    name: "延期失败"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
      - validDuration: null
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "证书有效期90天内才允许延期"]


- test:
    name: "制证区块链1天有效期"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: '12345678'
      - validDuration: "1D"
      - useBlockChain: 'true'
    extract:
      - certId1: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

- test:
    name: "延期成功"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
      - validDuration: '1D'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "再次延期成功"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
      - validDuration: null
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "制证3"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id3
      - name: $Name
      - idType: $id_Type
      - idNumber: $id_No
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId3: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]

- test:
    name: "延期：通道不支持延期"
    api: api/user/cert-service/postponeCerts.yml
    variables:
      - app_id: $app_id3
      - certId: $certId3
      - validDuration: null
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "通道不支持延期"]
