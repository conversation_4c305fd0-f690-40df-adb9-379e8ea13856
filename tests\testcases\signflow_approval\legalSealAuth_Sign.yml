- config:
    name: 发起法人授权签署流程
    variables:
      appId1: ${ENV(BZQ-App-Id)}
      operator1: ${ENV(operator1111)}
      orgId1: ${ENV(orgid1111)}

- test:
    name: 发起法人授权签署流程
    variables:
      - app_id: $appId1
      - operator: $operator1
      - orgId: $orgId1
    api: api/signflow_approval/legalSealAuth_Sign.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 撤销法人授权申请
    variables:
      - app_id: $appId1
      - operator: $operator1
      - orgId: $orgId1
    api: api/signflow_approval/legalSealAuth_cancel.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
