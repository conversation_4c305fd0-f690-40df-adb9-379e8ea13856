- config:
    name: 实名签署，需要先进行个人账号的登陆，切换到标准签的账号
    #入参$mobileNew，返回$personOid_realname
    ###def: userLogin($mobileNew)
    base_url: ${ENV(base_url)}
    variables:
      - flowId: "1bae50909ad948549e9718b0f17d848c"
      - signerAccountId: "79ae64a1eed242a595fd3b34fb0d1e4e "
      - authKey: "authKeyxxx"
      - authorizedAccountId: ""
      - app_id: ${ENV(BZQ-App-Id)}
    output:
      - personOid_realname

- test:
    name: 校验手机号/邮箱是否已经注册
    api: api/user/accounts/get_checkMobileEmail.yml
    variables:
      - principal: $mobileNew
    extract:
      - personOid_realname: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 登录申请
    api: api/user/accounts/login_apply.yml
    variables:
      - path: "authCode"
      - mobileNew: $mobileNew
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 登录验证
    api: api/user/accounts/commit.yml
    variables:
      - loginParams: {"endpoint": "PC","expireTime": 0}
      - credentials: "123456"
      - path: "authCode"
      - mobileNew: $mobileNew
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]


- test:
    name: 校验手机号/邮箱是否已经注册
    teardown_hooks:
      - ${hook_sleep_n_secs(1)}
    api: api/user/accounts/get_checkMobileEmail.yml
    variables:
      - principal: $mobileNew
    extract:
      - personOid_realname: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 根据authKey获取免登签署地址
    #    teardown_hooks:
    #      - ${hook_sleep_n_secs(1)}
    api: api/signflow/signflows/getUrlWithAuthkey-api.yml
    variables:
      - flowId: $flowId
      - signerAccountId: $signerAccountId
      - authKey: $authKey
      - needShortLink: $needShortLink
    #    extract:
    #      - personOid_realname: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - contains: ["content.data.data.url", "http"]
      - contains: ["content.data.data.shortUrl", "http"]
