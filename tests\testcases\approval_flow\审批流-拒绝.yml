- config:
    name: "审批流用例-二级审批"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}
      - yuzan_stan: e4c6fbc2f2754633a7ecadb85fb24ac6
      - liangxianhong_stan: 32f14bdbb78445feaec6a695f32278cf

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId

- test:
    name: "gan_appid_xuanyuan下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构-泗县深泉纯净水有限公司"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建机构-东营伟信建筑安装工程有限公司"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: dongying201211
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId


- test:
    name: "获取标准签下的泗县纯净水公司的oid"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - orgId: $dongying_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询泗县纯净水公司实名组织下的企业印章"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - orgId: 8fbe5926547e40149978fb8c5a448394
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询东营伟信实名组织下的企业印章"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - orgId: bb4364e663ad43ee9381e65232b5ae1f
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建签署流程"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - autoArchive: false
      - businessScene: 审批流-单个二级审批-盖章-一级拒绝-盖章-一级同意-二级拒绝-盖章-一级同意-二级同意
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: ''}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $dongying_organize_id
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "个人-添加成功个人签名域-指定签名域 可以添加位置信息"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - flowId: $sign_flowId
      - signfields: [{"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "创建第一个手动签署区创建第一个手动签署区"},"fileId":$fileId1,"signerAccountId":$accountId_gan,"actorIndentityType":"2","authorizedAccountId":$dongying_organize_id,"assignedPosbean":false,"order":1,"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldIds0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 拒绝签署
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - tenant_Id: "7efbb66432874aa69ec42d723816d764"
      - refuseReason: 拒绝签署
      - accountId: $accountId_gan
      - flowId: $sign_flowId
    api: api/signflow/signflows/signersAccountIdRefuse.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(2)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_gan
      - urlType: 0
      - multiSubject: true
      - organizeId:
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}

- test:
    name: 查询时间流信息-拒签
    api: api/v3/signflows/flowId/timeStream.yml
    variables:
      - flowId: $sign_flowId
      - params: queryAccountId=$accountId_gan
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - str_eq: ["content.data.signers.0.operateType",6]

      #- test:
      #    name:  获取意愿链接，主体为王瑶济,
      #    api: api/signflow/signflows/identifyUrlWillWithJson.yml
      #    variables:
      #      - flowId: $sign_flowId
      #      - accountId: $yuzan_stan
      #      - app_id: ${ENV(gan_appid_xuanyuan)}
      #      #      - approvalId: $approveFlowId1
      #      - json: {
      #         "bizCtxIds":
      #        [
      #        $flowId     #flowid
      #        ],
      #    "bizType": "",
      #
      #    "redirectUrl": "www.baidu.com",
      #    "clientType": "PC",
      #    "wukongOid": $accountId,
      #    "app_id":  $app_id,
      #    "space": $accountId,
      #    "scene": "2",
      #    "willTypeHided": "5"
      #         }
      #    teardown_hooks:
      #      - ${hook_sleep_n_secs(5)}
      #    extract:
      #      - bizId1: content.data.bizId
      #    validate:
      - eq: ["content.code",0]





      #- test:
      #     name: 拒绝审批
      #     variables:
      #       - app_id: ${ENV(gan_appid_xuanyuan)}
      #       #       - refuseReason: 二级用印审批-第一个拒绝
      #       - approvalId: $approveFlowId1
      #       - accountId: $yuzan_stan
      #     api: api/signflow/approvalflows/approvalflowsRefuse.yml
      #     teardown_hooks:
      #       - ${hook_sleep_n_secs(5)}
      #     validate:
      - eq: ["content.code",0]