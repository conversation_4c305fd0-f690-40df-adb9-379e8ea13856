- config:
    name: 获取实名意愿校验
    base_url: ${ENV(footstone_api_url)}
    variables:
      - language: zh-CN
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_Id1: ${ENV(appId_xuanyuan)}
      - app_Id2: ${ENV(wukong_realname_not_in_white)}
      - app_Id3: ${ENV(solution_appid)}
      - path_pdf: data/个人借贷合同.pdf
      - mobile: "***********"
      - name: d${get_randomString()}
      - idNo: 331082199211223080
      - thirdPartyUserId1: a${get_randomString()}
      - thirdPartyUserId2: b${get_randomString()}
      - thirdPartyUserId3: c${get_randomString()}
      - mobile: "***********"
      - name1: esigntest东营伟信建筑安装工程有限公司1
      - idNo1: 91370502060407048H
      - nickname: ''
      - operator: ${getOid($mobile)}
      - fileId1: ${get_file_id($app_Id1,$path_pdf)}
      - fileId2: ${get_file_id($app_Id2,$path_pdf)}
      - fileId3: ${get_file_id($app_Id3,$path_pdf)}

- test:
    name: 创建账号，轩辕套餐
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo
      - mobile: $mobile
      - name: $thirdPartyUserId1
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid1: content.data.accountId


- test:
    name: 创建企业账号，轩辕套餐
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo1
      - name: $thirdPartyUserId2
      - thirdPartyUserId: $thirdPartyUserId2
      - creator: $oid1
    extract:
      - orgId1: content.data.orgId

- test:
    name: 创建企业账号2，轩辕套餐
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo1
      - name: '自动化测试企业'
      - thirdPartyUserId: $thirdPartyUserId3
      - creator: $oid1
    extract:
      - orgId2: content.data.orgId


- test:
    name: 获取实名地址场景：数据准备：一步创建_轩辕实名企业签
    variables:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $oid1
      - signerAccountId: $oid1
      - app_id: $app_Id1
      - fileId: $fileId1
      - autoInitiate: true
      - signerAuthorizerId: $orgId1
      - signerAuthorizerType: 1
      - signerSignRoles: 1
      - name_org: ''
      - signerAuthorizerId: $orgId1

    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId1: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 获取实名地址场景：轩辕实名：获取个人实名地址
    variables:
      - flowId: $flowId1
      - accountId: $oid1
      - app_id1: $app_Id1
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.type", "1"]


- test:
    name: 获取实名地址场景：轩辕实名：获取企业实名地址
    variables:
      - flowId: $flowId1
      - accountId: $oid1
      - app_id1: $app_Id1
      - scene: 1
      - saleSchemaId: 18
      - orgId: $orgId1
    api: api/signflow/signflows/identifyUrl_org.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 获取实名地址场景：数据准备：一步创建_轩辕实名企业签-企业未实名
    variables:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $oid1
      - signerAccountId: $oid1
      - app_id: $app_Id1
      - fileId: $fileId1
      - autoInitiate: true
      - signerAuthorizerId: $orgId2
      - signerAuthorizerType: 1
      - signerSignRoles: 1
      - name_org: ''
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId2: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 获取实名地址场景：轩辕实名：获取企业实名地址
    variables:
      - flowId: $flowId2
      - accountId: $oid1
      - app_id1: $app_Id1
      - scene: 1
      - saleSchemaId: 18
      - orgId: $orgId1
    api: api/signflow/signflows/identifyUrl_org.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 创建账号，非轩辕套餐
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: $idNo
      - mobile: $mobile
      - name: $name
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid3: content.data.accountId


- test:
    name: 创建企业账号，非轩辕套餐
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: $idNo1
      - name: $name1
      - thirdPartyUserId: $thirdPartyUserId2
      - creator: $oid3
    extract:
      - orgId3: content.data.orgId


- test:
    name: 获取实名地址场景：数据准备：一步创建_悟空实名企业签
    variables:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $oid1
      - signerAccountId: $oid3
      - app_id: $app_Id2
      - fileId: $fileId1
      - autoInitiate: true
      - signerAuthorizerId: $orgId3
      - signerAuthorizerType: 1
      - signerSignRoles: 1
      - name_org: ''
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId3: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 获取实名地址场景：悟空实名：获取个人实名地址
    variables:
      - flowId: $flowId3
      - accountId: $oid3
      - app_id1: $app_Id2
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.type", "1"]


- test:
    name: 获取实名地址场景：悟空实名：获取企业实名地址
    variables:
      - flowId: $flowId3
      - accountId: $oid3
      - app_id1: $app_Id2
      - scene: 1
      - saleSchemaId: 18
      - orgId: $orgId3
    api: api/signflow/signflows/identifyUrl_org.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: 创建账号，轩辕套餐-解决方案
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id3
      - idNo: $idNo
      - mobile: $mobile
      - name: $name
      - thirdPartyUserId: $thirdPartyUserId2
    extract:
      - oid4: content.data.accountId


- test:
    name: 获取实名地址场景：解决方案：一步创建_轩辕实名个人签
    variables:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $oid4
      - signerAccountId: $oid4
      - app_id: $app_Id3
      - fileId: $fileId3
      - autoInitiate: true
      - signerAuthorizerId: $oid4
      - signerAuthorizerType: 0
      - signerSignRoles: 1
      - name_org: ''
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId4: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 获取实名地址场景：轩辕实名：获取个人实名地址
    variables:
      - flowId: $flowId4
      - accountId: $oid4
      - app_id1: $app_Id3
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.type", "1"]

- test:
    name: 数据准备：一步创建_轩辕实名企业签-只有企业名称
    variables:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $oid1
      - signerAccountId: $oid1
      - app_id: $app_Id1
      - fileId: $fileId1
      - autoInitiate: true
      - signerAuthorizerId: ''
      - signerAuthorizerType: 1
      - signerSignRoles: 1
      - name_org: $thirdPartyUserId1
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId5: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕实名：获取签署印章列表
    variables:
      - flowId: $flowId5
      - signerAccountId: $oid1
      - app_id: $app_Id1
      - loginAccountId: $operator
    api: api/signflow/signflows/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 查询流程详情
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId5
      - queryAccountId: $oid1
      - operatorid: $oid1
      - app_id: $app_Id1
    extract:
      - orgid_1: content.data.signers.0.signerAuthorizedAccountId
    validate:
      - eq: [status_code, 200]

- test:
    name: 轩辕实名：获取企业实名地址
    variables:
      - flowId: $flowId5
      - accountId: $oid1
      - app_id1: $app_Id1
      - scene: 1
      - saleSchemaId: 18
      - orgId: $orgid_1
    api: api/signflow/signflows/identifyUrl_org.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 数据准备：一步创建_轩辕实名企业签-只有企业名称-企业已实名
    variables:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $oid1
      - signerAccountId: $oid1
      - app_id: $app_Id1
      - fileId: $fileId1
      - autoInitiate: true
      - signerAuthorizerId: ''
      - signerAuthorizerType: 1
      - signerSignRoles: 1
      - name_org: 'esigntest东营伟信建筑安装工程有限公司'
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId6: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId6
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
- test:
    name: 轩辕实名：获取签署印章列表
    variables:
      - flowId: $flowId6
      - signerAccountId: $oid1
      - loginAccountId: $operator
      - app_id: $app_Id1
    api: api/signflow/signflows/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 查询流程详情
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId6
      - queryAccountId: $oid1
      - operatorid: $oid1
      - app_id: $app_Id1
    extract:
      - orgid_2: content.data.signers.0.signerAuthorizedAccountId
    validate:
      - eq: [status_code, 200]

      #- test:
      #    name: 轩辕实名：获取企业实名地址
      #    variables:
      #      - flowId: $flowId6
      #      - accountId: $oid1
      #      - app_id1: $app_Id1
      #      - scene: 1
      #      #      - saleSchemaId: 18
      #      - orgId: $orgid_2
      #    api: api/signflow/signflows/identifyUrl_org.yml
      #    validate:
#      - eq: ["content.code",1435303]
#      - eq: ["content.message", 企业账号已存在，且已关联经办人]

- test:
    name: 获取实名地址场景：轩辕实名：获取个人实名地址
    variables:
      - flowId: $flowId6
      - accountId: $operator
      - app_id1: $app_Id1
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.type", "1"]



- test:
    name: 一步创建非灰度批量发起流程
    variables:
      - createWay: 'normal_batch'
      - businessScene: "一步创建非灰度批量发起流程"
      - initiatorAccountId: $oid1
      - signerAccountId: $oid1
      - app_id: $app_Id1
      - fileId: $fileId1
      - autoInitiate: true
      - signerAuthorizerId: $orgId1
      - signerAuthorizerType: 1
      - signerSignRoles: 1
      - name_org: ''
      - signerAuthorizerId: $orgId1
      - seperateSigner: true
    api: api/signflow/signflows/createAllAtOnceV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code", 0]


- test:
    name: 账号注销场景：数据准备：一步创建_轩辕实名企业签
    variables:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $oid1
      - signerAccountId: $oid1
      - app_id: $app_Id1
      - fileId: $fileId1
      - autoInitiate: true
      - signerAuthorizerId: $orgId1
      - signerAuthorizerType: 1
      - signerSignRoles: 1
      - name_org: ''
      - signerAuthorizerId: $orgId1

    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId7: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]


- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId7
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#- test:
#    name: "注销组织"
#    api: api/user/organizations/delete_organizations.yml
#    variables:
#      - app_id: $app_Id1
#      - organId: $orgId1
#      - operatorid: $oid1
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#      - eq: ["content.message","成功"]

- test:
    name: 获取签署链接
    api: api/signflow/signflows/getManySignUrl.yml
    variables:
      - flowId: $flowId7
      - accountId: $oid1
      - urlType: 0
      - multiSubject: false
      - organizeId: $orgId1
      - app_id: $app_Id1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: "注销个人账号"
    api: api/user/accounts/delete_accounts.yml
    variables:
      - app_id: $app_Id1
      - accountId: $oid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: 获取签署链接
    api: api/signflow/signflows/getManySignUrl.yml
    variables:
      - flowId: $flowId7
      - accountId: $oid1
      - urlType: 0
      - multiSubject: true
      - organizeId: ''
      - app_id: $app_Id1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]