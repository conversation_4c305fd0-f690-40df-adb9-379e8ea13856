- config:
    name: ONLINEBUG-3978:下载文件权限校验
    base_url: ${ENV(footstone_api_url)}
    variables:
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_Id1: ${ENV(appId_xuanyuan)}
      - app_Id2: ${ENV(appId_wukong)}
      - path_pdf: data/个人借贷合同.pdf
      - mobile1: "***********"
      - mobile2: "***********"
      - name1: 梁贤红
      - idNo1: 331082199211223080
      - thirdPartyUserId1: atest001-lxh
      - thirdPartyUserId2: btest002-test
      - name2: esigntest东营伟信建筑安装工程有限公司
      - idNo2: 91370502060407048H
      - operator: ${getOid($mobile1)}
      - batchSign: false
- test:
    name: 创建流程：轩辕套餐
    variables:
      - app_id: $app_Id1
      - autoArchive: False
      - businessScene: 创建流程：轩辕套餐
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: 创建账号，轩辕套餐
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo1
      - mobile: $mobile1
      - name: $name1
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid1: content.data.accountId

- test:
    name: 创建账号非参与人
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: ''
      - mobile: $mobile2
      - name: '测试'
      - thirdPartyUserId: $thirdPartyUserId2
    extract:
      - oid11: content.data.accountId


- test:
    name: 创建个人模板印章，轩辕套餐
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id1
      - accountId: $oid1
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId1: content.data.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 获取上传文件url
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $oid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 添加流程文档，轩辕套餐
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 添加流程签署区
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - posBean: {}
      - signfield: ${gen_signfield_data_V2(0,0,$fileId1,$flowId1,$posBean,0,$oid1,,,)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人-运营商三要素意愿认证-意愿发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - app_id: $app_Id1
      - accountId: $operator
      - flowId: $flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 回填三要素验证码
    variables:
      - app_id: $app_Id1
      - authcode: 123456
      - accountId: $operator
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - accountId: $operator
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield:  {actorIndentityType: 0,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $operator ,autoExecute: false, extendFieldDatas: {},fileId: $fileId1, flowId: $flowId1,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId1,sealType: 1,signType: 1,signerAccountId: $operator}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $operator
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    extract:
      - serviceId: content.data.serviceId
    validate:
      - eq: [status_code, 200]

- test:
    name: 融合层签署场景-查询异步签署结果
    api: api/signflow/signflows/requsetResult.yml
    variables:
      - app_id: $app_Id1
      - service_Id: $serviceId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 轩辕个人签签署人文件下载配置
    api: api/signflow/signflows/signflowsConfig_para.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - queryAccountId: $oid1
      - querySpaceAccountId: $oid1
      - menuId: ''
      - scenario: ''
      - operator_id: $oid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.data.pageConfig.configList.1.name","下载"]
- test:
    name: 轩辕个人签签署人文件下载成功
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - queryAccountId: $oid1
      - querySpaceAccountId: $oid1
      - menuId: ''
      - operator_id: $oid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]

- test:
    name: 轩辕个人签操作人文件下载配置
    api: api/signflow/signflows/signflowsConfig_para.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - queryAccountId: $operator
      - querySpaceAccountId: $operator
      - menuId: ''
      - scenario: ''
      - operator_id: $operator
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.data.pageConfig.configList.1.name","下载"]
      #- test:
      #    name: 轩辕个人签操作人+签署人文件下载配置
      #    api: api/signflow/signflows/signflowsConfig_para.yml
      #    variables:
      #      - app_id: $app_Id1
      #      - flowId: $flowId1
      #      - queryAccountId: $operator
      #      - querySpaceAccountId: $operator,$oid1
      #      - menuId: ''
      #      - scenario: ''
      #    validate:
      - eq: ["content.code",0]
      - contains: ["content.data.pageConfig.configList.1.name","下载"]

- test:
    name: 轩辕个人签操作人文件下载成功
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - queryAccountId: $operator
      - querySpaceAccountId: $operator
      - menuId: ''
      - operator_id: $operator
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]
- test:
    name: 轩辕个人签非参与人文件下载配置
    api: api/signflow/signflows/signflowsConfig_para.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - queryAccountId: $oid11
      - querySpaceAccountId: $oid11
      - menuId: ''
      - scenario: ''
      - operator_id: $oid11
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - not_equals: ["content.data.pageConfig.configList.1.name","下载"]
- test:
    name: 轩辕个人签非参与人文件下载失败
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - queryAccountId: $oid11
      - querySpaceAccountId: $oid11
      - menuId: ''
      - operator_id: $oid11
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435208]
      - contains: ["content.message",当前用户无权限]
- test:
    name: 创建流程：悟空
    variables:
      - app_id: $app_Id2
      - autoArchive: False
      - businessScene: 创建流程：悟空
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: 创建账号，悟空
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: $idNo1
      - mobile: $mobile1
      - name: $name1
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid2: content.data.accountId


- test:
    name: 创建企业账号，悟空
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: $idNo2
      - name: $name2
      - thirdPartyUserId: $thirdPartyUserId2
      - creator: $oid2
    extract:
      - orgId2: content.data.orgId

- test:
    name: 创建个人模板印章，悟空
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id2
      - accountId: $oid2
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId2: content.data.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 创建企业模板印章，悟空
    api: api/seals/seals/create_c_template_seal_appid.yml
    variables:
      - app_id: $app_Id2
      - orgId: $orgId2
      - color: "BLUE"
      - height: 159
      - width: 159
      - alias: "企业章-蓝色"
      - type: "TEMPLATE_OVAL"
      - central: "NONE"
      - hText: "财务"
      - qText: "2019年"

    extract:
      - sealId_org2: content.data.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 获取上传文件url
    variables:
      - app_id: $app_Id2
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $oid2
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 添加流程文档，悟空
    variables:
      - flowId: $flowId2
      - app_id: $app_Id2
      - doc1: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加流程签署区
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
      - posBean: {}
      - signfield: {actorIndentityType: 1,assignedItem: 0,assignedPosbean: false,assignedSeal: false,authorizedAccountId: $orgId2 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId2, flowId: $flowId2,order: 1,posBean: $posBean,sealFileKey: "",sealId: '',sealType: 1,signType: 0,signerAccountId: $oid2}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId0: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
      - accountId: $oid2
      - posBean: {addSignTime: false, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield:  {actorIndentityType: 1,assignedItem: 0,assignedPosbean: true,assignedSeal: true,authorizedAccountId: $orgId2 ,autoExecute: false, extendFieldDatas: {},fileId: $fileId2, flowId: $flowId2,order: 1,posBean: $posBean,sealFileKey: "",sealId: $sealId_org2,sealType: 1,signType: 1,signerAccountId: $oid2}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $oid2
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    extract:
      - serviceId: content.data.serviceId
    validate:
      - eq: [status_code, 200]

- test:
    name: 融合层签署场景-查询异步签署结果
    api: api/signflow/signflows/requsetResult.yml
    variables:
      - app_id: $app_Id2
      - service_Id: $serviceId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 悟空文件下载配置
    api: api/signflow/signflows/signflowsConfig_para.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
      - queryAccountId: $oid2
      - querySpaceAccountId: $orgId2
      - menuId: ''
      - scenario: ''
      - operator_id: $oid2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.data.pageConfig.configList.1.name","下载"]

- test:
    name: 悟空文件下载成功
    api: api/signflow/flowFile/pack.yml
    variables:
      - app_id: $app_Id2
      - flowId: $flowId2
      - queryAccountId: $oid2
      - querySpaceAccountId: $orgId2
      - menuId: ''
      - operator_id: $oid2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]
