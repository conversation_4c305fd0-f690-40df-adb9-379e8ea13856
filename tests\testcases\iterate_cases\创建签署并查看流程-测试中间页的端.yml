- config:
        name: "0528接口测试用例"
        base_url: ${ENV(footstone_api_url)}
        variables:
            - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
            -   path_pdf: data/个人借贷合同.pdf
            -   path_pdf1: data/劳动合同书.pdf
            -   path_pdf2: data/劳动合同书12.pdf
            -   group:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   fileId1: ${get_file_id($app_id,$path_pdf1)}
            -   thirdPartyUserId1: third1${get_randomString()}
#         - app_secret: 15367ee02f9d49f805441e52ee3e1202

#######/v1/signflows/{flowId}/signfields/handSign      关于getSignSeals接口的校验
###############################悟空非实名签################################
-   test:
        name: "获取app_id_tengqing_PaaS_with_willing_and_real_name的appSecret"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
        api: api/iterate_cases/getAppInfo.yml
        extract:
            -   app_secret: content.appSecret
        validate:
            -   eq: ["status_code", 200]


-  test:
        name: "创建签署人账号 - 陈凯丽"
        variables:
            - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            - app_secret: $app_secret
            - thirdPartyUserId: $thirdPartyUserId_person_ckl
            - name: 陈凯丽
            - idNumber: 362323199201061068
            - idType: "CRED_PSN_CH_IDCARD"
            - email:
            - mobile: ***********
        api: api/iterate_cases/p_createByThirdPartyUserId.yml
        extract:
            - accountId_ckl: content.data.accountId

-  test:
        name: "创建签署人账号 - gan"
        variables:
            - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            - app_secret: $app_secret
#            - thirdPartyUserId: zzzzzzzzzzzzzzzzz123
            - thirdPartyUserId: $thirdPartyUserId1
            - name: 甘舰戈
            - idNumber: ******************
            - idType: "CRED_PSN_CH_IDCARD"
            - email:
            - mobile: ***********
        api: api/iterate_cases/p_createByThirdPartyUserId.yml
        extract:
            - accountId_gan: content.data.accountId
            - yuzan_accountId: content.data.accountId

#-   test:
#        name: "app_id_tengqing_PaaS_with_willing_and_real_name下创建签署人王瑶济账号"
#        variables:
#            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
#            -   app_secret: $app_secret
#            -   thirdPartyUserId: yuzan201915222421234
#            -   name: 王瑶济
#            -   idNumber: 320721199305232634
#            -   idType: CRED_PSN_CH_IDCARD
#            -   email:
#            -   mobile: ***********
#        api: api/iterate_cases/p_createByThirdPartyUserId.yml
#        extract:
#            -   yuzan_accountId: content.data.accountId

-   test:
        name: "创建梁贤红账号"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   thirdPartyUserId: caogu20191111411321213
            -   name: 梁贤红
            -   idNumber: 331082199211223080
            -   idType: "CRED_PSN_CH_IDCARD"
            -   email:
            -   mobile: ***********
        api: api/iterate_cases/p_createByThirdPartyUserId.yml
        extract:
            -   caogu_accountId: content.data.accountId

-   test:
        name: "使用caogu_accountId创建机构"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   thirdPartyUserId: sixian201911141132
            -   creator: $caogu_accountId
            -   idNumber: 91341324MA2RMB1W3T
            -   idType: CRED_ORG_USCC
            -   name: 泗县深泉纯净水有限公司
            -   orgLegalIdNumber: 331082199211223080
            -   orgLegalName: 梁贤红
        api: api/iterate_cases/o_createByThirdPartyUserId.yml
        extract:
            -   sixian_organize_id: content.data.orgId



-   test:
        name: "获取对应的实名组织详情"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   orgId: $sixian_organize_id
        extract:
            -   standard_sixian_organize_id: content.data.base.ouid
        api: api/iterate_cases/getFinalRealnameOrgInfo.yml
        validate:
            -   eq: ["content.code",0]

#-   test:
#        teardown_hooks:
#            - ${teardown_seals($response)}
#        name: "查询实名组织下的企业印章"
#        variables:
#            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
#            -   app_secret: $app_secret
#            -   orgId: $standard_sixian_organize_id
#        api: api/iterate_cases/searchOrganizationsSeal.yml
#        extract:
#            -   org_sealId1: org_sealId1
#            -   org_sealId2: org_sealId2
#            -   org_sealId3: org_sealId3
#            -   legal_sealId1: legal_sealId1
#            -   cancellation_sealId: cancellation
#        validate:
#            -   eq: ["content.code",0]
#            -   eq: ["content.message", "成功"]

#-   test:
#        name: "查询企业印章"
#        variables:
#            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
#            -   app_secret: $app_secret
#            -   orgId: $sixian_organize_id
#        api: api/iterate_cases/searchOrganizationsSeal.yml
#        extract:
#            -   org_open_sealId1: content.data.seals.0.sealId
#        validate:
#            -   eq: ["content.code",0]
#            -   eq: ["content.message", "成功"]

-   test:
        name: "创建签署流程"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   autoArchive: false
            -   businessScene: 悟空非实名签校验add-update字段
            -   configInfo: { noticeDeveloperUrl: 'http://172.20.62.157/simpleTools/notice/', noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3,7,8'}
            -   contractValidity: ""
            -   extend: {}
            -   initiatorAccountId: $accountId_gan
            -   payerAccountId: ""
            -   signValidity:   *************
#            -   signValidity: *************
            -   initiatorAuthorizedAccountId: ""
        api: api/iterate_cases/signflowsCreate.yml
        extract:
            -   sign_flowId: content.data.flowId
        validate:
            -   len_gt: ["content.data.flowId",0]
            -   eq: ["status_code", 200]

-   test:
        name: "添加1份流程文档"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
            -   flowId: $sign_flowId
        api: api/iterate_cases/addFlowDoc.yml
        validate:
            -   eq: ["content.code",0]

-   test:
        name: "个人-添加成功个人签名域-自由签名域"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   flowId: $sign_flowId
            -   signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"0","authorizedAccountId":$yuzan_accountId,"assignedPosbean":false,"order":1,"sealType":null,"sealId":"","signType":1},
]
        api: api/iterate_cases/signfieldsCreate_handSign.yml
        extract:
            - signfieldIds0: content.data.signfieldBeans.0.signfieldId
        validate:
            - eq: ["status_code", 200]

-   test:
        name: "添加企业签名域"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   flowId: $sign_flowId
            -   signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"sealType":null,"sealId":"","signType":0,"posBean":{"addSignTime":true,"posPage":"1","posX":10,"posY":10,"location": "第一个用户手动签，第一个用户手动签"}},
]
        api: api/iterate_cases/signfieldsCreate_handSign.yml
        extract:
            - signfieldIds0: content.data.signfieldBeans.0.signfieldId
        validate:
            - eq: ["status_code", 200]


-   test:
        name: "开启签署流程"
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   flowId: $sign_flowId
        api: api/iterate_cases/signflowsStart.yml
        validate:
            -   eq: ["content.code",0]


-   test:
        name: "查看合同详情"
        api: api/signflow/signflows/flowsDetailV2.yml
        variables:
            -   app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
            -   app_secret: $app_secret
            -   flowId: $sign_flowId
            -   queryAccountId: 3986d7544d9848f186ee9c5dcd342de1
            -   operatorid: 3986d7544d9848f186ee9c5dcd342de1
        extract:
#            -   content_data: content.data
#            -   content_data_configInfo: content.data.configInfo
            -   processId: content.data.processId
        validate:
            -   eq: ["status_code", 200]
            -   eq: ["content.data.flowStatus",1]

-   test:
        name: "获取签署链接" #查看的中间页
        variables:
            -   app_secret: $app_secret
            -   flowId: $sign_flowId
            -   accountId: $yuzan_accountId
            -   urlType: 0
            -   multiSubject: true
            -   organizeId: $yuzan_accountId
            -   compressContext: true
        api: api/iterate_cases/executeUrl.yml
        extract:
            -   shortUrl: content.data.shortUrl
            -   code: content.code
            -   message: content.message
        validate:
            -   eq: ["content.code",0]
