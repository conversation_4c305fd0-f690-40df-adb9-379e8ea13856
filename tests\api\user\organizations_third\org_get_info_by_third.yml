#def: org_get_info_by_third($thirdPartyUserId, $thirdPartyUserType)
request:
  url: ${ENV(footstone_user_url)}/v1/organizations/getByThirdId?thirdPartyUserId=$thirdPartyUserId&thirdPartyUserType=$thirdPartyUserType
  method: GET
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
    X-Tsign-Open-Auth-Mode: $auth_mode
validate:
  - eq: [status_code, 200]
#账号注销
