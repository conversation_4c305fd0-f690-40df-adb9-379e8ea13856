- config:
    name: 添加签署区拆分校验
    ###def: signfields_simple_check()
    "request":
      "base_url":
      "headers":
        "Content-Type": "application/json"

- test:
    name: 数据准备：创建流程
    variables:
      - autoArchive: False
      - businessScene: 添加签署区拆分场景$now_time
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 数据准备：添加流程文档
    variables:
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 数据准备：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-fileId为空，失败
    variables:
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'order':1, 'signType':'', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-posBean为空json，失败
    variables:
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'order':1, 'signType':'', 'posBean':{}, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-posBean不传，失败
    variables:
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'order':1, 'signType':'', 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#- test:
#    name: 对接平台自动签署-sealId为空，失败

- test:
    name: 对接平台自动签署-非必填项不传值，默认单页签署，成功
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':100}
      - signfield: {'fileId':$fileId1,  'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=1单页签，posPage为多页，失败
    variables:
      - posBean: {'posPage':'1-2','posX':100, 'posY':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'1', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=1单页签，posPage为单页，成功
    variables:
      - posBean: {'posPage':'1','posX':100, 'posY':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'1', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=1单页签，posPage超出页数，失败
    variables:
      - posBean: {'posPage':'7','posX':100, 'posY':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'1', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=1单页签，posPage为空，失败
    variables:
      - posBean: {'posPage':'','posX':100, 'posY':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'1', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=1单页签，posX为空，失败
    variables:
      - posBean: {'posPage':'1','posY':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'1', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=1单页签，posY为空，失败
    variables:
      - posBean: {'posPage':'1','posX':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'1', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=2骑缝签，posPage为空，失败
    variables:
      - posBean: {'posPage':'','posX':100, 'posY':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'2', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=2骑缝签，posPage为多页1-2，成功
    variables:
      - posBean: {'posPage':'1-2','posX':100, 'posY':100}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'2', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=2骑缝签，posPage为多页1,2，成功
    variables:
      - posBean: {'posPage':'1,2','posX':100, 'posY':200}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'2', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=2骑缝签，posX为空，成功
    variables:
      - posBean: {'posPage':'1-2','posX':'', 'posY':300}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'2', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=2骑缝签，posY为空，成功
    variables:
      - posBean: {'posPage':'1-2','posX':'100', 'posY':''}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'2', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-signType=3不存在，失败
    variables:
      - posBean: {'posPage':'1-2','posX':'100', 'posY':''}
      - signfield: { 'fileId':$fileId1, 'order':1, 'signType':'3', 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#印章ID和平台不一致