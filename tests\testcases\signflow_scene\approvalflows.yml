- config:
    name: 空间签署审批
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id)}2
      - client_id: pc
    ###def: approvalflows($fileId1,$orgOid1,$personOid1,$accountId)
    request:
      base_url: "${ENV(footstone_api_url)}"
      headers:
        Content-Type: "application/json"

- test:
    name: 普通用户创建流程
    variables:
      - autoArchive: True
      - businessScene: 企业空间创建流程$now_time
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: , signPlatform: '1,2,3'}
      - extend: {}
      - initiatorAuthorizedAccountId: $orgOid1
      - initiatorAccountId: $personOid1
    api: api/signflow/signflows/signflowsCreateForOrganize.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 批量添加流程文档：单文档
    variables:
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 批量添加流程签署区
    variables:
      - posBean1: {addSignTime: True, key: '', posPage: '1', posX: 100, posY: 600, qrcodeSign: False, width: 159}
      - posBean2: {addSignTime: True, key: '', posPage: '2', posX: 100, posY: 200, qrcodeSign: False, width: 159}
      - signfield1: ${gen_signfield_data_V2(1,0,$fileId1,$flowId,$posBean1,1,$personOid1,$orgOid1,,)}
      - signfield2: ${gen_signfield_data_V2(1,0,$fileId1,$flowId,$posBean2,1,$personOid1,$orgOid1,,)}
      # - signfield2: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean2,1,$personOid1,,,)}
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
      - signfieldId2: content.data.signfieldIds.1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询企业印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_c_seals.yml
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
    output:
      - o_sealId

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
    output:
      - p1_sealId

- test:
    name: 批量更新流程签署区
    variables:
      - posBean1: {addSignTime: False, key: '', posPage: '1', posX: 100, posY: 600, qrcodeSign: False, width: 159}
      - signfield1: ${gen_signfield_update_data($orgOid1, , $posBean1, ,$o_sealId,$signfieldId1)}
      - posBean2: {addSignTime: False, key: '', posPage: '2', posX: 100, posY: 200, qrcodeSign: False, width: 159}
      - signfield2: ${gen_signfield_update_data($orgOid1, , $posBean2, ,$o_sealId,$signfieldId2)}
      # - signfield2: ${gen_signfield_update_data($personOid1, , $posBean2, ,$p1_sealId,$signfieldId2)}
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 批量执行流程签署区
    variables:
      - signfieldIds: [$signfieldId1,$signfieldId2]
    api: api/signflow/signfields/signfieldsExecute.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]
      - eq: ["content.data.executeResults.1.failedReason",null]

- test:
    name: 查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - approvalId1: content.data.signfields.0.approvalFlowId
      - approvalId2: content.data.signfields.1.approvalFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
    output:
      - approvalId1
      - approvalId2

- test:
    name: 查询审批信息
    api: api/signflow/approvalflows/approvalflowsQuery.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.executeResults.0.failedReason",null]

- test:
    name: 查询审批进度信息
    api: api/signflow/approvalflows/approvalflowsProgress.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]

- test:
    name: 同意审批
    api: api/signflow/approvalflows/approvalflowsAgree.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(8)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - approvalId: content.data.signfields.0.approvalFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.signfields.0.status", 4]
      - eq: ["content.data.signfields.1.status", 4]

- test:
    name: 查询审批详细信息
    api: api/signflow/approvalflows/approvalflowsDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]



