#def: org_update_by_third($thirdPartyUserId, $thirdPartyUserType, $name)
request:
  url: ${ENV(footstone_user_url)}/v1/organizations/updateByThirdId?thirdPartyUserId=$thirdPartyUserId&thirdPartyUserType=$thirdPartyUserType
  method: POST
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
    X-Tsign-Open-Auth-Mode: $auth_mode
  json:
    {
      "name": $name
    }
validate:
  - eq: [status_code, 200]