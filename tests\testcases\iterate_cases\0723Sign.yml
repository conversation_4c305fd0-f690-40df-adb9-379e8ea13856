- config:
    name: "0723迭代测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - group: ''
      - batchSign:

      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - path_pdf: 个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US

- test:
    name: "创建签署人账号"
    variables:
      - thirdPartyUserId: autotest-wyj-320721199305232634
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId


- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:

      - accountId: $accountId
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 获取文件信息，提取fileKey
    variables:
      - fileId: $fileId1
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件2
    variables:
      - accountId: $accountId
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件3
    variables:
      - accountId: $accountId
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId3: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件4
    variables:
      - accountId: $accountId
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId4: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 一步发起：添加文档list
    variables:
      - attachment1: {fileId: $fileId1, attachmentName: "附件1.pdf"}
      - attachment2: {fileId: $fileId2, attachmentName: "附件2.pdf"}
      - attachment3: {fileId: $fileId3, attachmentName: "附件3.pdf"}
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "签署文档1.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "签署文档2.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "签署文档3.pdf", source: 0}
      - attachments: [$attachment1,$attachment2,$attachment3]
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["status_code", 200]



- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.0.fileId",$fileId1]
      - eq: ["content.data.signDocs.1.fileId",$fileId2]
      - eq: ["content.data.signDocs.2.fileId",$fileId3]
      - eq: ["content.data.attachments.0.fileId",$fileId1]
      - eq: ["content.data.attachments.1.fileId",$fileId2]
      - eq: ["content.data.attachments.2.fileId",$fileId3]


- test:
    name: 添加流程文档：单文档
    variables:
      - doc4: {'fileHash': '', 'fileId': $fileId4, 'fileName': '签署文档4', 'filePassword': '', 'encryption': 0}
      - docs: [$doc4]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 添加附件：单文档
    variables:
      - flowId: $sign_flowId1
      - attachments: [{"attachmentName": "附件4.pdf", "fileId": $fileId4}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.3.fileId",$fileId4]
      - eq: ["content.data.attachments.3.fileId",$fileId4]

- test:
    name: 删除签署文档2
    variables:
      - flowId: $sign_flowId1
      - fileIds: $fileId2
    api: api/signflow/flowDocuments/documentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
- test:
    name: 删除附件1
    variables:
      - flowId: $sign_flowId1
      - fileIds: $fileId1
    api: api/signflow/flowAttachments/attachmentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId1
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.0.fileId",$fileId1]
      - eq: ["content.data.signDocs.1.fileId",$fileId3]
      - eq: ["content.data.signDocs.2.fileId",$fileId4]
      - eq: ["content.data.attachments.0.fileId",$fileId2]
      - eq: ["content.data.attachments.1.fileId",$fileId3]
      - eq: ["content.data.attachments.2.fileId",$fileId4]





- test:
    name: "一步到位创建流程-对内-添加文档list"
    variables:
      - attachment1: {fileId: $fileId1, attachmentName: "附件1.pdf"}
      - attachment2: {fileId: $fileId2, attachmentName: "附件2.pdf"}
      - attachment3: {fileId: $fileId3, attachmentName: "附件3.pdf"}
      - doc1: {encryption: 0,fileId: $fileId1, fileName: "签署文档1.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "签署文档2.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "签署文档3.pdf", source: 0}
      - json: {"accountId":$accountId,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[$attachment1,$attachment2,$attachment3],"docs":[$doc1,$doc2,$doc3],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$accountId,"signerAccountName":"","signerAuthorizerId":$accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":0,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId2: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]




- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId2
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.0.fileId",$fileId1]
      - eq: ["content.data.signDocs.1.fileId",$fileId2]
      - eq: ["content.data.signDocs.2.fileId",$fileId3]
      - eq: ["content.data.attachments.0.fileId",$fileId1]
      - eq: ["content.data.attachments.1.fileId",$fileId2]
      - eq: ["content.data.attachments.2.fileId",$fileId3]


- test:
    name: 添加流程文档：单文档
    variables:
      - doc4: {'fileHash': '', 'fileId': $fileId4, 'fileName': '签署文档4', 'filePassword': '', 'encryption': 0}
      - docs: [$doc4]
      - flowId: $sign_flowId2
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 添加附件：单文档
    variables:
      - flowId: $sign_flowId2
      - attachments: [{"attachmentName": "附件4.pdf", "fileId": $fileId4}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId2
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.3.fileId",$fileId4]
      - eq: ["content.data.attachments.3.fileId",$fileId4]

- test:
    name: 删除签署文档2
    variables:
      - flowId: $sign_flowId2
      - fileIds: $fileId2
    api: api/signflow/flowDocuments/documentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
- test:
    name: 删除附件1
    variables:
      - flowId: $sign_flowId2
      - fileIds: $fileId1
    api: api/signflow/flowAttachments/attachmentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId2
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.0.fileId",$fileId1]
      - eq: ["content.data.signDocs.1.fileId",$fileId3]
      - eq: ["content.data.signDocs.2.fileId",$fileId4]
      - eq: ["content.data.attachments.0.fileId",$fileId2]
      - eq: ["content.data.attachments.1.fileId",$fileId3]
      - eq: ["content.data.attachments.2.fileId",$fileId4]


- test:
    name: 创建流程
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 添加流程文档：多文档
    variables:

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "签署文档1.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "签署文档2.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "签署文档3.pdf", source: 0}

      - docs: [$doc1,$doc2,$doc3]
      - flowId: $sign_flowId3
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 添加附件：多文档
    variables:
      - attachment1: {fileId: $fileId1, attachmentName: "附件1.pdf"}
      - attachment2: {fileId: $fileId2, attachmentName: "附件2.pdf"}
      - attachment3: {fileId: $fileId3, attachmentName: "附件3.pdf"}
      - flowId: $sign_flowId3
      - attachments: [$attachment1,$attachment2,$attachment3]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]



- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId3
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.0.fileId",$fileId1]
      - eq: ["content.data.signDocs.1.fileId",$fileId2]
      - eq: ["content.data.signDocs.2.fileId",$fileId3]
      - eq: ["content.data.attachments.0.fileId",$fileId1]
      - eq: ["content.data.attachments.1.fileId",$fileId2]
      - eq: ["content.data.attachments.2.fileId",$fileId3]


- test:
    name: 添加流程文档：单文档
    variables:
      - doc4: {'fileHash': '', 'fileId': $fileId4, 'fileName': '签署文档4', 'filePassword': '', 'encryption': 0}
      - docs: [$doc4]
      - flowId: $sign_flowId3
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 添加附件：单文档
    variables:
      - flowId: $sign_flowId3
      - attachments: [{"attachmentName": "附件4.pdf", "fileId": $fileId4}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId3
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.3.fileId",$fileId4]
      - eq: ["content.data.attachments.3.fileId",$fileId4]

- test:
    name: 删除签署文档2
    variables:
      - flowId: $sign_flowId3
      - fileIds: $fileId2
    api: api/signflow/flowDocuments/documentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
- test:
    name: 删除附件1
    variables:
      - flowId: $sign_flowId3
      - fileIds: $fileId1
    api: api/signflow/flowAttachments/attachmentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 查询流程详情：签署文档+附件顺序
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId3
      - queryAccountId: $accountId
      - operatorid: $accountId
    extract:
      - content_data: content.data
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.signDocs.0.fileId",$fileId1]
      - eq: ["content.data.signDocs.1.fileId",$fileId3]
      - eq: ["content.data.signDocs.2.fileId",$fileId4]
      - eq: ["content.data.attachments.0.fileId",$fileId2]
      - eq: ["content.data.attachments.1.fileId",$fileId3]
      - eq: ["content.data.attachments.2.fileId",$fileId4]