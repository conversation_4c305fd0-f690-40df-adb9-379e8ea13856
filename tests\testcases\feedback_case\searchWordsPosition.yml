- config:
    name: 反哺case_搜索关键字
    base_url: ${ENV(footstone_api_url)}
    "request":
      "base_url":
      "headers":
        "Content-Type": "application/json"
    variables:
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - fileName2117: 2019103000880493.pdf
      - fileName3586: 模板文件.pdf
      - fileId1: ${get_file_id($app_id,$fileName2117)}
      - fileId2: ${get_file_id($app_id,$fileName3586)}


- test:
    name: ONLINEBUG-2117：使用第三方userId 创建账号
    variables:
      - json:
          {
            "name": 梁贤红,
            "thirdPartyUserId": lxh-331082199211223080,
            "idNumber":"331082199211223080",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********"
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - accountId: content.data.accountId


- test:
    name: ONLINEBUG-2117：创建流程
    variables:
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $accountId
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-2117：添加流程文档：单文档
    variables:
      - flowId: $flowId
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: ONLINEBUG-2117：查询关键字坐标
    variables:
      - flowId: $flowId
      - fileId: $fileId1
      - keywords: '杨武#421381199207091013'
    api: api/signflow/flowDocuments/documentsKeywordSelect.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: ONLINEBUG-3586：添加流程文档：单文档
    variables:
      - flowId: $flowId
      - doc1: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: ONLINEBUG-3586：查询关键字坐标
    variables:
      - flowId: $flowId
      - fileId: $fileId2
      - keywords: '甲方(盖章）'
    api: api/signflow/flowDocuments/documentsKeywordSelect.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]