#
validate:
  - eq: [status_code, 200]
request:
  url: ${ENV(item_url)}/createFlow/createFlowInput
  method: POST
  headers: ${get_headers($app_id)}
  json:
    {
      "accountId": $accountId,
      "flowId": $flowId,
      "appId": $app_id,
      "attachments": $attachments,
      "authorizerId": $authorizerId,
      "autoArchive": $autoArchive,
      "autoInitiate": $autoInitiate,
      "batchDropSeal": true,
      "businessScene": $businessScene,
      "configInfo": $configInfo,
      "contractEffective": 0,
      "cooperationComplete": true,
      "createByApi": true,
      "createWay": "normal",
      "dnsAppId": "",
      "docs": $docs,
      "extend": {},
      "hashSign": false,
      "isShow": true,
      "processId": "",
      "recipients": $recipients,
      "signValidity": $signValidity,
      "signers": $signers,
      "templateCooperationId": "",
      "templateId": ""
    }


