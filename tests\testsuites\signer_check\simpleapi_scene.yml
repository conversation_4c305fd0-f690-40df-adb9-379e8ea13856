config:
  name: 简化版签署场景测试
  base_url: ${ENV(footstone_api_url)}

testcases:

  #    账户、组织、文档准备:
  #       testcase: testcases/signflow_scene/user_doc_prepare.yml

  简化版签署场景测试:
    testcase: testcases/signflow_scene/simple_openapi.yml


  #    添加签署区拆分场景-单文档：对接平台自动签署-用户自动签署-用户手动签署:
  #       testcase: testcases/signflow_scene/simple_auto_hand_Sign_1doc.yml
  #
  #
  #    添加签署区拆分场景-多文档：对接平台自动签署-用户自动签署-用户手动签署:
  #       testcase: testcases/signflow_scene/simple_auto_hand_Sign_Ndocs.yml
  #
  #    添加签署区拆分场景-顺序签:
  #       testcase: testcases/signflow_scene/simple_auto_hand_Sign_order.yml
  20220317迭代:
    testcase: testcases/iterate_cases/20220317.yml

  场景包年项目:
    testcase: testcases/scene_Packyear/createFlowOneStepRPC.yml

  签署方付费项目1:
    testcase: testcases/签署方付费/create-by-file接口发起异常.yml

  签署方付费项目2:
    testcase: testcases/签署方付费/create-by-file接口发起正常.yml

  招签宝项目:
    testcase: testcases/iterate_cases/招签宝hash签-签署方付费.yml


