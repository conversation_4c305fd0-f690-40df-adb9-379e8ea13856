#获取上传文件url  #contentMd5, contentType, fileName, fileSize必填
- api:
    def: get_uploadUrl($contentMd5, $contentType, $fileName, $fileSize)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/getFileKeyUploadUrl
      method: POST
      headers: ${get_headers($app_id)}
      json:
        contentMd5: $contentMd5
        contentType: $contentType
        fileName: $fileName
        fileSize: $fileSize



#根据本地文件创建文件  fileKey,fileName必填
- api:
    def: createbyfilekey($accountId, $fileKey, $name)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/createByFileKey
      method: POST
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
        fileKey: $fileKey
        fileName: $name


#根据文件Hash创建文件  fileHash，fileKey，name必填
- api:
    def: createbyhash($accountId, $fileHash, $fileKey, $name)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/createByHash
      method: POST
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
        fileHash: $fileHash
        fileKey: $fileKey
        name: $name



#根据文件模板创建文件   name，templateId必填
- api:
    def: createbytemplate($accountId, $chunkedFields, $name, $simpleFormFields, $templateId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/createByTemplate
      method: POST
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
        chunkedFields: $chunkedFields
        name: $name
        simpleFormFields: $simpleFormFields
        templateId: $templateId


#根据外部下载地址创建文件   name，url必填
- api:
    def: createbyurl($accountId, $name, $url)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/createByUrl
      method: POST
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
        name: $name
        url: $url


#获取文件列表 pageNo, pageSize非必填
- api:
    def: get_files($pageNo,$pageSize)
    request:
      url: /v1/files?pageNo=$pageNo&pageSize=$pageSize
      method: GET
      headers: ${get_headers($app_id)}



#根据docId获取文件信息
- api:
    def: get_fileId($fileId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/$fileId   # fileId必填
      method: GET
      headers: ${get_headers($app_id)}


#获取文件下载信息
- api:
    def: get_downloadurl($fileKey)
    request:
      url: ${ENV(filesystem_service_url)}/file-system/fileService/getDownloadUrl   # fileId必填
      method: POST
      headers:
        Content-Type: application/json
      json:
        fileKey: $fileKey



#根据fileKey获取文档预览地址
- api:
    def: get_filePreviewUrl($fileId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/getFilePreviewUrl?fileKey=$fileKey  # fileKey必填
      method: GET
      headers: ${get_headers($app_id)}



#分页获取指定PDF文档预览
- api:
    def: get_imagepreview($fileId,$pageNum,$imageSize,$accountId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/files/$fileId/imagepreview?pageNum=$pageNum&imageSize=$imageSize&accountId=$accountId # fileId必填
      method: GET
      headers: ${get_headers($app_id)}



#指定fileKey分页获取指定PDF文档预览
- api:
    def: get_viewimages($fileId,$pageNum,$imageSize)
    request:
      url: /v1/files/$fileId/viewimages?pageNum=$pageNum&imageSize=$imageSize
      method: GET
      headers: ${get_headers($app_id)}