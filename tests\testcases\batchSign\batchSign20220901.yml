- config:
    name: "批量签署链接-允许获取手绘流程"
    base_url: ${ENV(base_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(saas_noapproval)}
      - file_id: ${ENV(file_id_in_saas_noapproval)}
      - fileId1: ${ENV(file_id_in_saas_noapproval)}
      - fileId2: ${ENV(file_id_in_saas_noapproval)}
      - mobile_fengjiu: ***********
      - mobile_caogu: ***********
      - sealId1: 8fa71acc-425d-4270-9fa7-b0e91f57fbef
      - signFlowStartTimeTo: *************
      - standardoid: ${ENV(standardoid)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_zn_tsign: ${ENV(account_jm_in_tsign)}
      - account_id_4_tsign: ${ENV(standardoid)}
      - account_id_5_tsign: ${ENV(account_id_zn_tsign)}
      - sealId_sx: ${ENV(sealId_sx)}
      - sealId_zn: ${ENV(sealId_jm)}
      - sealId_zl: ${ENV(sealId_zl)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}

- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: ckl201911141994
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId




#V3接口创建签署流程，不取appid配置数据，流程强制签署人、签署主体实名意愿
- test:
    name: 创建钟灵的V3签署流程1-不指定印章类型
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      #        - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      - flowInfo: { autoArchive: false, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},
                   signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/xy_wk_integration/V3_createFlowOneStep.yml
    extract:
      - flowId_nolimit: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 创建钟灵的V3签署流程2-指定手绘章
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      #        - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},signfields: [{ certId: "", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " 轩辕悟空整合-个人1的个人签，个人1代企业A签，个人2代企业B签",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: "",signerAccount: "***********",authorizedAccount: ""},
                   signfields: [{ certId: "", sealType: "0", autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/xy_wk_integration/V3_createFlowOneStep.yml
    extract:
      - flowId_shouhui: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 创建钟灵的V2签署流程3-指定模板章
    variables:
      - doc: {encryption: 0,fileId: $fileId2, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $accountId_ckl, autoArchive: true, autoInitiate: true, businessScene: "流程无抄送人",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "",sealType: "1", autoExecute: false, actorIndentityType: 0, fileId: $fileId2,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_muban: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 创建钟灵的V2签署流程-1个签署区手绘&1个签署模板
    variables:
      - doc: {encryption: 0,fileId: $fileId2, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { initiatorAccountId: $accountId_ckl ,initiatorAuthorizedAccountId: $accountId_ckl, autoArchive: true, autoInitiate: true, businessScene: "流程无抄送人",flowConfigInfo: {noticeDeveloperUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "",sealType: "1", autoExecute: false, actorIndentityType: 0, fileId: $fileId2,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },{signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $accountId_ckl}, signfields: [ { certId: "",sealType: "0", autoExecute: false, actorIndentityType: 0, fileId: $fileId2,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_v2mix: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



- test:
    name: 创建钟灵的API3.0签署流程-指定手绘章
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": "$file_id",
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "流程发起成功-非自动归档 签署方付费",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": [],
              "willingnessAuthModes": ["CODE_SMS","CODE_EMAIL","PSN_FACE_ALIPAY","PSN_FACE_TECENT","PSN_FACE_ESIGN","PSN_FACE_WECHAT"]
            },
            "autoFinish": false,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
              "orderType":"DISTRIBUTION"

            },
            "noticeConfig": {
              "noticeTypes": "1,2"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": 2,
              "redirectUrl": "https://www.baidu.com"
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [
          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "psnSignerInfo": {
              "psnAccount": "",
              "psnId": "$account_id_4_tsign"

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": "$file_id",
              "normalSignFieldConfig": {
                "assignedSealId": "",
                "autoSign": false,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "0",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 180,
                  "positionY": 180
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 0
          }
          ]
        }

    extract:
      - flowId_V3shouhui: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']


- test:
    name: 创建钟灵的API3.0签署流程-1个签署区手绘&1个签署区模板
    api: api/sign-flow/create-by-file.yml
    variables:
      json:
        {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
          {
            "fileEditPwd": "",
            "fileId": "$file_id",
            "fileName": "测试文档1.pdf",
            "neededPwd": false
          }
          ],

          "signFlowConfig": {
            "signFlowTitle": "流程发起成功-非自动归档 签署方付费",
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
              "orgEditableFields": [],
              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
              "psnEditableFields": [],
              "willingnessAuthModes": ["CODE_SMS","CODE_EMAIL","PSN_FACE_ALIPAY","PSN_FACE_TECENT","PSN_FACE_ESIGN","PSN_FACE_WECHAT"]
            },
            "autoFinish": false,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
              "orderType":"DISTRIBUTION"

            },
            "noticeConfig": {
              "noticeTypes": "1,2"
            },
            "notifyUrl": "",
            "redirectConfig": {
              "redirectDelayTime": 2,
              "redirectUrl": "https://www.baidu.com"
            },
            "signConfig": {
              "availableSignClientTypes": "",
              "showBatchDropSealButton": true
            }
          },
          "signers": [
          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "psnSignerInfo": {
              "psnAccount": "",
              "psnId": "$account_id_4_tsign"

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": "$file_id",
              "normalSignFieldConfig": {
                "assignedSealId": "",
                "autoSign": false,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "0",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 180,
                  "positionY": 180
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 0
          },

          {
            "noticeConfig": {
              "noticeTypes": "1"
            },

            "psnSignerInfo": {
              "psnAccount": "",
              "psnId": "$account_id_4_tsign"

            },

            "signConfig": {
              "forcedReadingTime": 0,
              "signOrder": 1
            },
            "signFields": [
            {
              "customBizNum": "xxx",
              "fileId": "$file_id",
              "normalSignFieldConfig": {
                "assignedSealId": "",
                "autoSign": false,
                "availableSealIds": [],
                "freeMode": false,
                "movableSignField": true,
                "orgSealBizTypes": "",
                "psnSealStyles": "1",
                "signFieldPosition": {
                  "acrossPageMode": "",
                  "positionPage": "1",
                  "positionX": 280,
                  "positionY": 280
                },
                "signFieldSize": 0,
                "signFieldStyle": 1
              },
              "signFieldType": 0
            }
            ],
            "signerType": 0
          }
          ]
        }

    extract:
      - flowId_v3mix: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']


#获取批量签署链接
- test:
    name:  获取批量签署url-手绘/模板存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_nolimit, $flowId_shouhui
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: [content.data.batchSignUrl, 10]

- test:
    name:  获取批量签署url-手绘/模板存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_nolimit, $flowId_muban
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: [content.data.batchSignUrl, 10]

- test:
    name:  获取批量签署url-手绘/模板存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_nolimit, $flowId_V3shouhui
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: [content.data.batchSignUrl, 10]

- test:
    name:  获取批量签署url-手绘/模板存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_muban, $flowId_v2mix
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: [content.data.batchSignUrl, 10]


- test:
    name:  获取批量签署url-手绘/模板不存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_muban, $flowId_V3shouhui
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","暂不支持仅模板印章和仅手绘印章组合的流程"]



- test:
    name:  获取批量签署url-手绘/模板不存在交集11
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_shouhui, $flowId_v3mix,$flowId_muban
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","暂不支持仅模板印章和仅手绘印章组合的流程"]

- test:
    name:  获取批量签署url-手绘/模板不存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_v2mix, $flowId_v3mix
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","暂不支持仅模板印章和仅手绘印章组合的流程"]



- test:
    name:  获取批量签署url-手绘/模板存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_v2mix, $flowId_v3mix,$flowId_nolimit
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]

- test:
    name:  获取批量签署url-手绘/模板存在交集
    variables:
      - json: {
        "operatorId": $accountId_ckl,
        "redirectUrl": "",
        "signFlowIds": [
          $flowId_shouhui, $flowId_muban,$flowId_nolimit
        ]
      }
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","暂不支持仅模板印章和仅手绘印章组合的流程"]



