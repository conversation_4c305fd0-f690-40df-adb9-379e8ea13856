- config:
    name: 追加手动签署区-轩辕
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(opponent_appid)}
      - file_id: ${ENV(file_id_in_tsing)}
      - account_id_sx: ${ENV(account_id_sx_tsign)}
      - account_id_dy: ${ENV(orgid_dy)}
      - account_id_1: ${ENV(account_id1_in_tsign)}
      - account_id_2: ${ENV(account_id2_in_tsign)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}

- test:
    name: 分布发起流程
    variables:
      - json: {"autoArchive":"false","businessScene":"签署${get_timestamp()}","configInfo":{"noticeType":"1","noticeDeveloperUrl":"${notifyUrl}","signPlatform":"1,2,3","archiveLock":false,"redirectUrl":"","countdown":0,"redirectDelayTime":"0","batchDropSeal":false},"extend":{},"initiatorAccountId":"$account_id_1","initiatorAuthorizedAccountId":"$account_id_1","payerAccountId":""}
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 无文件，流程无法开启
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437109]
      - eq: ["content.message", 流程没有文档不允许开启流程]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $file_id, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 指定个人签，签署人为企业，无法追加签署区
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_sx,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 手动签署的签署人必须是个人]

- test:
    name: 指定个人签，posBean 为空，无法追加签署区
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 盖章位置超出文件范围，无法盖章]

- test:
    name: 指定个人签，未传posBean，无法追加签署区
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 盖章位置超出文件范围，无法盖章]

- test:
    name: 指定个人签，关键字签未传关键字，无法追加签署区
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":4}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 必须指定keyword]

- test:
    name: 指定个人签，关键字签关键字为空，无法追加签署区
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"keyword":""},"sealType":"","sealId":"","signType":4}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 必须指定keyword]

- test:
    name: 指定个人签，关键字签
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"keyword":"一二三"},"sealType":"","sealId":"","signType":4}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 指定个人签，不传postPage，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 盖章位置超出文件范围，无法盖章]

- test:
    name: 指定个人签，posPage 为空，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 盖章位置超出文件范围，无法盖章]

- test:
    name: 单页签，signType 为1-2，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1-2","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 盖章位置超出文件范围，无法盖章]

- test:
    name: 关键字签，signType 为1-2，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1-2","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 盖章位置超出文件范围，无法盖章]

- test:
    name: 骑缝签，signType 为1-2，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1-2","posX":100,"posY":200},"sealType":"","sealId":"","signType":2}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 骑缝签，signType 为1-2,2-3，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1-2,2-3","posX":100,"posY":200},"sealType":"","sealId":"","signType":2}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: qrcodeSign 为true，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"qrcodeSign": true},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: qrcodeSign 为false，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"qrcodeSign": false},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: qrcodeSign 为false，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"qrcodeSign": false},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: handDrawnWay 为2，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200,"qrcodeSign": false,"handDrawnWay": "2"},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: fileId 为空，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":"","signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", fileId不能为空]

- test:
    name: fileId 为不存在，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":1$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 文件id不在签署流程中]

- test:
    name: certId 为不存在，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":2,"certId": $file_id, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 指定证书不存在]

- test:
    name: 个人签传了企业oid，直接读取签署人信息，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId": $account_id_dy,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message", 成功]

- test:
    name: 企业签传了个人oid，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"2","authorizedAccountId": $account_id_1,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 签署主体不是企业]

- test:
    name: 法人签传了个人oid，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"3","authorizedAccountId": $account_id_1,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 签署主体不是企业]

- test:
    name: 经办人签传了个人oid，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"4","authorizedAccountId": $account_id_1,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 签署主体不是企业]

- test:
    name: actorIndentityType 为-1，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"-1","authorizedAccountId": $account_id_1,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 未定义的actorIndentityType]

- test:
    name: 轩辕appId 指定印章，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId": $account_id_1,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"1","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 实名签署不允许指定印章id]

- test:
    name: 查询个人印章
    variables:
      - accountId: $account_id_1
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message", 成功]

- test:
    name: 个人签无法追加印章列表
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId": $account_id_1,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealIds":[$sealId],"signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 不支持指定印章列表的任务类型]

- test:
    name: 查询企业印章
    variables:
      - accountId: $account_id_dy
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealId2: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message", 成功]

- test:
    name: 企业签指定印章列表，有个印章不匹配，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"2","authorizedAccountId": $account_id_dy,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealIds":[$sealId,$sealId2],"signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 印章和签署主体不匹配]

- test:
    name: 企业签指定印章列表，印章匹配，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"2","authorizedAccountId": $account_id_dy,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealIds":[$sealId2],"signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message", 成功]

- test:
    name: signDateBeanType 为3，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"2","authorizedAccountId": $account_id_dy,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealIds":[$sealId2],"signType":1,"signDateBeanType": 3}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", "signDateBeanType[0,2]之间"]

- test:
    name: willTypes 非枚举值，不校验，可以追加
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"2","authorizedAccountId": $account_id_dy,"assignedPosbean":true,"order":2, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealIds":[$sealId2],"signType":1,"willTypes":["1111"]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message", 成功]



- test:
    name: 流程开启成功
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: 再次追加的order 比当前签署任务中的小
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"2","authorizedAccountId": $account_id_dy,"assignedPosbean":true,"order":1, "posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealIds":[$sealId2],"signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437309]
      - contains: ["content.message", '当前签署顺序无效或已全部签署完成, 不能添加签署区']

- test:
    name: 分布发起流程
    variables:
      - json: {"autoArchive":"true","businessScene":"签署${get_timestamp()}","configInfo":{"noticeType":"1","noticeDeveloperUrl":"${notifyUrl}","signPlatform":"1,2,3","archiveLock":false,"redirectUrl":"","countdown":0,"redirectDelayTime":"0","batchDropSeal":false},"extend":{},"initiatorAccountId":"$account_id_1","initiatorAuthorizedAccountId":"$account_id_1","payerAccountId":""}
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $file_id, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 指定个人自由签
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$file_id,"signerAccountId":$account_id_1,"actorIndentityType":"0","authorizedAccountId":$account_id_1,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":"","sealId":"","signType":0}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message", 成功]

- test:
    name: 流程开启成功
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flowId
      - accountId: $account_id_1
      - urlType: 0
      - multiSubject: true
      - organizeId: ''
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $account_id_1
      - flowId: $sign_flowId
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]
      - eq: ["content.code",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $account_id_1
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 批量新增签署区+无需用印审批的自由签
    variables:
      - flowId: $sign_flowId
      - json: {"accountId":"$account_id_1","addSignfields":[{"actorIndentityType":0,"allowedUploadAttachment":true,"approvalNoticeUrl":"","assignedPosbean":true,"assignedSeal":true,"authorizedAccountId":"$account_id_1","autoExecute":false,"autoExecuteType":0,"certId":"","crossEnterpriseSeal":false,"extendFieldDatas":{},"fieldType":0,"fileId":"$file_id","flowId":"$sign_flowId","forceReadTime":0,"handDrawnWay":"","noticeUrl":"","order":1,"posBean":{"addSignTime":false,"posPage":"1","posX":"439","posY":"332","qrcodeSign":false,"sealType":1,"signDateBean":{"fontName":"simsun","fontSize":12,"format":"yyyy年MM月dd日","posPage":"1","posX":"253","posY":"346"},"signType":1,"signerAccountId":"$account_id_1","width":200},"sealBizTypes":"","sealId":"$sealId","sealIds":[],"sealType":"","signFinishNotice":true,"signStartNotice":true,"signType":1,"signWillingType":0,"signerAccountId":"$account_id_1","signerOperatorAuthorizerId":"$account_id_1","signerOperatorId":"$account_id_1","synchExecute":true,"thirdOrderNo":"","willTypes":[]},{"actorIndentityType":0,"allowedUploadAttachment":true,"approvalNoticeUrl":"","assignedPosbean":true,"assignedSeal":true,"authorizedAccountId":"$account_id_1","autoExecute":false,"autoExecuteType":0,"certId":"","crossEnterpriseSeal":false,"extendFieldDatas":{},"fieldType":0,"fileId":"$file_id","flowId":"$sign_flowId","forceReadTime":0,"handDrawnWay":"","noticeUrl":"","order":1,"posBean":{"addSignTime":false,"posPage":"1","posX":"400","posY":"400","qrcodeSign":false,"sealType":1,"signDateBean":{"fontName":"simsun","fontSize":12,"format":"yyyy年MM月dd日","posPage":"1","posX":400,"posY":400},"signType":1,"signerAccountId":"$account_id_1","width":200},"sealBizTypes":"","sealId":"$sealId","sealIds":[],"sealType":"","signFinishNotice":true,"signStartNotice":true,"signType":1,"signWillingType":0,"signerAccountId":"$account_id_1","signerOperatorAuthorizerId":"$account_id_1","signerOperatorId":"$account_id_1","synchExecute":true,"thirdOrderNo":"","willTypes":[]}],"agentAccountId":"","approvalPolicy":0,"approvalSubPolicy":0,"async":true,"batchSign":true,"clientId":"","dingUser":{"dingCorpId":"","dingIsvAppId":"","dingUserId":"","processInstanceId":""},"fromOldVersionDing":true,"language":"","needSignWilling":true,"notifyPolicy":0,"notifySerialId":"","orderNo":"","securityCode":"","serviceId":"","signfieldIds":[],"supportSignDuplicateCheck":true,"updateSignfields":[]}
    api: api/signflow/v3/addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
#