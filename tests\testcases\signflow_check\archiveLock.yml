- config:
    name: 流程归档锁定配置
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id1: ${ENV(BZQ-App-Id)}
      - app_id: ${ENV(BZQ-App-Id)}
      - extend: {}
      - tPUserId: autotest123${get_randomNo()}
    "request":
      "base_url":
      "headers":
        "Content-Type": "application/json"

- test:
    name: 使用第三方userId 创建账号
    variables:
      - json:
          {
            "name": 梁贤红,
            "thirdPartyUserId": $tPUserId,
            "idNumber":"331082199211223080",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********"
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - oId1: content.data.accountId
    output:
      - $oId1

- test:
    name: 创建流程：archiveLock字段值为空
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',archiveLock: ''}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：archiveLock字段值为false
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId1
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.archiveLock",false]


- test:
    name: 创建流程：archiveLock字段不传
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：archiveLock字段值为false
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId2
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.archiveLock",false]


- test:
    name: 创建流程：archiveLock字段值为false
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',archiveLock: false}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：archiveLock字段值为false
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId3
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.archiveLock",false]


- test:
    name: 创建流程：archiveLock字段值为true
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',archiveLock: true}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: 查询流程详情
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId4
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.archiveLock",True]

