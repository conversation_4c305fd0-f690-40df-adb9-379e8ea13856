- config:
    name: "********薪福通测试用例"
    base_url: ${ENV(base_url)}

    variables:
      #      - salary_url: ${ENV(base_url)}
      - app_id: ${ENV(app_id_tengqing_SaaS)}
      - accountId_in_SaaS: ${ENV(accountId_in_SaaS)}
      - app_id: ${ENV(app_id_tengqing_SaaS)}
      - fileId_tengqing_SaaS: ${ENV(fileId_tengqing_SaaS)}
      - account_id_dy: ${ENV(mobile_shield_wukong_appid_oid)}
      - account_id_sq: ${ENV(account_id_sq_SaaS)}
      - group: ''
      - operator_id: ${ENV(account_id1_in_tsign)}

#薪福通项目
- test:
    name: 创建流程
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId_in_SaaS
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加文件
    variables:
      - doc: {fileId: $fileId_tengqing_SaaS, fileName: "合同.pdf","filePassword":"", encryption: 0}
      - docs: [$doc]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加手动签名域-组合
    variables:
      #添加个人签署区
      - signDateBean1: {posPage: "1","posX": 146,"posY": 84}
      - posBean1: {posPage: "1", posX: 97, posY: 8}
      - signfield1: {signDateBean: $signDateBean1, posBean: $posBean1,"fileId": $fileId_tengqing_SaaS, signerAccountId: $accountId_in_SaaS,actorIndentityType: "0",authorizedAccountId: $accountId_in_SaaS, assignedPosbean: true, signDateBeanType: "1", order: 1,sealType: "",sealId: "", signType: ""}
      #添加企业签署区-深泉
      - signDateBean2: {posPage: "1","posX": 266,"posY": 285}
      - posBean2: {posPage: "1", posX: 343, posY: 334}
      - signfield2: {signDateBean: $signDateBean2, posBean: $posBean2,"fileId": $fileId_tengqing_SaaS, signerAccountId: $accountId_in_SaaS,actorIndentityType: "2",authorizedAccountId: $account_id_sq, assignedPosbean: true, signDateBeanType: "1", order: 1,sealType: "",sealId: "", signType: ""}
      #添加企业签署区-东营
      - signDateBean3: {posPage: "1","posX": 617,"posY": 21}
      - posBean3: {posPage: "1", posX: 554, posY: 50}
      - signfield3: {signDateBean: $signDateBean3, posBean: $posBean3,"fileId": $fileId_tengqing_SaaS, signerAccountId: $accountId_in_SaaS,actorIndentityType: "2",authorizedAccountId: $account_id_dy, assignedPosbean: true, signDateBeanType: "1", order: 1,sealType: "",sealId: "", signType: ""}
      #添加法人签署区
      - signDateBean4: {posPage: "1","posX": 113,"posY": 580}
      - posBean4: {posPage: "1", posX: 88, posY: 579}
      - signfield4: {signDateBean: $signDateBean4, posBean: $posBean4,"fileId": $fileId_tengqing_SaaS, signerAccountId: $accountId_in_SaaS,actorIndentityType: "3",authorizedAccountId: $account_id_dy, assignedPosbean: true, signDateBeanType: "1", order: 1,sealType: "",sealId: "", signType: ""}
      #添加经办人签署区
      - signDateBean5: {posPage: "1","posX": 552,"posY": 549}
      - posBean5: {posPage: "1", posX: 572, posY: 539}
      - signfield5: {signDateBean: $signDateBean5, posBean: $posBean5,"fileId": $fileId_tengqing_SaaS, signerAccountId: $accountId_in_SaaS,actorIndentityType: "4",authorizedAccountId: $account_id_dy, assignedPosbean: true, signDateBeanType: "1", order: 1,sealType: "",sealId: "", signType: ""}
      - signfields: [$signfield1,$signfield2,$signfield3,$signfield4, $signfield5]
      - flowId: $sign_flowId
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启签署流程
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 获取签署链接
    variables:
      - flowId: $sign_flowId
      - accountId: ${ENV(accountId_in_SaaS)}
      - urlType: 0
      - organizeId:
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询可用印章
    variables:
      - flowId: $sign_flowId
      - loginAccountId: ${ENV(accountId_in_SaaS)}
      - signerAccountId: ${ENV(accountId_in_SaaS)}
      - accountId: ${ENV(accountId_in_SaaS)}
      - urlType: 1
      - organizeId:
    api: api/signflow/signflows/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
        # 返回的ownerName list 中 ownerName 未空的即为跨企业授权章，最长授权3年，到期后需手动调整
      - len_gt: ["content.data.officialSeals.0.organSeals.-1.ownerName", 1]

- test:
    name: 创建流程
    variables:
      - extend:
      - autoArchive: 'true'
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1，2，3，4，5'}
      - contractValidity:
      - initiatorAccountId: $accountId_in_SaaS
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加文件
    variables:
      - doc: {fileId: $fileId_tengqing_SaaS, fileName: "合同.pdf","filePassword":"", encryption: 0}
      - docs: [$doc]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加手动签名域-单个
    variables:
      #添加企业签署区-深泉
      - signDateBean2: {posPage: "1","posX": 266,"posY": 285}
      - posBean2: {posPage: "1", posX: 343, posY: 334}
      - signfield2: {signDateBean: $signDateBean2, posBean: $posBean2,"fileId": $fileId_tengqing_SaaS, signerAccountId: $accountId_in_SaaS,actorIndentityType: "2",authorizedAccountId: $account_id_dy, assignedPosbean: true, signDateBeanType: "1", order: 1,sealType: "",sealId: "", signType: ""}
      - signfields: [$signfield2]
      - flowId: $sign_flowId
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启签署流程
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询签名域
    variables:
      - flowId: $sign_flowId
      - accountId: ${ENV(accountId_in_SaaS)}
    extract:
      - signfieldId: content.data.signfields.0.signfieldId
    api: api/iterate_cases/search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署跨企业授权章-无需授权
    variables:
      - flowId: $sign_flowId
      - accountId: ${ENV(accountId_in_SaaS)}
      - json: {"async":true,"accountId":"$operator_id","updateSignfields":[{"sealId":"${ENV(sealId_for_crossEnterpriseSeal_in_dy)}","sealFileKey":"${ENV(sealFileKey_for_crossEnterpriseSeal_in_dy)}","signfieldId":"$signfieldId","actorIndentityType":2,"assignedItem":false,"assignedPosbean":true,"assignedSeal":false,"authorizedAccountId":"${ENV(orgid_dy)}","autoExecute":false,"signDateBeanType":1,"extendFieldDatas":{},"fileId":"$fileId_tengqing_SaaS","crossEnterpriseSeal":true,"order":1,"posBean":{"addSignTime":false,"posPage":"1","posX":343,"posY":334,"qrcodeSign":false,"width":200,"signDateBean":{"posPage":"1","fontName":"simsun","format":"yyyy年MM月dd日","fontSize":12,"posX":266,"posY":285},"sealType":1,"signType":1,"signerAccountId":"$operator_id"}}],"approvalPolicy":0,"dingUser":{}}
    api: api/signflow/v3/addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", 成功]

- test:
    name: 查询操作日志
    api: api/signflow/flowLogs/logsSelect.yml
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $sign_flowId
      - pageNum: ""
      - pageSize: ""
      - type: ""
    extract:
      - logs: content.data.logs
    validate:
      - eq: [status_code, 200]
      - contains: ['${toString($logs)}', "crossEnterpriseSealInfos"]
