#流程参与人

#查询参与人列表
- api:
    def: recipientsSelect($flowId, $roleType)
    request:
      url: /v1/signflows/$flowId/recipients?roleType=$roleType
      method: GET
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]

#批量添加流程参与人
- api:
    def: recipientsCreate($flowId, $recipients)
    request:
      url: /v1/signflows/$flowId/recipients
      method: POST
      headers: ${get_headers($app_id)}
      json:
        recipients: $recipients
    validate:
      - eq: [status_code, 200]

#批量删除流程参与人
- api:
    def: recipientsDelete($flowId, $recipientIds)
    request:
      url: /v1/signflows/$flowId/recipients
      method: DELETE
      headers: ${get_headers($app_id)}
      json:
        recipientIds: $recipientIds
    validate:
      - eq: [status_code, 200]
