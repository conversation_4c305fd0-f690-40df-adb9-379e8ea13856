config:
  name: 多文档并发测试场景-批量签署20份文件
  variables:
    email_1:
    mobile_1: 18850583991
    idType_person: CRED_PSN_CH_IDCARD
    name_person_1: 陈凯丽
    idNumber_person_1: 362323199201061068
    app_id: ${ENV(X-Tsign-Open-App-Id)}
    path_pdf: data/个人借贷合同.pdf
    path_pdf2: data/劳动合同书.pdf
    path_pdf3: data/模板劳动合同0001.pdf
    path_pdf4: data/销售合同006.pdf
    path_pdf5: data/three_page.pdf
    mobileNew: $mobile_1

testcases:

  多文档并发测试场景-批量签署20份文件:
    testcase: testcases/signflow_scene/sign_20docs_createflow.yml

  多文档并发异步签测试场景-批量签署20份文件:
    testcase: testcases/signflow_scene/sign_20docs_createflow_async.yml

  批量多文档并发测试场景-获取批量签署流程:
    testcase: testcases/signflow_scene/sign_20docs_getBatchSignFlows.yml

#    签署登陆:
#       testcase: testcases/signflow_scene/userLogin.yml
#
#    意愿认证:
#       testcase: testcases/signflow_scene/willing.yml
#       variables:
#      flowId: $flowId
#      personOid_realname: $personOid_realname

#    name: 签署
#    variables:
#       personOid: $personOid_realname
#       flowId: $flowId
#    testcase: testcases/signflow_scene/sign_20docs_sign.yml
