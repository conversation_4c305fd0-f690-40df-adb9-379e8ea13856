#创建模板  fileKey, templateName必填
- api:
    def: create_templates($fileKey, $templateName, $fileMimeType)
    request:
      url: ${ENV(footstone_doc_url)}/v1/docTemplates
      method: POST
      headers: ${get_headers($app_id)}
      json:
        fileKey: $fileKey
        templateName: $templateName
        fileMimeType: $fileMimeType



##老的创建模板，根据文件模板创建文件需要用到
#- api:
#    def: old_create_templates($fileKey, $templateName, $templateFormKeys, $templateTypeId)
#    request:
#        url: /api/v1/templates
#        method: POST
#        headers: ${get_headers($app_id)}
#        json:
#        fileKey: $fileKey
#        templateName: $templateName
#        templateFormKeys: $templateFormKeys
#        templateTypeId: $templateTypeId


#获取模板列表
- api:
    def: get_templates($pageNo, $pageSize)
    request:
      url: ${ENV(footstone_doc_url)}/v1/docTemplates
      method: GET
      headers: ${get_headers($app_id)}
      json:
        pageNo: $pageNo
        pageSize: $pageSize


#删除模板
- api:
    def: delete_templates($templateId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/docTemplates/$templateId
      method: DELETE
      headers: ${get_headers($app_id)}


#添加模板结构化组件
- api:
    def: add_components($templateId, $structComponent)
    request:
      url: ${ENV(footstone_doc_url)}/v1/docTemplates/$templateId/components
      method: POST
      headers: ${get_headers($app_id)}
      json:
        structComponent: $structComponent



#删除模板结构化组件
- api:
    def: delete_components($templateId, $id)
    request:
      url: ${ENV(footstone_doc_url)}/v1/docTemplates/$templateId/components/$id
      method: DELETE
      headers: ${get_headers($app_id)}


#获取模板下载地址
- api:
    def: get_template_downloadUrl($templateId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/docTemplates/$templateId/downloadUrl
      method: GET
      headers: ${get_headers($app_id)}


#获取模板设置页面地址
- api:
    def: get_template_getSettingUrl($templateId)
    request:
      url: ${ENV(footstone_doc_url)}/v1/docTemplates/$templateId/getSettingUrl
      method: GET
      headers: ${get_headers($app_id)}