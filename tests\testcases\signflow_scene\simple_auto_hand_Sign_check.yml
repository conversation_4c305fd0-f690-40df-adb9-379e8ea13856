- config:
    name: 简化版参数校验：对接平台自动签署-用户自动签署-用户手动签署
    base_url: ${ENV(base_url)}
    variables:
      - orgName: 东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: 13344445555
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - personOid1: 4468fe9323bc4bfeaf5f659af14b6754
      - personOid2: 824708b5195546eab28565ebb0203a1e
      - personOid3: 310913bf68594f8bb7c457215e0b4831
      - orgOid1: 4d4352cd0de849ab9daa507397086bf1
      - fileId1: 37492a3be0c64282b834a48f77db3c89
      - m1: "19922222222"
      - m2: "19800000000"
      - e1: <EMAIL>
      - e2: <EMAIL>
      - pOid1: ${getOid($m1)}
      - pOid2: ${getOid($m2)}
      - pOid3: ${getOid($e1)}
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:
      - m3: "***********"
      - pOid_m: ${getOid($m3)}
      - tPUserId1: autotest1${get_randomNo()}
      - appid_xuanyuan: ${ENV(appId_xuanyuan)}
      - m4: "***********"
      - pOid_m1: ${getOid($m4)}
      - client_id: pc

- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    variables:
      - idNo: ${get_idNo()}
      - name: esigntest${get_randomNo()}
    api: api/user/accounts/create_accounts.yml
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建组织：个人账户1创建组织"
    variables:
      - accountId: $personOid2
      #        - organCode: ${get_orgCode()}
      - idNo: ${get_idNo()}
      - orgType: CRED_ORG_CODE
      - orgCode: ${get_orgCode()}
      - legalIdNo:
      - legalName:
      - name: $orgName
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 数据准备：查询平台印章
    api: api/seals/seals/select_plat_seals.yml
    extract:
      - plat_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name:  数据准备：查询个人1印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询个人2印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name:  数据准备：查询企业印章
    api: api/seals/seals/select_c_seals.yml
    variables:
      - orgId: $orgOid1
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 数据准备：对个人账户1静默授权
    variables:
      - accountId: $personOid1
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 撤销场景——创建流程
    variables:
      - businessScene: "创建流程"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs_2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1,$docs_2]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_revoke: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 撤销场景——开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId_revoke
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 撤销流程
    api: api/signflow/signflows/signflowsRevoke.yml
    variables:
      - flowId: $flowId_revoke
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-流程状态异常-已撤销，失败
    variables:
      - flowId: $flowId_revoke
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'流程已经结束']

- test:
    name: 用户自动签署-流程状态异常-已撤销，失败
    variables:
      - flowId: $flowId_revoke
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'流程已经结束']

- test:
    name: 添加用户手动签署-流程状态异常-已撤销，失败
    variables:
      - flowId: $flowId_revoke
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'流程已经结束']

- test:
    name: 拒签场景——创建流程
    variables:
      - businessScene: "创建流程"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs_2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1,$docs_2]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_refuse: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 拒签场景——开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId_refuse
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}

- test:
    name: 拒签场景——添加流程签署区
    variables:
      - flowId: $flowId_refuse
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 拒签流程
    variables:
      - refuseReason: '拒签不需要理由No Reason'
      - signfieldId: $signfieldId1
      - flowId: $flowId_refuse
    api: api/signflow/signfields/signfieldsRefuse.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署-流程状态异常-已拒签，失败
    variables:
      - flowId: $flowId_refuse
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'流程已经结束']

- test:
    name: 用户自动签署-流程状态异常-已拒签，失败
    variables:
      - flowId: $flowId_refuse
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'流程已经结束']

- test:
    name: 添加用户手动签署-流程状态异常-已拒签，失败
    variables:
      - flowId: $flowId_refuse
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'流程已经结束']

- test:
    name: 参数校验——创建流程
    variables:
      - businessScene: "创建流程"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_check: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 对接平台自动签署-流程状态异常-草稿，开启后签署完成
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId10: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户自动签署-流程状态异常-草稿，开启后签署完成
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId11: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加用户手动签署-流程状态异常-草稿，开启后签署中
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: { 'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId12: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 参数校验——开启流程
    api: api/signflow/signflows/signflowsStart.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(8)}
    variables:
      - flowId: $flowId_check
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 参数校验-草稿时添加的签署区，流程开启后-$desc
    api: api/signflow/signfields/signfieldsSelect.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}
    variables:
      - flowId: $flowId_check
      - accountId:
      - desc: '平台自动签，签署完成'
      - signfieldIds: $signfieldId10
      - status: 4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.status",$status]

- test:
    name: 参数校验-草稿时添加的签署区，流程开启后-$desc
    teardown_hooks:
      - ${hook_sleep_n_secs(10)}
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId_check
      - accountId:
      - desc: '用户自动签，签署完成'
      - signfieldIds: $signfieldId11
      - status: 4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.status",$status]

- test:
    name: 参数校验-草稿时添加的签署区，流程开启后-$desc
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId_check
      - accountId:
      - desc: '用户手动签，签署中'
      - signfieldIds: $signfieldId12
      - status: 1
    setup_hooks:
      - ${sleep_N_secs(3)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.status",$status]

- test:
    name: 对接平台自动签署参数校验-流程状态异常-fileId不属于该流程，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId2, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
      - message: ${ENV(message2)}
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",$message]

- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 对接平台自动签署参数校验-flowId非必填
    variables:
      - flowId: ""
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 404]

- test:
    name: 对接平台自动签署参数校验-fileId非必填
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {  'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'fileId不能为空']

- test:
    name: 对接平台自动签署参数校验-fileId非必填
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':'', 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'fileId不能为空']

- test:
    name: 对接平台自动签署参数校验-posBean必填
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'签署区位置不能为空']

- test:
    name: 对接平台自动签署参数校验-posBean必填
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':{}, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 对接平台自动签署参数校验-sealId非必填
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 对接平台自动签署参数校验-sealId非必填
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':''}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 对接平台自动签署参数校验-sealId不属于平台方，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'印章和签署主体不匹配']

- test:
    name: 对接平台自动签署参数校验-signType为1时, 超过实际页码，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'10','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437306]
      - contains: ["content.message",'单页签署: 签署页码超出文档页数']

- test:
    name: 对接平台自动签署参数校验-signType为1时, 页码为空，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 对接平台自动签署参数校验-signType为1时, 页码必须为单页
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1-2','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 对接平台自动签署参数校验-signType为1时, X坐标为空，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1', 'posY':400}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 对接平台自动签署参数校验-signType为1时, Y坐标为空，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300}
      - signfield: { 'fileId':$fileId1, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 对接平台自动签署参数校验-signType为2时,页码超过实际页码，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':10,'posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1,'signType':2, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437306]
      - contains: ["content.message",'骑缝签署: 签署页码超出文档页数']

- test:
    name: 对接平台自动签署参数校验-signType为2时,页码为空，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'','posX':300, 'posY':400}
      - signfield: { 'fileId':$fileId1,'signType':2, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'骑缝签署：必须指定正确的page']

- test:
    name: 对接平台自动签署参数校验-signType为2时,Y坐标为空，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300}
      - signfield: { 'fileId':$fileId1,'signType':2, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'骑缝签署：必须指定y']

- test:
    name: 对接平台自动签署参数校验-signType为2,骑缝签,posPage多页时支持-分割
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1-2','posY':300}
      - signfield: { 'fileId':$fileId1,'signType':2, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署参数校验-signType为2,骑缝签,posPage多页时支持,分割
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1,2','posY':300}
      - signfield: { 'fileId':$fileId1,'signType':2, 'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户自动签署参数校验-fileId不属于该流程
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId2, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
      - message: ${ENV(message2)}
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",$message]

- test:
    name: 数据准备：取消签署授权:个人账户1
    variables:
      - accountId: $personOid1
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 用户自动签署参数校验-个人签署，未静默授权，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 用户未授权或授权已过期，不允许静默签署']

- test:
    name: 数据准备：对个人账户1静默授权，且授权过期
    variables:
      - accountId: $personOid1
      - type: silent
      - deadline: '2019-01-01 10:10:10'
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户自动签署参数校验-个人签署，静默授权过期，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 用户未授权或授权已过期，不允许静默签署']

- test:
    name: 数据准备：对个人账户1静默授权
    variables:
      - accountId: $personOid1
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户自动签署参数校验-sealId不属于用户
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$plat_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'印章和签署主体不匹配']

- test:
    name: 用户自动签署参数校验-signType为1时, 页码不能为空
    variables:
      - flowId: $flowId_check
      - posBean: {'posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 用户自动签署参数校验-signType为1时, X坐标不能为空
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 用户自动签署参数校验-signType为1时, Y坐标不能为空
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 用户自动签署参数校验-signType为2时, 页码不能为空
    variables:
      - flowId: $flowId_check
      - posBean: {'posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1,'signType':2, 'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'骑缝签署：必须指定正确的page']

- test:
    name: 用户自动签署参数校验-signType为2时, Y坐标不能为空
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300}
      - signfield: {'fileId':$fileId1,'signType':2,  'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'骑缝签署：必须指定y']

- test:
    name: 用户自动签署参数校验-signType为2时, 骑缝签
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posY':300}
      - signfield: {'fileId':$fileId1,'signType':2,  'authorizedAccountId':$personOid1,'posBean':$posBean, 'sealId':$p1_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户自动签署参数校验-企业主体，未静默授权
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$orgOid1,'posBean':$posBean, 'sealId':$o_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 用户未授权或授权已过期，不允许静默签署']

- test:
    name: 数据准备：对企业静默授权，且授权过期
    variables:
      - accountId: $orgOid1
      - type: silent
      - deadline: '2019-01-01 10:10:10'
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户自动签署参数校验-企业主体，静默授权过期
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1,'authorizedAccountId':$orgOid1,'posBean':$posBean, 'sealId':$o_sealId}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 用户未授权或授权已过期，不允许静默签署']

- test:
    name: 数据准备：对企业静默授权
    variables:
      - accountId: $orgOid1
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户手动签署参数校验-fileId不属于该流程
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: {'fileId':$fileId2, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
      - message: ${ENV(message2)}
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",$message]

- test:
    name: 用户手动签署参数校验-个人主体签署，signerAccountId是企业账号，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$orgOid1, 'actorIndentityType':'0', 'authorizedAccountId':'','assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'手动签署的签署人必须是个人']

- test:
    name: 用户手动签署参数校验-企业主体签署，signerAccountId是企业账号，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$orgOid1, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'手动签署的签署人必须是个人']

- test:
    name: 用户手动签署参数校验-企业主体签署，authorizedAccountId是个人账号，失败
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'签署主体不是企业']

- test:
    name: 用户手动签署参数校验-sealType支持0-手绘印章，1-模版印章，为空不限制
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield1: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':0,'signType':0}
      - signfield2: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':1,'signType':0}
      - signfield3: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'0,1','signType':0}
      - signfield4: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      - signfield5: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':null,'signType':0}
      - signfield6: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'signType':0}
      - signfields: [$signfield1,$signfield2,$signfield3,$signfield4,$signfield5,$signfield6]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

      #- test:
      #    name: 用户手动签署参数校验-企业主体签署，sealType不支持手绘
      #    variables:
      #      - flowId: $flowId_check
      #      - posBean: {'posPage':'1','posX':300, 'posY':600}
      #      - signfield: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':0,'signType':0}
      #      - signfields: [$signfield]
      #    api: api/iterate_cases/signfieldsCreate_handSign.yml
      #    validate:
#      - eq: ["content.code",1435002]
      #      - contains: ["content.message",'企业签署不支持手绘印章']
      #
      #- test:
      #    name: 用户手动签署参数校验-企业主体签署，sealType为空/null/0,1时，都重置为1-模板印章，只允许在前端展示企业印章
      #    variables:
      #      - flowId: $flowId_check
      #      - posBean: {'posPage':'1','posX':300, 'posY':600}
      #      - signfield1: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'0,1','signType':0}
      #      - signfield2: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':0}
      #      - signfield3: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':null,'signType':0}
      #      - signfield4: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'1', 'authorizedAccountId':$orgOid1,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'signType':0}
      #      - signfields: [$signfield1,$signfield2,$signfield3,$signfield4]
      #    api: api/iterate_cases/signfieldsCreate_handSign.yml
      #    extract:
      #      - signfieldId21: content.data.signfieldBeans.0.signfieldId
      #      - signfieldId22: content.data.signfieldBeans.1.signfieldId
      #      - signfieldId23: content.data.signfieldBeans.2.signfieldId
      #      - signfieldId24: content.data.signfieldBeans.3.signfieldId
      #    validate:
#      - eq: ["content.code",0]
      #
      #- test:
      #    name: 用户手动签署参数校验-查询签署区-确认企业主体签署只有模板章
      #    variables:
      #      - flowId: $flowId_check
      #      - accountId:
      #      - signfieldIds: $signfieldId21,$signfieldId22,$signfieldId23,$signfieldId24
      #    api: api/signflow/signfields/signfieldsSelect.yml
      #    validate:
#      - eq: ["content.data.signfields.0.sealType",'1']
#      - eq: ["content.data.signfields.1.sealType",'1']
#      - eq: ["content.data.signfields.2.sealType",'1']
#      - eq: ["content.data.signfields.3.sealType",'1']
#
- test:
    name: 用户手动签署参数校验-signType默认1
    variables:
      - flowId: $flowId_check
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':0}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 用户手动签署参数校验-查询签署区-signType默认1
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId_check
      - signfieldIds: $signfieldId
      - accountId:
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.signType",1]

- test:
    name: 用户手动签署参数校验-signType=1-单页签署，需要校验posBean，页码和XY坐标不能为空,
    variables:
      - flowId: $flowId_check
      - posBean: {}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':0,'signType':1}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 盖章位置超出文件范围，无法盖章']

- test:
    name: 用户手动签署参数校验-signType=2-骑缝签署，需要校验posBean，页码和Y坐标不能为空
    variables:
      - flowId: $flowId_check
      - posBean: {}
      - signfield: {'fileId':$fileId1, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':0,'signType':2}
      - signfields: [$signfield]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'骑缝签署：必须指定正确的page']

- test:
    name: 取消静默授权：$desc
    variables:
      - desc: '个人1'
      - accountId: $personOid1
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 取消静默授权：$desc
    variables:
      - desc: '机构1'
      - accountId: $orgOid1
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]
