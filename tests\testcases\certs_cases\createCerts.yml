- config:
    name: "收费证书制证接口校验"
    variables:
      - idNo: '331082199211223080'
      - id_No: '331082199211223080'
      - Name: 梁贤红
      - app_id1: ${ENV(appid_cert)}
      - app_id2: ${ENV(appid_NO_cert)}
      - app_id3: ${ENV(appid_NO_cert2)}
      - id_Type: CRED_PSN_CH_IDCARD
    request:
      base_url: "${ENV(footstone_api_url)}"

- test:
    name: "制证接口：证书名称必传，不传报错"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: ''
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: null
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "参数错误: name不能为空"]

- test:
    name: "制证接口：证书类型必传，不传报错"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: ''
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: null
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "参数错误: idType不能为空"]

- test:
    name: "制证接口：证书号必传，不传报错"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: $Name
      - idType: $id_Type
      - idNumber: ''
      - sceneDescription: '测试场景描述'
      - validDuration: null
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "参数错误: idNumber不能为空"]


- test:
    name: "制证接口：区块链证书名称超出长度限制，报错"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: null
      - useBlockChain: 'true'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "name长度为2-25"]


- test:
    name: "制证接口：非区块链证书名称超出长度限制，报错"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度测试超级长度'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: null
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "name长度为2-25"]


- test:
    name: "制证接口：证书类型非法，报错"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '梁贤红'
      - idType: test
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: null
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "idType无效"]



      #- test:
      #    name: "制证接口：是否区块链字段非法"
      #    api: api/user/cert-service/createCerts.yml
      #    variables:
      #      - app_id: $app_id1
      #      - name: '测试'
      #      - idType: $id_Type
      #      - idNumber: $idNo
      #      - sceneDescription: '测试场景描述'
      #      - validDuration: null
      #      - useBlockChain: 'abc'
      #    validate:
#      - eq: ["content.code",110]
#      - eq: ["content.message", "区块链字段非法"]

- test:
    name: "制证接口：区块链场景描述超长，报错"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景
                     描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述
                     测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试
                     场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景
                     描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述
                     测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试
                     场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景 描述测试场景
                     描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述
                     测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试
                     场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景
      描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述
      测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试
      场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景
      描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述
      测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试
      场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景 描述测试场景
      描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述
      测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试
      场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景描述测试场景'
      - validDuration: null
      - useBlockChain: 'true'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "sceneDescription长度为5-1024"]



- test:
    name: "制证接口：区块链制证，文本为空"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: 'true'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "sceneDescription不能为空"]

- test:
    name: "制证接口：appid为空"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: ''
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '1234566'
      - validDuration: null
      - useBlockChain: 'true'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "参数错误: 应用id不能为空"]

- test:
    name: "制证接口：非区块链未配置有效证书"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id3
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "申领证书失败：未配置有效证书，请联系管理员！"]

- test:
    name: "制证接口：非区块链无可用通道"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id2
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "无可用收费通道"]

- test:
    name: "制证接口：制证有效期非法"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: '123'
      - useBlockChain: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",110]
      - eq: ["content.message", "validDuration无效"]

- test:
    name: "制证接口：制证带有效期oneday"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: '1D'
      - useBlockChain: ''
    extract:
      - certId4: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]
          - ne: ["content.data.contentHash",None]
- test:
    name: "查询证书"
    api: api/user/cert-service/getCerts.yml
    teardown_hooks:
      - ${teardown_compare_1D($response)}
    variables:
      - app_id: $app_id1
      - certId: $certId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.idType", "$id_Type"]
      - eq: ["content.data.name", "测试"]
      - eq: ["content.data.idNumber", "$idNo"]
          - ne: ["content.data.txHash", None]
          - ne: ["content.data.contentHash", None]
      - eq: ["result",true]

- test:
    name: "制证接口：非区块链制证带有效期oneyear"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: '1Y'
      - useBlockChain: ''
    extract:
      - certId5: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]
          - ne: ["content.data.contentHash",None]
- test:
    name: "查询证书"
    api: api/user/cert-service/getCerts.yml
    teardown_hooks:
      - ${teardown_compare_1Y($response)}
    variables:
      - app_id: $app_id1
      - certId: $certId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.idType", "$id_Type"]
      - eq: ["content.data.name", "测试"]
      - eq: ["content.data.idNumber", "$idNo"]
      - eq: ["content.data.txHash", ""]
      - eq: ["content.data.contentHash", ""]
      - eq: ["result",true]



- test:
    name: "区块链成功制证"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景描述'
      - validDuration: null
      - useBlockChain: 'true'
    extract:
      - certId1: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]
      - len_gt: ["content.data.contentHash",0]

- test:
    name: "查询证书"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.name", "测试"]
      - eq: ["content.data.idNumber", "$idNo"]
          - ne: ["content.data.txHash", None]
          - ne: ["content.data.contentHash", None]


- test:
    name: "区块链字段为false，文本不为空，非区块链成功制证"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: '测试场景'
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId2: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "查询证书"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.name", "测试"]
      - eq: ["content.data.idNumber", "$idNo"]
      - eq: ["content.data.txHash", ""]
      - eq: ["content.data.contentHash", ""]


- test:
    name: "非区块链成功制证，只传必填字段"
    api: api/user/cert-service/createCerts.yml
    variables:
      - app_id: $app_id1
      - name: '测试'
      - idType: $id_Type
      - idNumber: $idNo
      - sceneDescription: ''
      - validDuration: null
      - useBlockChain: ''
    extract:
      - certId3: content.data.certId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - len_gt: ["content.data.certId",0]
          - ne: ["content.data.contentHash",None]

- test:
    name: "查询证书"
    api: api/user/cert-service/getCerts.yml
    variables:
      - app_id: $app_id1
      - certId: $certId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
