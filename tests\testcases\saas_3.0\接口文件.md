# 创建合同拟定和签署流程接口文档

## 接口信息
​**​请求地址​**​: `POST https://{host}/v3/sign-flow/create-by-sign-template`  
​**​更新时间​**​: 2025-03-18

---

## 请求参数

### 基础参数
| 参数名称 | 类型 | 必选 | 位置 | 说明 |
|---------|------|-----|------|-----|
| `signTemplateId` | string | 是 | body | 流程模板ID |
| `signFlowInitiator` | object | 否 | body | 发起方信息（需与模板所属企业一致） |
| `orgId` | string | 否 | body | 机构账号ID（需实名认证） |
| `transactor` | object | 否 | body | 机构发起方的经办人 |
| `psnId` | string | 否 | body | 经办人账号ID（需实名认证） |
| `initialRemarks` | list | 否 | body | 合同备注（最多1条，300字符） |

---

### 流程配置 (`signFlowConfig`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `signFlowTitle` | string | 是 | 签署任务主题（不含特殊字符） |
| `signFlowExpireTime` | int64 | 否 | 签署截止时间（Unix毫秒时间戳，默认90天后） |
| `autoStart` | boolean | 否 | 自动开启签署流程（默认`true`） |
| `autoFinish` | boolean | 否 | 所有签署完成后自动完结（默认`false`） |
| `notifyUrl` | string | 否 | 回调通知接收地址 |

---

### 签署配置 (`signConfig`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `availableSignClientTypes` | string | 否 | 签署终端类型（默认`1,2`，1=网页/H5/PC，2=支付宝） |
| `autoFillAndSubmit` | boolean | 否 | 自动跳过预填步骤（默认`false`） |
| `editComponentValue` | boolean | 否 | 是否允许修改预填内容（默认`true`） |

---

### 通知配置 (`noticeConfig`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `noticeTypes` | string | 否 | 通知类型（`1`=短信，`2`=邮件，逗号分隔） |
| `examineNotice` | boolean | 否 | 是否通知企业印章审批人员（默认`false`） |

---

### 认证配置 (`authConfig`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `psnAvailableAuthModes` | list | 否 | 个人认证方式：`PSN_MOBILE3`,`PSN_FACE`,`PSN_BANKCARD4` |
| `orgAvailableAuthModes` | list | 否 | 企业认证方式：`ORG_BANK_TRANSFER`,`ORG_ALIPAY_CREDIT`,`ORG_LEGALREP_AUTHORIZATION`,`ORG_LEGALREP` |

---

### 合同配置 (`contractConfig`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `allowToRescind` | boolean | 否 | 是否允许解约（默认`true`） |

---

### 参与方 (`participants[]`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `participantId` | string | 是 | 参与方ID（通过查询模板详情获取） |
| `orgParticipant` | object | 否 | 企业参与方 |
| `psnParticipant` | object | 否 | 个人参与方 |

#### 企业参与方 (`orgParticipant`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `orgId`/`orgName` | string | 二选一 | 企业ID或名称（需实名认证） |
| `transactor` | object | 否 | 经办人信息 |
| `transactorPsnId` | string | 条件必填 | 经办人ID（传入`orgId`时必填） |
| `transactorPsnAccount` | string | 条件必填 | 经办人手机号/邮箱（传入`orgName`时必填） |
| `transactorName` | string | 条件必填 | 经办人姓名（传入`orgName`时必填） |

#### 个人参与方 (`psnParticipant`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `psnId`/`psnAccount` | string | 二选一 | 个人ID或联系方式（需实名认证） |
| `psnName` | string | 条件必填 | 个人姓名（使用`psnAccount`时必填） |

---

### 抄送方 (`addCopiers[]`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `copierPsnInfo` | object | 否 | 个人抄送方 |
| `psnId` | string | 否 | 个人抄送方ID |
| `psnAccount` | string | 否 | 个人手机号/邮箱 |
| `psnName` | string | 否 | 个人姓名 |
| `copierOrgInfo` | object | 否 | 企业抄送方 |
| `orgId`/`orgName` | string | 二选一 | 企业ID或名称 |

---

### 附件 (`addAttachments[]`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `fileId` | string | 否 | 文件ID（通过上传接口获取） |
| `fileName` | string | 否 | 附件名称（不含特殊字符） |

---

### 控件 (`components[]`)
| 参数名称 | 类型 | 必选 | 说明 |
|---------|------|-----|-----|
| `fileId` | string | 否 | 控件所属文件ID（通过模板详情获取） |
| `componentId`/`componentKey` | string | 二选一 | 控件ID或Key |
| `componentValue` | string | 否 | 控件填充值 |

---

## 请求示例
```json
{
  "signTemplateId": "11​**​*087ee0",
  "signFlowConfig": {
    "signFlowTitle": "房屋租赁合同签署",
    "autoFinish": true,
    "notifyUrl": "http://xx.xx.xx/notify",
    "noticeConfig": { "noticeTypes": "1" },
    "signConfig": {
      "availableSignClientTypes": "1",
      "autoFillAndSubmit": true,
      "editComponentValue": false
    },
    "authConfig": {
      "psnAvailableAuthModes": ["PSN_MOBILE3", "PSN_FACE"],
      "orgAvailableAuthModes": ["ORG_BANK_TRANSFER", "ORG_LEGALREP"]
    }
  },
  "participants": [
    {
      "participantId": "7f​**​*debe8fa",
      "orgParticipant": {
        "orgName": "XX有限公司",
        "transactor": {
          "transactorPsnAccount": "153​**​*1110",
          "transactorName": "张三"
        }
      }
    },
    {
      "participantId": "1e9211​**​​**​6",
      "psnParticipant": {
        "psnAccount": "139​**​​**​3333",
        "psnName": "李四"
      }
    }
  ],
  "components": [
    {
      "fileId": "b68a​**​​**​12fg",
      "componentId": "dfdb9d7b84ba111112dd922ded7",
      "componentValue": "填充值001"
    }
  ]
}