import json
from datetime import datetime

import requests
cert_service_token = "eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************.ddQh0I9z7o_N9anPwW2gToG5rtdW2CJmFGIoS4uKJJs"
class 法人():
    signerAccountId = "5872f7dc604f4c2a8ba5b5dca086107e"
    psnAccount = "***********"
class 管理员():
    #haokun的账号
    # signerAccountId = "20cde9316db04c978439840ba9101193"
    # psnAccount = "***********"
    signerAccountId = "25c300258dfb4d1b9d2aa2d0cf54f44b"
    psnAccount = "***********"
class 普通个人():
    signerAccountId = "25c300258dfb4d1b9d2aa2d0cf54f44b"
    psnAccount = "***********"

class 企业():
    authorizedAccountId = "750b0706e753491abdef6bc679910c5e"

class 非实名个人(): #等发到线上后实名
    signerAccountId = "e1ff614fa8274a9497fad7e56465deaa"
    psnAccount = "***********"

class 文件id():
    fileId = "ce44ffd0b1e04959a25814ebefaa7a6b"
def appid数据(appid版本=1):
    """
       全灰          **********  走意愿弹框  领e签宝ca
       全白          **********  走签署弹框  领天津ca
       弹框灰 证书不灰 **********  走意愿弹框  领天津ca
       弹框不灰 证书灰 **********  走老版签署弹框 领天津ca
       """

    if appid版本 == 1:
        appid = "**********"
        secret = "aa686826dbfb68b6d637a2180d56b108"
    elif appid版本 == 2:
        appid = "**********"
        secret = "298bd15b0047b910b1bffb10bb8186c0"
    elif appid版本 == 3:
        appid = "**********"
        secret = "aa686826dbfb68b6d637a2180d56b108"
    else:
        appid = "**********"
        secret = "298bd15b0047b910b1bffb10bb8186c0"
    print(f"使用的appid是{appid}")
    return appid, secret

def 吊销证书(oid):
    global token

    url = 'http://pre.tsign.cn/cloudCertDelete/model'
    headers = {
        'Content-Type': 'application/json',
        'X-Tsign-Service-Id': 'cert-service',
        'X-Tsign-Pre-Access-Token': cert_service_token
    }
    data = {
        "oid": oid
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))

    # 打印响应

    print(response.json())
    print(f"吊销证书{oid}，{response.json()}")

"""signFieldType  签署区类型，默认值为 0
0 - 签章区 （添加印章、签名等）
1 - 备注区（添加备注文字信息等）（点击了解 备注签署）
2 - 独立签署日期（添加单独的签署日期）

actorIndentityType  0个人 2企业 3法人 4经办人

signerType签署方类型，  0 - 个人，1 - 企业/机构，2 - 法定代表人，3 - 经办人
"""
def 获取当前年月日时分秒(name):
    print("当前执行类型是:{}".format(name))
    now = datetime.now()
    res = name + now.strftime("%Y-%m-%d %H:%M:%S")
    res = res.strip().replace(" ", "").replace(":", "-")  # 去除所有空格并替换冒号为连字符
    print(res)
    return res



# 示例调用
def getSignUrl(签署类型=1,appid版本=1,是否吊销证书=False,是否项目环境=1): #管理员  ***********  oid 65ffa4996e6b4a19b8b2cfc6a86661d2
    appid,secret = appid数据(appid版本)
    headers = {
        'X-Tsign-Open-App-Id': appid,
        "X-Tsign-Open-App-Secret": secret,
        'X-Tsign-Open-Auth-Mode': 'secret',
        'Content-Type': 'application/json;charset=UTF-8',

    }


    """ 1法人-法人签  2法人-企业签  3法人-个人签
        4管理员-企业 5管理员-个人
        6个人-企业 7个人-个人
    
    """

    url = 'http://openapi.esign.cn/v1/accounts/createByThirdPartyUserId'

    data ={
  "thirdPartyUserId": "z111zzzzzzzzzzzzzzzz",
  "name": "甘舰戈",
  "idNumber": "******************",
  "idType": "CRED_PSN_CH_IDCARD",
  "mobile": "***********"
}

    response = requests.post(url, headers=headers, json=data)

    print(response.text)
if __name__ == '__main__':
    """  1法人-法人签  2法人-企业签  3法人-个人签 
         4管理员-企业  5管理员-个人  
         6个人-企业     7个人-个人"""

    """
        1全灰          **********  走意愿弹框  领e签宝ca  0219 1528 改成了无需意愿
        2全白          **********  走签署弹框  领天津ca
        3弹框灰 证书不灰 **********  走意愿弹框  领天津ca
        4弹框不灰 证书灰 **********  走老版签署弹框 领天津ca
    """
    runD = [[5]] #1,2,3,4,5,6,7
    for i in runD:
        getSignUrl(签署类型=i,appid版本=3,是否吊销证书=True,是否项目环境=0) #管理员  ***********  oid 65ffa4996e6b4a19b8b2cfc6a86661d2
    # for i in range(1, 8):
    #     getSignUrl(i)

    #0214 已测版本3  把e签宝的全部恢复成天津的