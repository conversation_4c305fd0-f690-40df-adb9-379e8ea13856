- config:
    name: "悟空轩辕整合"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - xuanyuan_sixian: ${ENV(mobile_shield_wukong_appid_oid)}
      - xuanyuan_sixian_name: ${ENV(xuanyuan_sixian_name)}
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - group: ${ENV(envCode)}
      - fileId: ${ENV(fileId)}
      - oid_wang_tsign: ${ENV(oid_wang_tsign)}
      - phone_wang: ${ENV(phone_wang)}
      - mail_wang: ${ENV(mail_wang)}
      - none_tsign_account_id: ${ENV(none_tsign_account_id)}
      - accountId_withRealName_withoutAuth: ${ENV(accountId_withRealName_withoutAuth)}

- test:
    name: 签署人传个人oid，签署主体传同一个oid-个人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询签署区
    variables:
      - flowId: $sign_flowId
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId: content.data.signfields.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询可用印章
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $oid_wang_tsign
      - signerAccountId: $oid_wang_tsign
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - personalSealId: content.data.personalSeals.0.sealId
      - personalSealFileKey: content.data.personalSeals.0.sealFileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 签署时需意愿认证
    variables:
      - flowId: $sign_flowId
      - json: {async: true,accountId: $oid_wang_tsign,updateSignfields: [{sealId: $personalSealId,sealFileKey: $personalSealFileKey,signfieldId: $signfieldId,actorIndentityType: 0,assignedItem: false,assignedPosbean: true,assignedSeal: false, authorizedAccountId: $oid_wang_tsign,autoExecute: false,signDateBeanType: 1,fileId: $fileId,order: 1,posBean: {addSignTime: false, posPage: "1", posX: 500,posY: 500, qrcodeSign: false,width: 500,signDateBean: {posPage: "1", fontName: "simsun", format: "yyyy年MM月dd日",fontSize: 12,posX: 220,posY: 250},sealType: 1,signType: 1,signerAccountId: $oid_wang_tsign}}],approvalPolicy: 0,dingUser: {}}
    api: api/signflow/v3/addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1436201]
      - contains: ["content.message",用户未做意愿认证]

- test:
    name: 获取签署链接-个人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传同一个oid，无发起人-个人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 发起人不允许为空]

- test:
    name: 签署人传个人oid，签署主体传同一个oid，无发起主体-个人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-个人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传同一个oid，无发起人和发起主体，失败-个人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-个人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人手机号，签署主体传相同手机号-个人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-个人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人手机号，签署主体传该手机号对应oid的邮箱号，不允许
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$phone_wang", authorizedAccount: "$mail_wang",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人签署主体登录凭证不一致]

- test:
    name: 签署人传个人邮箱号，签署主体传该邮箱号对应oid的手机号，不允许
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$mail_wang", authorizedAccount: "$phone_wang",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人签署主体登录凭证不一致]

- test:
    name: 签署人传个人邮箱号，签署主体传相同邮箱-个人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$mail_wang", authorizedAccount: "$mail_wang",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-个人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询可用印章
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $oid_wang_tsign
      - signerAccountId: $oid_wang_tsign
    api: api/signflow/signflows/getSignSeals.yml
    #    extract:
    #      - personalSealId: content.data.officialSeals.0.organSeals[?(@.sealUserFlag==false)].sealAlias
    #      - personalSealFileKey: content.data.personalSeals.0.sealFileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 获取签署链接-企业签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起人和发起主体-企业签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-企业签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起人-企业签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",发起人不允许为空]

- test:
    name: 获取签署链接-企业签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起主体
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-企业签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传手机号，签署主体传企业名称
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "esigntest东营伟信建筑安装工程有限公司",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-企业签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传邮箱，签署主体传企业名称
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "<EMAIL>", authorizedAccount: "esigntest东营伟信建筑安装工程有限公司",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-企业签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-法人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起人和发起主体-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-法人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起人-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",发起人不允许为空]

- test:
    name: 获取签署链接-法人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起主体-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-法人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传手机号，签署主体传企业名称-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "esigntest东营伟信建筑安装工程有限公司",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-法人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传邮箱，签署主体传企业名称-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "<EMAIL>", authorizedAccount: "esigntest东营伟信建筑安装工程有限公司",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-法人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无法人签和发起主体-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起人-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",发起人不允许为空]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起主体-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起人和发起主体-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起主体-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传企业oid，无发起主体-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传手机号，签署主体传企业名称-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "esigntest东营伟信建筑安装工程有限公司",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传邮箱，签署主体传企业名称-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "<EMAIL>", authorizedAccount: "esigntest东营伟信建筑安装工程有限公司",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-经办人签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人传个人oid，签署主体传同一个oid-平台自动签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: true, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: true, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 获取签署链接-平台自动签
    variables:
      - accountId: $oid_wang_tsign
      - flowId: $sign_flowId
    api: api/signflow/v3/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 非标准签所属于的发起人，发起失败
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $none_tsign_account_id, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - contains: ["content.message",账号不存在或已注销]

- test:
    name: 非标准签所属于的发起主体，发起失败
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $none_tsign_account_id, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - contains: ["content.message",账号不存在或已注销]

- test:
    name: 发起人未实名
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $accountId_withoutRealName_in_tsign , initiatorAuthorizedAccountId: $oid_wang_tsign , flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437610]
      - contains: ["content.message",账号未实名授权]

- test:
    name: 发起人oid在标准签下不存在
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: ********** , initiatorAuthorizedAccountId: $oid_wang_tsign , flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437123]
      - contains: ["content.message",账号不存在]

- test:
    name: 发起人oid已实名未授权
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $none_tsign_account_id,initiatorAuthorizedAccountId: $oid_wang_tsign , flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - contains: ["content.message", 账号不存在或已注销]

- test:
    name: 发起主体未实名
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign , initiatorAuthorizedAccountId: $accountId_withoutRealName_in_tsign , flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437610]
      - contains: ["content.message",账号未实名授权]

- test:
    name: 发起主体oid在标准签下不存在
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign , initiatorAuthorizedAccountId: 1111111 , flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437123]
      - contains: ["content.message",账号不存在]

- test:
    name: 发起主体oid在已实名未授权
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign , initiatorAuthorizedAccountId: $none_tsign_account_id, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435011]
      - contains: ["content.message",账号不存在或已注销]

- test:
    name: 签署人oid在标准签appid不存在
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "${ENV(account_id_2)}",authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 签署人账号未注册]

- test:
    name: signerAccount传入格式不正确-10位数字
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "**********",authorizedAccount: "<EMAIL>",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437611]
      - contains: ["content.message",手机号或邮箱格式错误]

- test:
    name: signerAccount传入格式不正确-非法邮箱
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "tiangu@tsgin",authorizedAccount: "<EMAIL>",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437611]
      - contains: ["content.message",手机号或邮箱格式错误]

- test:
    name: 签署主体oid在标准签appid不存在
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign",authorizedAccountId: "${ENV(account_id_2)}",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message", 签署主体账号未注册]

- test:
    name: authorizedAccountId=个人oid，actorIndentityType=2/3/4
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign",authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体和签署区主体类型不一致]

- test:
    name: authorizedAccountId=企业oid，actorIndentityType=0
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体和签署区主体类型不一致]

- test:
    name: authorizedAccount传个人手机号，actorIndentityType=2/3/4
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$phone_wang", authorizedAccount: "$phone_wang",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体和签署区主体类型不一致]

- test:
    name: authorizedAccount传个人邮箱，actorIndentityType=2/3/4
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "<EMAIL>", authorizedAccount: "<EMAIL>",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体和签署区主体类型不一致]

- test:
    name: authorizedAccount传企业名称，actorIndentityType=0
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$phone_wang", authorizedAccount: "$xuanyuan_sixian_name",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体和签署区主体类型不一致]

- test:
    name: 用户自动签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: true, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",不允许用户自身签]

- test:
    name: 签署人传个人oid，签署主体传另一个oid
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$accountId_withRealName_withoutAuth",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",不支持代他人签署]

- test:
    name: 签署人传个人手机号，签署主体传不同手机号
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$phone_wang", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人签署主体登录凭证不一致]

- test:
    name: 签署人传个人手机号，签署主体传另一oid的邮箱号
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$mail_wang", authorizedAccount: "<EMAIL>",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人签署主体登录凭证不一致]

- test:
    name: 签署人传个人oid，签署主体传邮箱
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccount: "$mail_wang",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署人传个人oid，签署主体传手机
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccount: "$phone_wang",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署人传邮箱，签署主体传个人oid
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$mail_wang", authorizedAccount: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署人传手机，签署主体传个人oid
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$phone_wang", authorizedAccount: "$oid_wang_tsign",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署人传个人oid，签署主体传企业名称-企业签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccount: "$xuanyuan_sixian_name",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署人传个人oid，签署主体传企业名称-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccount: "$xuanyuan_sixian_name",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署人传个人oid，签署主体传企业名称-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccount: "$xuanyuan_sixian_name",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人手机号-企业签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$phone_wang", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人手机号-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$phone_wang", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人手机号-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$phone_wang", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人邮箱号-企业签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$mail_wang", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人邮箱-法人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$mail_wang", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 3, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人邮箱-经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "$mail_wang", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 4, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 10个签署人
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 11个签署人
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"},{platformSign: false, signOrder: 1, signerAccount: {signerAccount: "***********", authorizedAccount: "***********",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437608]
      - contains: ["content.message",超过数量限制]

- test:
    name: signerAccount为空
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5'}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 0, sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {attachments: $attachments,docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署方账号信息不能为空]

- test:
    name: 签署人传个人oid，签署主体传同一个oid-个人签
    variables:
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: '1,2,3,4,5',"willTypes": "CODE_SMS"}
      - flowInfo: {flowConfigItemBean: {buttonConfig: {propButton: "1,2,15" }}, autoArchive: true, autoInitiate: true, businessScene: "签签署署${get_randomNo()}", contractRemind: 65, initiatorAccountId: $oid_wang_tsign, initiatorAuthorizedAccountId: $oid_wang_tsign, flowConfigInfo: $flowConfigInfo}
      - signers: [{platformSign: false, signOrder: 1, signerAccount: {signerAccountId: "$oid_wang_tsign", authorizedAccountId: "$xuanyuan_sixian",noticeType: ""}, signfields: [{assignedPosbean: true,autoExecute: false, actorIndentityType: 2, sealIds: [],sealId: "", fileId: $fileId, posBean: {posPage: "1", posX: 200, posY: 200},sealType: "", signDateBeanType: "1",signDateBean: {fontSize: 20, posX: 220, posY: 250}, signType: 1, width: 150}], thirdOrderNo: "111"}]
      - json: {docs: $docs,flowInfo: $flowInfo, signers: $signers}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "RPC接口获取签署链接"
    api: api/getSignPageUrl/signPageUrlInput.yml
    variables:
      json: {"accountId":"${oid_wang_tsign}","appScheme":"","authorizedAccountId":"${xuanyuan_sixian}","clientType":"ALL","flowId":"${sign_flowId}","needLogin":false,"redirectDelayTime":3,"redirectUrl":""}
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - contains: [content.message, 成功]