config:
  name: 签署技改项目-add-update-execute接口改造


testcases:

  0721技改项目测试:
    testcase: testcases/technology_upgrading/非实名签印章列表.yml
  企业签署:
    testcase: testcases/technology_upgrading/企业签署.yml
  静默签署:
    testcase: testcases/technology_upgrading/静默签署.yml
  个人骑缝关键字签署:
    testcase: testcases/technology_upgrading/个人-骑缝-关键字签署.yml
  新增位置字段-指定位置签-抄送人:
    testcase: testcases/technology_upgrading/新增位置字段-指定位置签-抄送人.yml
  平台自动签-个人自动签-测试新增的位置字段:
    testcase: testcases/technology_upgrading/平台自动签-个人自动签-测试新增的位置字段.yml
  未添加签署区签署-模拟问题-来自于企业签署:
    testcase: testcases/technology_upgrading/未添加签署区签署-模拟问题-来自于企业签署.yml
  同一主体企业签署两次自由签不成功-模拟问题-来自于企业签署:
    testcase: testcases/technology_upgrading/同一主体企业签署两次自由签不成功-模拟问题-来自于企业签署.yml
  法人章-自由签-悟空-一步发起-模拟问题:
    testcase: testcases/technology_upgrading/法人章-自由签-悟空-一步发起-模拟问题.yml
  法人章-自由签-轩辕-一步发起-模拟问题:
    testcase: testcases/technology_upgrading/法人章-自由签-轩辕-一步发起-模拟问题.yml
  审批流-发起审批-测试war包:
    testcase: testcases/approval_flow/审批流-发起审批-测试war包.yml