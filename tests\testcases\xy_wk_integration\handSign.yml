- config:
    name: "悟空轩辕整合"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - xuanyuan_sixian: ${ENV(mobile_shield_wukong_appid_oid)}
      - xuanyuan_sixian_name: ${ENV(xuanyuan_sixian_name)}
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - account_id_2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id_3_tsign: ${ENV(account_id3_in_tsign)}
      - group: ''
      - fileId: ${ENV(fileId)}
      - oid_wang_tsign: ${ENV(oid_wang_tsign)}
      - phone_wang: ${ENV(phone_wang)}
      - mail_wang: ${ENV(mail_wang)}
      - none_tsign_account_id: ${ENV(none_tsign_account_id)}
      - accountId_withRealName_withoutAuth: ${ENV(accountId_withRealName_withoutAuth)}

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $oid_wang_tsign
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-签署人传个人oid，签署主体传同一个oid
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccountId: "$oid_wang_tsign","authorizedAccountId": $oid_wang_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 查询签署区
    variables:
      - flowId: $sign_flowId
    api: api/mobile-shield/signflows_search_signfields.yml
    extract:
      - signfieldId: content.data.signfields.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 查询可用印章
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $oid_wang_tsign
      - signerAccountId: $oid_wang_tsign
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - personalSealId: content.data.personalSeals.0.sealId
      - personalSealFileKey: content.data.personalSeals.0.sealFileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: 签署时需意愿认证
    variables:
      - flowId: $sign_flowId
      - json: {async: true,accountId: $oid_wang_tsign,updateSignfields: [{sealId: $personalSealId,sealFileKey: $personalSealFileKey,signfieldId: $signfieldId,actorIndentityType: 0,assignedItem: false,assignedPosbean: true,assignedSeal: false, authorizedAccountId: $oid_wang_tsign,autoExecute: false,signDateBeanType: 1,fileId: $fileId,order: 1,posBean: {addSignTime: false, posPage: "1", posX: 500,posY: 500, qrcodeSign: false,width: 500,signDateBean: {posPage: "1", fontName: "simsun", format: "yyyy年MM月dd日",fontSize: 12,posX: 220,posY: 250},sealType: 1,signType: 1,signerAccountId: $oid_wang_tsign}}],approvalPolicy: 0,dingUser: {}}
    api: api/signflow/v3/addUpdateExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1436201]
      - contains: ["content.message",用户未做意愿认证]

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $oid_wang_tsign
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-签署人传个人oid，签署主体传同一个oid
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccountId: "$oid_wang_tsign","authorizedAccountId": $oid_wang_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加个人签名域-签署人传个人oid，签署人传个人手机号，签署主体传相同手机号
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "$phone_wang","authorizedAccount": "$phone_wang","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加个人签名域-签署人传个人手机号，签署主体传该手机号对应oid的邮箱号
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "$phone_wang","authorizedAccount": "$mail_wang","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加个人签名域-签署人传个人邮箱号，签署主体传相同邮箱
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "$mail_wang","authorizedAccount": "$mail_wang","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加个人签名域-签署人传个人邮箱号，签署主体传该邮箱号对应oid的手机号
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "$mail_wang","authorizedAccount": "$phone_wang","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加企业签名域-签署人传个人oid，签署主体传企业oid
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$oid_wang_tsign","authorizedAccountId": $xuanyuan_sixian,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加企业签名域-签署人传手机号，签署主体传企业名称
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccount: "$phone_wang","authorizedAccount": "$xuanyuan_sixian_name","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加企业签名域-签署人传邮箱，签署主体传企业名称
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccount: "$mail_wang","authorizedAccount": "$xuanyuan_sixian_name","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加法人签名域-签署人传个人oid，签署主体传企业oid
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 3,signerAccountId: "$oid_wang_tsign","authorizedAccountId": $xuanyuan_sixian,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加法人签名域-签署人传手机号，签署主体传企业名称
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 3,signerAccount: "$phone_wang","authorizedAccount": "$xuanyuan_sixian_name","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加法人签名域-签署人传邮箱，签署主体传企业名称
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 3,signerAccount: "$mail_wang","authorizedAccount": "$xuanyuan_sixian_name","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加经办人签名域-签署人传个人oid，签署主体传企业oid
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 4,signerAccountId: "$oid_wang_tsign","authorizedAccountId": $xuanyuan_sixian,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加经办人签名域-签署人传手机号，签署主体传企业名称
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 4,signerAccount: "$phone_wang","authorizedAccount": "$xuanyuan_sixian_name","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加经办人签名域-签署人传邮箱，签署主体传企业名称
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 4,signerAccount: "$mail_wang","authorizedAccount": "$xuanyuan_sixian_name","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 签署人oid在标准签appid不存在
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 4,signerAccountId: "${ENV(account_id_2)}","authorizedAccountId": "$oid_wang_tsign","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署人账号未注册]

- test:
    name: signerAccount传入格式不正确-10位数字
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 4,signerAccount: "**********","authorizedAccount": "$phone_wang","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437611]
      - contains: ["content.message",手机号或邮箱格式错误]

- test:
    name: signerAccount传入格式不正确-非邮箱
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 4,signerAccount: "tiangu.cn","authorizedAccount": "$phone_wang","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437611]
      - contains: ["content.message",手机号或邮箱格式错误]

- test:
    name: 签署主体oid在标准签appid不存在-个人签时使用签署人oid
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccountId: "$oid_wang_tsign","authorizedAccountId": "${ENV(account_id_2)}","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 签署主体oid在标准签appid不存在
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$oid_wang_tsign","authorizedAccountId": "${ENV(sixian)}","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体账号未注册]


#签署接口不再做名称长度的限制，交给用户心来做这层的校验
- test:
    name: 签署主体名字过长
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一二三四五六七八九十一","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]

- test:
    name: authorizedAccountId=个人oid，actorIndentityType=2/3/4
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$oid_wang_tsign","authorizedAccountId": "$oid_wang_tsign","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体不是企业]

- test:
    name: authorizedAccountId=企业oid，actorIndentityType=0
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccountId: "$oid_wang_tsign","authorizedAccountId": "$xuanyuan_sixian","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: authorizedAccount传个人手机号，actorIndentityType=2/3/4
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 3,signerAccount: "$phone_wang","authorizedAccount": "$phone_wang","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体不是企业]

- test:
    name: authorizedAccount传个人邮箱，actorIndentityType=2/3/4
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 4,signerAccount: "$mail_wang","authorizedAccount": "$mail_wang","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",签署主体不是企业]

- test:
    name: authorizedAccount传企业名称，actorIndentityType=0
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "$xuanyuan_sixian_name","authorizedAccount": "$xuanyuan_sixian_name","assignedPosbean": false,"signDateBeanType": "1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437611]
      - contains: ["content.message",手机号或邮箱格式错误]

- test:
    name: 签署人传个人oid，签署主体传另一个oid--强制转为签署人oid
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccountId: "$oid_wang_tsign","authorizedAccountId": "$accountId_withRealName_withoutAuth","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $oid_wang_tsign
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 签署人id传与签署人不同的oid，签署主体id传与签署主体不同的oid，oid的优先级更高
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$oid_wang_tsign",signerAccount: "***********","authorizedAccountId": "${ENV(mobile_shield_wukong_appid_oid)}",authorizedAccount: "esigntest东营伟信建筑安装工程有限公司", "assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 查询签署区
    variables:
      - flowId: $sign_flowId
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.signerAccountId", $oid_wang_tsign]
      - eq: ["content.data.signfields.0.authorizedAccountId", "${ENV(mobile_shield_wukong_appid_oid)}"]

- test:
    name: 签署人传个人oid，签署主体传企业名称-企业签
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$oid_wang_tsign",authorizedAccount: "$xuanyuan_sixian_name", "assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人手机号-法人签
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccount: "$phone_wang",authorizedAccountId: "$xuanyuan_sixian", "assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 签署主体传企业oid，签署人传个人邮箱-经办人签
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 2,signerAccount: "$mail_wang",authorizedAccountId: "$xuanyuan_sixian", "assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署人、签署主体账号类型必须一致]

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $oid_wang_tsign
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加11个签署区-失败
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437608]
      - contains: ["content.message",最多允许创建10个]

- test:
    name: 添加10个签署区-成功
    variables:
      - flowId: $sign_flowId
      - json: {signfields: [{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 0,signerAccount: "***********","authorizedAccount": "***********","assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 查詢签署区-流程来源appid与当前appid一致
    variables:
      - flowId: $sign_flowId
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查詢签署区-流程来源appid与当前appid不一致
    variables:
      - flowId: $sign_flowId
      - app_id: ********
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "企业签名域企业名称，不传经办人-无法追加"
    api: api/sign-flow/sign-fields-add.yml
    variables:
      flowId: $sign_flowId
      json: {"signers":[{"noticeConfig":{"noticeTypes":"1,2"},"orgSignerInfo":{"orgName":"${ENV(xuanyuan_sixian_name)}"},"signConfig":{"forcedReadingTime":0,"signOrder":1},"signFields":[{"fileId":"${fileId}","normalSignFieldConfig":{"assignedSealId":"","autoSign":false,"availableSealIds":[],"freeMode":false,"movableSignField":true,"orgSealBizTypes":"","psnSealStyles":"","signFieldPosition":{"acrossPageMode":"","positionPage":"1","positionX":100,"positionY":100},"signFieldSize":20,"signFieldStyle":1},"signDateConfig":{"dateFormat":"","fontSize":0,"showSignDate":0,"signDatePositionX":0,"signDatePositionY":0},"signFieldType":0}],"signerType":1}]}
    validate:
      - eq: [content.code, 1435002]
      - contains: [content.message, 机构签署方orgName非空时transactorInfo不能为空]
