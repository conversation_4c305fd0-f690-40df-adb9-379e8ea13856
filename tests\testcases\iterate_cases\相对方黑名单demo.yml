- config:
    name: "e签盾流程创建-更新-添加签名域"
    base_url: ${ENV(opponent_entity_url)}
    variables:
        - app_id1: ${ENV(opponent_appid)}
        - app_id: ${ENV(opponent_appid)}
        - thirdPartyUserId_person_1: autotest1${get_randomNo()}
        - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
        - app_id1: ${ENV(opponent_appid)}
        - path_pdf1: data/个人借贷合同.pdf
        - path_pdf2: data/有签名无篡改2.pdf
        - path_pdf3: data/被篡改的文档1.pdf
        - path_pdf4: data/附件2.pdf
        - opponent_check_org_oid: ${ENV(opponent_check_org_oid)}
        - opponent_black_org1: ${ENV(opponent_black_org1)}
        - opponent_black_org2: ${ENV(opponent_black_org2)}
        - opponent_black_person1: ${ENV(opponent_black_person1)}
        - opponent_black_person2: ${ENV(opponent_black_person2)}
        - opponent_black_person3: ${ENV(opponent_black_person3)}
        - opponent_black_person4: ${ENV(opponent_black_person4)}
        - group:


- test:
    name: "获取appSecret"
    variables:
        - app_id: $app_id1
    api: api/iterate_cases/getAppInfo.yml
    extract:
        - app_secret: content.appSecret
    validate:
        - eq: ["status_code", 200]
    output:
        -  $app_secret

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
        - app_id: $app_id1
        - app_secret: $app_secret
        - thirdPartyUserId: $thirdPartyUserId_person_ckl
        - name: 陈凯丽
        - idNumber: 362323199201061068
        - idType: CRED_PSN_CH_IDCARD
        - email:
        - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
        - accountId_ckl: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
        - app_id: $app_id1
        - app_secret: $app_secret
        - thirdPartyUserId: caogu20191111411321213
        - name: 梁贤红
        - idNumber: 331082199211223080
        - idType: "CRED_PSN_CH_IDCARD"
        - email:
        - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
        - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
        - app_id: $app_id1
        - app_secret: $app_secret
        - thirdPartyUserId: sixian201911141132
        - creator: $caogu_accountId
        - idNumber: 91341324MA2RMB1W3T
        - idType: CRED_ORG_USCC
        - name: 泗县深泉纯净水有限公司
        - orgLegalIdNumber: 331082199211223080
        - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
        - sixian_organize_id: content.data.orgId

- test:
    name: "查询泗县企业印章"
    variables:
      - app_id: $app_id1
      - app_secret: $app_secret
      - orgId: $sixian_organize_id
    api: api/iterate_cases/searchOrganizationsSeal.yml
    extract:
      - sixian_org_sealId: content.data.seals.0.sealId
    validate:
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询校验方企业印章"
    variables:
      - app_id: $app_id1
      - app_secret: $app_secret
      - orgId: $opponent_black_org1
    api: api/iterate_cases/searchOrganizationsSeal.yml
    extract:
      - opponent_blackorg_sealId: content.data.seals.0.sealId
    validate:
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "查询校验方企业印章"
    variables:
      - app_id: $app_id1
      - app_secret: $app_secret
      - orgId: $opponent_check_org_oid
    api: api/iterate_cases/searchOrganizationsSeal.yml
    extract:
      - opponent_org_sealId: content.data.seals.0.sealId
    validate:
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
        - contentMd5: ${get_file_base64_md5($path_pdf1)}
        - contentType: application/pdf
        - fileName: 个人借贷合同.pdf
        - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
        - uploadUrl: content.data.uploadUrl
        - fileId1: content.data.fileId
    validate:
        - eq: ["content.code",0]
        - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
        - contentMd5: ${get_file_base64_md5($path_pdf1)}
        - contentType: application/pdf
        - binary: ${open_file($path_pdf1)}
    request:
        url: $uploadUrl
        method: PUT
        headers:
            Content-Type: $contentType
            Content-Md5: $contentMd5
        data: $binary
    validate:
        - eq: ["content.errCode", 0]
        - eq: ["content.msg", "成功"]





#验证签署
#同一签署区 - 签署人在签署主体黑名单
- test:
    name: 创建流程 - 签署人在签署主体黑名单40
    variables:
      - app_secret: $app_secret
      - app_id: $app_id1
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - initiatorAuthorizedAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
      - copierAccountId: $accountId_ckl
      - copierIdentityAccountId: $accountId_ckl
    api: api/mobile-shield/v1_signflows.yml
    extract:
      - flowId8: content.data.flowId
    validate:
      - len_gt: ["content.data.flowId",0]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加流程文档
    variables:
        - app_secret: $app_secret
        - app_id: $app_id1
        - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
        - docs: [$doc1]
        - flowId: $flowId8
    api: api/signflow/flowDocuments/documentsCreate.yml
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - eq: ["status_code", 200]
    teardown_hooks:
        - ${hook_sleep_n_secs(5)}

- test:
    name: "添加签署区 - 签署人在签署主体黑名单 - 企业签41"
    variables:
        - app_secret: $app_secret
        - app_id: $app_id1
        - flowId: $flowId8
        - signfields: [{"fileId":$fileId1,"signerAccountId":$opponent_black_person2,"actorIndentityType":"2","authorizedAccountId": $opponent_check_org_oid,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":2,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]

- test:
    name: "开启签署流程42"
    variables:
        - app_secret: $app_secret
        - app_id: $app_id1
        - flowId: $flowId8
    api: api/iterate_cases/signflowsStart.yml
    validate:
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]


-   test:
        name: "获取签署链接" #查看的中间页
        variables:
            -   app_secret: $app_secret
            -   flowId: $flowId8
            -   accountId: $opponent_black_person2
            -   urlType: 0
            -   multiSubject: true
            -   organizeId: $opponent_black_person2
            -   compressContext: true
        api: api/iterate_cases/executeUrl.yml
        extract:
            -   shortUrl: content.data.shortUrl
            -   code: content.code
            -   message: content.message
        validate:
            -   eq: ["content.code",0]



#- test:
#    name: 签署43
#    variables:
#      - accountId: $opponent_black_person2
#      - flowId: $flowId8
#      - posBean: {addSignTime: True, keyword: '', posPage: '1-2', posX: 400, posY: 600, qrcodeSign: True, width: 0}
#      - addSignfields: []
#      - updateSignfield: {authorizedAccountId: $opponent_check_org_oid, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $opponent_org_sealId, signfieldId: $signfieldId1, signerOperatorAuthorizerId: $opponent_check_org_oid, signerOperatorId: $opponent_black_person2}
#      - updateSignfields: [$updateSignfield]
#      - signfieldIds: []
#      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
#      - dingUser:   #钉钉用户数据，钉钉审批时必传
#      - app_secret: $app_secret
#      - app_id: $app_id1
#    api: api/mobile-shield/addUpdateExecute_handsign.yml
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code",1435011]
#      - eq: ["content.message",检测到被加入黑名单的参与方，按照相对方管理规定，不允许与其签署合同]

