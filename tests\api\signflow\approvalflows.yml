#审批流

#查询审批信息
- api:
    def: approvalflowsQuery($approvalId)
    request:
      url: /v1/approvalflows/$approvalId
      method: GET
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]

#同意审批
- api:
    def: approvalflowsAgree($approvalId,$accountId)
    request:
      url: /v1/approvalflows/$approvalId/agree
      method: PUT
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
    validate:
      - eq: [status_code, 200]

#拒绝审批
- api:
    def: approvalflowsRefuse($approvalId,$accountId,$refuseReason)
    request:
      url: /v1/approvalflows/$approvalId/refuse
      method: PUT
      headers: ${get_headers($app_id)}
      json:
        accountId: $accountId
        refuseReason: $refuseReason
    validate:
      - eq: [status_code, 200]

#查询审批详细信息
- api:
    def: approvalflowsDetail($approvalId)
    request:
      url: /v1/approvalflows/$approvalId/detail
      method: GET
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]

#查询审批进度信息
- api:
    def: approvalflowsProgress($approvalId)
    request:
      url: /v1/approvalflows/$approvalId/progress
      method: GET
      headers: ${get_headers($app_id)}
    validate:
      - eq: [status_code, 200]