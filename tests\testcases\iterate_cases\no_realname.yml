- config:
    name: 通过文件创建签署流程-非实名流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "3438757422"
      - app_Id1: "3438757422"

- test:
    name: "通过文件创建流程 （个人企业均未实名"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {
    "docs": [
        {
            "fileEditPwd": "",
            "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
            "fileName": "个人借贷合同.pdf",
            "neededPwd": false
        },
        {
            "fileId": "232d475467a84ce7b5c42926440a2779"
        }
    ],
    "signFlowInitiator": {
        "orgInitiator": {
            "orgId": "705789a4bc6545e7b9cdc4ebadafc177",
            "transactor": {
                "psnId": "ab9e503fb5b24b988da20e2a718a552a"
            }
        }
    },
    "signFlowConfig": {
        "authConfig": {
            "audioVideoTemplateId": "",
            "orgAvailableAuthModes": [
                "ORG_BANK_TRANSFER",
                "ORG_ALIPAY_CREDIT",
                "ORG_LEGALREP_AUTHORIZATION",
                "ORG_LEGALREP"
            ],
            "orgEditableFields": [],
            "psnAvailableAuthModes": [
                "PSN_MOBILE3"
            ],
            "psnEditableFields": [],
            "willingnessAuthModes": [
                "CODE_SMS"
            ]
        },
        "autoFinish": true,
        "autoStart": true,
        "chargeConfig": {
            "chargeMode": 1
        },
        "noticeConfig": {
            "noticeTypes": "1"
        },
        "notifyUrl": "",
        "redirectConfig": {
            "redirectDelayTime": null,
            "redirectUrl": ""
        },
        "signConfig": {
            "availableSignClientTypes": "",
            "showBatchDropSealButton": true
        },
        "signFlowTitle": "1109测试",
        "signFlowExpireTime": ""
    },
    "signers": [
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": null,
            "psnSignerInfo": {
                "psnId": "",
                "psnAccount": "***********"
            },
            "signConfig": {
                "signOrder": 1
            },
            "authConfig": {
              "audioVideoTemplateId": "",
              "orgAvailableAuthModes": [
                "ORG_BANK_TRANSFER",
                "ORG_ALIPAY_CREDIT",
                "ORG_LEGALREP_AUTHORIZATION",
                "ORG_LEGALREP"
              ],
              "orgEditableFields": [ ],
              "psnAvailableAuthModes": [
                "PSN_MOBILE3"
              ],
              "psnEditableFields": [ ],
              "willingnessAuthModes": [
                "CODE_SMS"
              ]
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": false,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": 0,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 0
        },
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": {
                "orgId": "",
                "orgName": "esigntest浙江浙农实业投资有限公司1114",
                "orgInfo": null,
                "transactorInfo": {
                    "psnAccount": "<EMAIL>",
                    "psnId": "",
                    "psnInfo": {
                        "psnName": ""
                    }
                }
            },
            "psnSignerInfo": null,
            "signConfig": {
                "signOrder": 1
            },
            "authConfig": {
                "audioVideoTemplateId": "",
                "orgAvailableAuthModes": [
                    "ORG_BANK_TRANSFER",
                    "ORG_ALIPAY_CREDIT",
                    "ORG_LEGALREP_AUTHORIZATION",
                    "ORG_LEGALREP"
                ],
                "orgEditableFields": [],
                "psnAvailableAuthModes": [
                    "PSN_MOBILE3"
                ],
                "psnEditableFields": [],
                "willingnessAuthModes": [
                    "CODE_SMS"
                ]
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": 0,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 1
        }
    ]
}
    extract:
      - signFlowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]



- test:
    name:  获取实名地址(个人未实名)
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $signFlowId
      - accountId: "27d5dfdf691f4e18b86433dabec1e4a8"
      - json: {
    "bizCtxIds": [
        $signFlowId
    ],
    "billParams": {
        "saleSchemaId": "18",
        "scope": "GENERAL",
        "payerAccountGid": "480f753a53a3413daaae19a33c6156de"
    },
    "redirectUrl": "http://tsign-openservice-pc-3-saas-global.projectk8s.tsign.cn/esign/?context=5qqs3cE&flowId=79158b9ad2e04688b2854422983581c3&organ=true&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2N17JwN5wSfJqjkiLiwVdqnxnnW4ytYDkPSHYEPKG6tAamrDL36bE6rX1E4S9TYddkwS6EC7sm94E5srcQ0MydEc%2Fc1jeT3aLGALr5CnsYp3DQ7UxzpHf%2BGKkn62Y5bojLMqvYBjdnZxweqz7OYEUkUSsLWqpvqOHun%2BKhM4ynWrYqw0WwukKQIvUKeMuz1rCyreYV7wHkzZ4WBpYqgECtEPSYn19IFq9oCmZ3uYvJNPP",
    "clientType": "PC",
    "wukongOid": "734d1c4288084bfc945fe1403eca2d7e",
    "appId": "**********",
    "scene": 1,
    "authorizerIds": "705789a4bc6545e7b9cdc4ebadafc177,734d1c4288084bfc945fe1403eca2d7e"
}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name:  获取企业实名地址
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $signFlowId
      - accountId: "071e6d760f3c46dbbe8f56171e79cbd0"
      - json: {
    "bizCtxIds": [
        $signFlowId
    ],
    "billParams": {
        "saleSchemaId": "18",
        "appId": "**********",
        "scope": "GENERAL",
        "payerAccountGid": "480f753a53a3413daaae19a33c6156de"
    },
    "clientType": "PC",
    "contextInfo": {
        "redirectUrl": "http://tsign-openservice-pc-3-sign-0320.projectk8s.tsign.cn/esign/?context=EE9XdHd&flowId=af3d1aec9d384030bfba63cd7abd913d&organ=true&appId=**********&linkSource=1&bizType=1&tsign_source_type=SIGN_LINK_XUANYUAN&tsign_source_detail=1dD1NfAAcu6I1%2BcZbDyz2N17JwN5wSfJqjkiLiwVdqnxnnW4ytYDkPSHYEPKG6tAamrDL36bE6rX1E4S9TYddkwS6EC7sm94E5srcQ0MydEc%2Fc1jeT3aLGALr5CnsYp3DQ7UxzpHf%2BGKkn62Y5bojLMqvYBjdnZxweqz7OYEUkUSsLWqpvqOHun%2BKhM4ynWrYsjHGDdtcVTpkFS3F8eAwlLpDQw%2F%2B%2Bkh2X%2FLuoz82skMCPTIWL%2Fxcr6YwXidPUGRd"
    },
    "agentAccountId": "734d1c4288084bfc945fe1403eca2d7e"
}



- test:
    name: "通过文件创建流程 （指定意愿）"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {
    "docs": [
        {
            "fileEditPwd": "",
            "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
            "fileName": "个人借贷合同.pdf",
            "neededPwd": false
        },
        {
            "fileId": "232d475467a84ce7b5c42926440a2779"
        }
    ],
    "signFlowInitiator": {
        "orgInitiator": {
            "orgId": "705789a4bc6545e7b9cdc4ebadafc177",
            "transactor": {
                "psnId": "ab9e503fb5b24b988da20e2a718a552a"
            }
        }
    },
    "signFlowConfig": {
        "authConfig": {
            "audioVideoTemplateId": "",
            "orgAvailableAuthModes": [
                "ORG_BANK_TRANSFER",
                "ORG_ALIPAY_CREDIT",
                "ORG_LEGALREP_AUTHORIZATION",
                "ORG_LEGALREP"
            ],
            "orgEditableFields": [],
            "psnAvailableAuthModes": [
                "PSN_BANKCARD4",
                "PSN_MOBILE3"
            ],
            "psnEditableFields": [],
            "willingnessAuthModes": [
                "CODE_SMS"
            ]
        },
        "autoFinish": true,
        "autoStart": true,
        "chargeConfig": {
            "chargeMode": 1
        },
        "noticeConfig": {
            "noticeTypes": "1"
        },
        "notifyUrl": "",
        "redirectConfig": {
            "redirectDelayTime": null,
            "redirectUrl": ""
        },
        "signConfig": {
            "availableSignClientTypes": "3",
            "showBatchDropSealButton": true
        },
        "signFlowTitle": "指定签署方意愿",
        "signFlowExpireTime": ""
    },
    "signers": [
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": null,
            "psnSignerInfo": {
                "psnId": "",
                "psnAccount": "***********"
            },
            "signConfig": {
                "signOrder": 1
            },
            "authConfig": {
                "orgAvailableAuthModes": [
                    "ORG_BANK_TRANSFER",
                    "ORG_ALIPAY_CREDIT",
                    "ORG_LEGALREP_AUTHORIZATION",
                    "ORG_LEGALREP"
                ],
                "psnAvailableAuthModes": [
                    "PSN_BANKCARD4",
                    "PSN_MOBILE3"
                ],
                "willingnessAuthModes": [
                    "CODE_SMS",
                    "PSN_FACE_ALIPAY",
                    "PSN_FACE_TECENT",
                    "PSN_FACE_ESIGN",
                    "PSN_FACE_WECHAT",
                    "PSN_AUDIO_VIDEO_ALIPAY",
                    "PSN_AUDIO_VIDEO_WECHAT"
                ]
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": false,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": 0,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 0
        },
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": {
                "orgId": "",
                "orgName": "esigntest浙江浙农实业投资有限公司1111",
                "orgInfo": null,
                "transactorInfo": {
                    "psnAccount": "***********",
                    "psnId": "",
                    "psnInfo": {
                        "psnName": ""
                    }
                }
            },
            "psnSignerInfo": null,
            "signConfig": {
                "signOrder": 1
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": 0,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 1
        },
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": {
                "orgId": "",
                "orgName": "esigntest浙江浙农实业投资有限公司",
                "orgInfo": null,
                "transactorInfo": {
                    "psnAccount": "***********",
                    "psnId": "",
                    "psnInfo": {
                        "psnName": ""
                    }
                }
            },
            "psnSignerInfo": null,
            "signConfig": {
                "signOrder": 1
            },
            "authConfig": {
                "audioVideoTemplateId": "",
                "orgAvailableAuthModes": [],
                "orgEditableFields": [],
                "psnAvailableAuthModes":[],
                "psnEditableFields": [],
                "willingnessAuthModes": [
                    "PSN_FACE_ALIPAY"
                ]
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "1d5d95daa3124dbeb774b82910bcbfb8",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": false,
                        "availableSealIds": [],
                        "freeMode": true,
                        "movableSignField": true,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": 0,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": null,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 1
        }
    ]
}
    extract:
      - signFlowId: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]


- test:
    name:  获取意愿地址(多主体链接)
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $signFlowId
      - accountId: "27d5dfdf691f4e18b86433dabec1e4a8"
      - json: {
    "bizCtxIds": [
        $signFlowId
    ],
    "bizType": "",
    "billParams": {
        "saleSchemaId": "18",
        "scope": "GENERAL",
        "payerAccountGid": "480f753a53a3413daaae19a33c6156de"
    },
    "redirectUrl": "https://testfront.tsign.cn:8887/esign?context=K3tizms&flowId=7103569dcd074373b134c2e6ebf84a73&organ=true&appId=**********&linkSource=1&bizType=1&cacheKey=b7887dc9c65a478baedc1f5aa67817c5&autoSign=true",
    "clientType": "PC",
    "wukongOid": "27d5dfdf691f4e18b86433dabec1e4a8",
    "appId": "**********",
    "space": "27d5dfdf691f4e18b86433dabec1e4a8",
    "scene": "1",
    "willTypeHided": "5",
    "signSerialId": "",
    "authorizerIds": "3e91cacbd7de4657a68c522f806e7c23,705789a4bc6545e7b9cdc4ebadafc177,27d5dfdf691f4e18b86433dabec1e4a8"
}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]