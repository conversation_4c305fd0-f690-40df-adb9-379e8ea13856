- config:
    name: "0709迭代测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - group: ''
      - app_id: ${ENV(wukong_flowId_appId)}
      - batchSign:

- test:
    name: "yuzan-多主体获取的签署链接的风险提示-3份"
    variables:
      - flowId: ${ENV(wukong_flowId)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(multiSubject)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有3份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

- test:
    name: "yuzan-sixian获取的签署链接的风险提示-2份"
    variables:
      - flowId: ${ENV(wukong_flowId)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(sixian)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有2份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

- test:
    name: "yuzan-yuzan获取的签署链接的风险提示-1份"
    variables:
      - flowId: ${ENV(wukong_flowId)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(yuzan)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有1份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

- test:
    name: "caogu-多主体获取的签署链接的风险提示-2份"
    variables:
      - flowId: ${ENV(wukong_flowId)}
      - queryAccountId: ${ENV(caogu)}
      - querySpaceAccountId: ${ENV(multiSubject1)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有2份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

- test:
    name: "caogu-sixian获取的签署链接的风险提示-1份"
    variables:
      - flowId: ${ENV(wukong_flowId)}
      - queryAccountId: ${ENV(caogu)}
      - querySpaceAccountId: ${ENV(sixian)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有1份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

- test:
    name: "fengjiu-fengjiu,第二签署顺序，获取的签署链接的风险提示-0份"
    variables:
      - flowId: ${ENV(wukong_flowId)}
      - queryAccountId: ${ENV(fengjiu)}
      - querySpaceAccountId: ${ENV(fengjiu)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有0份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]


######################################################################
- test:
    name: "部分签署完成的，yuzan-多主体，获取的签署链接的风险提示-2份"
    variables:
      - flowId: ${ENV(wukong_flowId_part)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(multiSubject1)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有2份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

- test:
    name: "部分签署完成的，yuzan-yuzan，获取的签署链接的风险提示-0份"
    variables:
      - flowId: ${ENV(wukong_flowId_part)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(yuzan)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有0份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

- test:
    name: "部分签署完成的，yuzan-sixian，获取的签署链接的风险提示-2份"
    variables:
      - flowId: ${ENV(wukong_flowId_part)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(sixian)}
    api: api/iterate_cases/riskWarning.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.value", "你有2份电子文件待签署，电子文件与纸质文件具有同等法律效力，请务必仔细阅读和理解文件内容。"]

##########################################################################################
- test:
    name: "未归档的没有出证权限"
    teardown_hooks:
      - ${teardown_giveEvidence($response)}
    variables:
      - flowId: ${ENV(wukong_flowId_testify)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(yuzan)}
      - operator_id: ${ENV(yuzan)}
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["giveEvidence", False]

- test:
    name: "归档的签署人有出证权限"
    teardown_hooks:
      - ${teardown_giveEvidence($response)}
    variables:
      - flowId: ${ENV(wukong_flowId_testify_finsh)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(yuzan)}
      - operator_id: ${ENV(yuzan)}
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["giveEvidence", True]

- test:
    name: "被抄送人没有出证权限"
    teardown_hooks:
      - ${teardown_giveEvidence($response)}
    variables:
      - flowId: ${ENV(wukong_flowId_testify_finsh)}
      - queryAccountId: ${ENV(fengjiu)}
      - querySpaceAccountId: ${ENV(fengjiu)}
      - operator_id: ${ENV(fengjiu)}
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["giveEvidence", False]

#####################################################################################

- test:
    name: "轩辕企业实名签签署人是否有下载权限-传的是任务里面的签署人和签署主体"
    teardown_hooks:
      - ${teardown_download($response)}
    variables:
      - flowId: ${ENV(xuanyuan_flowId_finsh)}
      - queryAccountId: ${ENV(task_yuzan)}
      - querySpaceAccountId: ${ENV(task_sixian)}
      - operator_id: ${ENV(xuanyuan_yuzan)}
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["download", True]

- test:
    name: "轩辕企业实名签签署人是否有下载权限-传的是操作主体和操作人"
    teardown_hooks:
      - ${teardown_download($response)}
    variables:
      - flowId: ${ENV(xuanyuan_flowId_finsh)}
      - queryAccountId: ${ENV(xuanyuan_yuzan)}
      - querySpaceAccountId: ${ENV(xuanyuan_sixian)}
      - operator_id: ${ENV(xuanyuan_yuzan)}
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["download", True]




