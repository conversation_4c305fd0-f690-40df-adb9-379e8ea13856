- config:
    name: 流程增删改查校验
    base_url: ${ENV(footstone_api_url)}
    variables:
      - orgName: 东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: 13344445555
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - personOid1: 4468fe9323bc4bfeaf5f659af14b6754
      - personOid2: 824708b5195546eab28565ebb0203a1e
      - personOid3: 310913bf68594f8bb7c457215e0b4831
      - orgOid1: 4d4352cd0de849ab9daa507397086bf1
      - fileId1: 37492a3be0c64282b834a48f77db3c89
      - m1: "19922222222"
      - m2: "19800000000"
      - e1: <EMAIL>
      - e2: <EMAIL>
      - pOid1: ${getOid($m1)}
      - pOid2: ${getOid($m2)}
      - pOid3: ${getOid($e1)}
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:
      - m3: "***********"
      - pOid_m: ${getOid($m3)}
      - tPUserId1: autotest1${get_randomNo()}
      - appid_xuanyuan: ${ENV(appId_xuanyuan)}
      - m4: "***********"
      - pOid_m1: ${getOid($m4)}
      - client_id: pc
    "request":
      "base_url":
      "headers":
        "Content-Type": "application/json"

- test:
    name: 创建流程：入参校验-initiatorAccountId不存在
    variables:
      - autoArchive: 1
      - contractValidity:
      - initiatorAccountId: dfdffdfdfdfdfd
      - payerAccountId:
      - signValidity:
      - businessScene: 签署流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 发起人账号不存在]
- test:
    name: 创建流程：入参校验-payerAccountId不存在
    variables:
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId: dfdfd4343dc
      - signValidity:
      - autoArchive: 1
      - businessScene: 签署流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 付费方账号不存在]

- test:
    name: 创建流程：入参校验-必填项autoArchive为空字符串
    variables:
      - autoArchive:
      - signValidity:
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId: dfdfd4343dc
      - businessScene: 签署流程
      - configInfo: $configInfo
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 自动归档不能为空]

- test:
    name: 创建流程：入参校验-必填项businessScene为空字符串，失败
    variables:
      - businessScene:
      - autoArchive: true
      - configInfo: $configInfo
      - signValidity:
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", businessScene不能为空]

- test:
    name: 创建流程：入参校验-只传必填项
    variables:
      - autoArchive: true
      - businessScene: 签署流程
      - configInfo: $configInfo
      - signValidity:
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：流程不存在
    variables:
      - flowId: sdsdssdsfdfd
    api: api/signflow/signflows/signflowsSelect.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", 流程不存在]

- test:
    name: 创建流程：入参校验-不传configInfo
    variables:
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreateNOtConfigInfo.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：项目配置了异步通知，则configInfo有默认值
    api: api/signflow/signflows/signflowsSelect.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.configInfo.noticeDeveloperUrl",0]