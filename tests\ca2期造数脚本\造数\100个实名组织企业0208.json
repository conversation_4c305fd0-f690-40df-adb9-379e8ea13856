{"errCode": 0, "message": "成功", "data": {"items": [{"name": "esigntest随便取的企业", "codeType": "CRED_ORG_USCC", "codeValue": "91**************XL", "code": {"CRED_ORG_USCC": "91**************XL"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "15e9ffc3de524e1ea38d2afe41254724", "guid": "9104d267f4894830abc37422de21cec8", "organGuid": "9104d267f4894830abc37422de21cec8", "ouid": "a97780e49e1f48f8bd3a1e87af0cd2fe", "adminInfoList": [{"mobile": null, "email": "131***@tsign.cn", "name": "测试张三", "ouid": "0e0796ba9c9d4594b87f2bfb1d336e84", "accountUid": "71e7b0176791497bb0363fa38bed50b8", "guid": "b3483d8f69b8486d87093365446f03f4", "credentialNo": "43**************79", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "57b29632015a48fe8e3ad4ecb92c0e59", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试张三", "orgLegalNo": "43**************79", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000007489137XL"}, "serviceId": "3759755154342556455", "createSource": "RPC_REGISTER:**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000007489137XL"}, {"name": "esigntest测试企业非epaas模板", "codeType": "CRED_ORG_USCC", "codeValue": "91**************N2", "code": {"CRED_ORG_USCC": "91**************N2"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "ae3a713c075644be92692f39efca4d70", "guid": "d1f569c5f84c4857b01796cbfa1e594d", "organGuid": "d1f569c5f84c4857b01796cbfa1e594d", "ouid": "265210bd57734f61aabd5e363396e5d2", "adminInfoList": [{"mobile": "198****8802", "email": "5UU***@tsign.cn", "name": "明绣", "ouid": "276b5a8f2a964142bb1ebcf0dd82bf72", "accountUid": "0702fdab26904151b96ed0b9a1a4331f", "guid": "6a82a07dc2734389a382c98375908c3c", "credentialNo": "43**************10", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "eea5e912cf0546328da79f513bb60b5f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "明绣", "orgLegalNo": "43**************10", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000034550998N2"}, "serviceId": "3758071688643228496", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000034550998N2"}, {"name": "esigntest泗导师姐夫好", "codeType": "CRED_ORG_USCC", "codeValue": "91**************7W", "code": {"CRED_ORG_USCC": "91**************7W"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "c2e3a2e806bd4c829acb818a8aca056d", "guid": "25222bf9aa6d4f27ac5345653d1929cb", "organGuid": "25222bf9aa6d4f27ac5345653d1929cb", "ouid": "7b7d72816ce64118a1efa74d0be15d4c", "adminInfoList": [{"mobile": "188****3991", "email": "zho***@tsign.cn", "name": "陈凯丽", "ouid": "091bc7d9666146a2b44f699620623a48", "accountUid": "28c3a065c2764feca9715a3237688f12", "guid": "c0d8aac409ea49be9b952abf327ee806", "credentialNo": "36**************68", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "d1e4b0f5c3f54119aa5363bff0f33dcf", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "李保辉", "orgLegalNo": null, "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "91000000106276977W"}, "serviceId": "3740894012845339442", "createSource": "RPC_REGISTER:**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "91000000106276977W"}, {"name": "esigntest新建外部机构实名zNYL", "codeType": "CRED_ORG_USCC", "codeValue": "91**************54", "code": {"CRED_ORG_USCC": "91**************54"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "9942a8e10c9246e2b5801699d846da42", "guid": "bfa3f6ca93b647b6821b17b061c4ee07", "organGuid": "bfa3f6ca93b647b6821b17b061c4ee07", "ouid": "0a5175c19ea94ebc8a10b85ad21286e3", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "45f3b321811f487b91fe40e6c08a7f37", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114529042254"}, "serviceId": "3738031488445723445", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114529042254"}, {"name": "esigntest新建用于实名ExYJ", "codeType": "CRED_ORG_USCC", "codeValue": "91**************30", "code": {"CRED_ORG_USCC": "91**************30"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "2eb18678598f4ce4a324fa33af7c944c", "guid": "98611c7bf9f84e539a81b96f0a3a581a", "organGuid": "98611c7bf9f84e539a81b96f0a3a581a", "ouid": "7e4304113b0f4c0fb8865a8577022fd7", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "0dc7bedcee054359bd4b9e0cbda37d3a", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115859031830"}, "serviceId": "3738029517139620645", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115859031830"}, {"name": "esigntest新建用于实名CmkW", "codeType": "CRED_ORG_USCC", "codeValue": "91**************14", "code": {"CRED_ORG_USCC": "91**************14"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "b932479d3a0942efb0ea3ebd700b2344", "guid": "8a50f0ed2d364016bcd73948635460dc", "organGuid": "8a50f0ed2d364016bcd73948635460dc", "ouid": "516da7bba9c943b79eeca70672fa1f5a", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "6f23ff4a51a94a1387b9c8422242fabf", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115651940414"}, "serviceId": "3738027112293479175", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115651940414"}, {"name": "esigntest新建用于实名RJYL", "codeType": "CRED_ORG_USCC", "codeValue": "91**************19", "code": {"CRED_ORG_USCC": "91**************19"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "caed82faedf1417b8da07f3c8d7055b6", "guid": "a398d22a76664765ba35687b5fa7e659", "organGuid": "a398d22a76664765ba35687b5fa7e659", "ouid": "276c40d8e1fa4bd491bac1589a366a64", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "462f973a94b04a9b920bbc8e4b15824a", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113997652019"}, "serviceId": "3738024774388429593", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113997652019"}, {"name": "esigntest贲顾东徐赵闾机构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************56", "code": {"CRED_ORG_USCC": "91**************56"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "726ff3570037420cad1231fc74d918a2", "guid": "d01932964c9b4bbba69c8480a25fd3e0", "organGuid": "d01932964c9b4bbba69c8480a25fd3e0", "ouid": "0ca63bccb60347339ed22a1a632430c3", "adminInfoList": [{"mobile": "198****2835", "email": null, "name": "测试方容蓝", "ouid": "60bf00afd9fe439381dae8a82d8677a1", "accountUid": "d40964b1f0ce446bbe59133a68bbf1b0", "guid": "7f0afe253e0c4fa6bc3ba26b739c2b15", "credentialNo": "11**************99", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "b8e80c079fbf430186b11f632f3978fa", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试方容蓝", "orgLegalNo": "11**************99", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000000956944156"}, "serviceId": "3737920656764306191", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000000956944156"}, {"name": "esigntest企业零零", "codeType": "CRED_ORG_USCC", "codeValue": "91**************0B", "code": {"CRED_ORG_USCC": "91**************0B"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "cfb02e156395490d8884b4d9320927b8", "guid": "632cab07e7e14848ab409692d8642715", "organGuid": "632cab07e7e14848ab409692d8642715", "ouid": "4e339dcd181b432188bb970870ed6ab2", "adminInfoList": [{"mobile": "198****8526", "email": null, "name": "测试用户去", "ouid": "10db9ab6ff6549d682b62ef3e9028eb4", "accountUid": "6ebbcac12af544afaa6fe3acd16d484d", "guid": "c8f6f43c1fd54309a60b1e7af3bf347b", "credentialNo": "43**************37", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "95b4a3d208c84f07ba5e8b519e07a785", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试用户去", "orgLegalNo": "43**************37", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "91000000176150340B"}, "serviceId": "3737656687185101652", "createSource": "RPC_REGISTER:**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "91000000176150340B"}, {"name": "esigntest自建二期阿朱测试", "codeType": "CRED_ORG_USCC", "codeValue": "91**************94", "code": {"CRED_ORG_USCC": "91**************94"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "a3a23164a41244c892e1bcc5bd443702", "guid": "682d2df91c144a1ea485d0886b8af779", "organGuid": "682d2df91c144a1ea485d0886b8af779", "ouid": "53814038def94c29943ba39b327b21c6", "adminInfoList": [{"mobile": null, "email": null, "name": "自建二期阿朱测试01", "ouid": "cc671e6eb77a4d9f8da9c918a7cb13bb", "accountUid": "c1ecdf6dc7504e209670f20af0198826", "guid": "e4a81397788240b08d4bf599694912d0", "credentialNo": "43**************43", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "ab272888c5194d529686c0e474f8643c", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": null, "orgLegalNo": null, "orgLegalType": null, "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000001209140794"}, "serviceId": "****************", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000001209140794"}, {"name": "esigntest富婆薛的公司BIU", "codeType": "CRED_ORG_USCC", "codeValue": "91**************E6", "code": {"CRED_ORG_USCC": "91**************E6"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "f1439634f3cb4fcab600f5ee5fa8e086", "guid": "96c4922a122540fbbd2ab81e493d35d9", "organGuid": "96c4922a122540fbbd2ab81e493d35d9", "ouid": "143a0faa1c5c4a3b8159f61f68fe8672", "adminInfoList": [{"mobile": "190****0000", "email": null, "name": "测试猫猫零", "ouid": "8a48c66df52e40b4a5b29320eaa87117", "accountUid": "82ccb95657cc4221be4e3e654925a11f", "guid": "df56c319a8dc488cbb037de8155e1241", "credentialNo": "43**************20", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "3f60bb286f1344b5875e74130c9852ec", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试猫猫零", "orgLegalNo": "43**************20", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000048528334E6"}, "serviceId": "3734946402699578686", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000048528334E6"}, {"name": "esigntest新建外部机构实名mVnI", "codeType": "CRED_ORG_USCC", "codeValue": "91**************15", "code": {"CRED_ORG_USCC": "91**************15"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "f83fa84877644cdf91bfe1c7139b3055", "guid": "d14f03518ff24b9bb769b2c0f9578cf4", "organGuid": "d14f03518ff24b9bb769b2c0f9578cf4", "ouid": "619bb81c85c343c9a8fa4779d2001168", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "7d46553c2f904c45b0c9b01df8c6c9f7", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115656232815"}, "serviceId": "3729090952603110698", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115656232815"}, {"name": "esigntest新建用于实名cvqz", "codeType": "CRED_ORG_USCC", "codeValue": "91**************46", "code": {"CRED_ORG_USCC": "91**************46"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "c4f7aa221e3247989b2c2344f7676045", "guid": "3a57ab0d7805456ca273f5fc79d92289", "organGuid": "3a57ab0d7805456ca273f5fc79d92289", "ouid": "575faadbe58449598faac8d6e1ea212e", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "0011b4d86faf42d1a4b731af797cae2a", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114554478646"}, "serviceId": "3729089203309907240", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114554478646"}, {"name": "esigntest新建用于实名vuNL", "codeType": "CRED_ORG_USCC", "codeValue": "91**************58", "code": {"CRED_ORG_USCC": "91**************58"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "d6f3b086694341f0b393bfe96600e902", "guid": "a90321f19b6b45d9bbb138c21be800f5", "organGuid": "a90321f19b6b45d9bbb138c21be800f5", "ouid": "e0d2865520674b7c8b35823a1469e573", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "68d894995d63472e81664c37e68952c3", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113567827958"}, "serviceId": "3729087292770880842", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113567827958"}, {"name": "esigntest新建用于实名KuxL", "codeType": "CRED_ORG_USCC", "codeValue": "91**************77", "code": {"CRED_ORG_USCC": "91**************77"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "831b5df3a3c04757bbe663818172c731", "guid": "3b966b1463ce43dc87aed5f8de94e753", "organGuid": "3b966b1463ce43dc87aed5f8de94e753", "ouid": "6b43dd64cb2a43e6aeba85f5063dd5fc", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "20ca32f7d36d47e1b9096ab03efc36dc", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115610618677"}, "serviceId": "3729085643788651844", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115610618677"}, {"name": "esigntest新建外部机构实名yNzl", "codeType": "CRED_ORG_USCC", "codeValue": "91**************97", "code": {"CRED_ORG_USCC": "91**************97"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "68e83566f25d4c479d0bdd03a76bbb50", "guid": "bb2f601033624508a9b66153b4142b78", "organGuid": "bb2f601033624508a9b66153b4142b78", "ouid": "7653775e7ccc477aa8ef9b6aabd196ad", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "95fd1b352be44f45980ca0b1353074e9", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113517547797"}, "serviceId": "3728892316522908936", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113517547797"}, {"name": "esigntest新建用于实名PFAX", "codeType": "CRED_ORG_USCC", "codeValue": "91**************28", "code": {"CRED_ORG_USCC": "91**************28"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "0928c93661314d909a0956f6b028eb79", "guid": "1a9d3d1efa744ea6ad4f80f1bd7dd3c3", "organGuid": "1a9d3d1efa744ea6ad4f80f1bd7dd3c3", "ouid": "0d47627a08c6401f870928714facf64e", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "39a17d5b668644eabd357627e83e6ee6", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115837968228"}, "serviceId": "3728890593184058631", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115837968228"}, {"name": "esigntest新建用于实名HNtb", "codeType": "CRED_ORG_USCC", "codeValue": "91**************83", "code": {"CRED_ORG_USCC": "91**************83"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "1ec063c0c49042bfa0e9485e4395ca03", "guid": "8476296ffefa4b8b87467137515f1958", "organGuid": "8476296ffefa4b8b87467137515f1958", "ouid": "110e456fa11b43f6b9ae3988aed899c0", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "e22307cbf51d48a3931fca6f63f86176", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113663207683"}, "serviceId": "3728888693852212571", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113663207683"}, {"name": "esigntest新建用于实名dRQF", "codeType": "CRED_ORG_USCC", "codeValue": "91**************46", "code": {"CRED_ORG_USCC": "91**************46"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "e9bd6a74c55f4858809fa06edee626f3", "guid": "b5b87dbbd5324afeae70988553e96fe7", "organGuid": "b5b87dbbd5324afeae70988553e96fe7", "ouid": "2739b2dffbb1445581c1ef2cbee3dfb9", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "d628c7bfc5704d29a17f244e38c952fe", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115012295746"}, "serviceId": "3728887164659305790", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115012295746"}, {"name": "esigntest新建外部机构实名uHlm", "codeType": "CRED_ORG_USCC", "codeValue": "91**************65", "code": {"CRED_ORG_USCC": "91**************65"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "05ab25490cc049d0aefc5cd6def44fcb", "guid": "0fcfb1b202ca471cbe947f84a91381e3", "organGuid": "0fcfb1b202ca471cbe947f84a91381e3", "ouid": "a65973b35f714e5597e80e0a7ef72c89", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "2c61d8c9603d4e4f937525591451305f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113988273765"}, "serviceId": "3727428790713322789", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113988273765"}, {"name": "esigntest苦茶测试********", "codeType": "CRED_ORG_USCC", "codeValue": "91**************34", "code": {"CRED_ORG_USCC": "91**************34"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4b22325a59ad41cd86fbaf14e9591d25", "guid": "62081565dd6e4a26acd73bf169d499a6", "organGuid": "62081565dd6e4a26acd73bf169d499a6", "ouid": "369448f2d88143edaaa153fce10e2dcc", "adminInfoList": [{"mobile": null, "email": "Har***@qq.com", "name": "郑瀚霖", "ouid": "f346d5d9adc54eebabfc21122185c74b", "accountUid": "81726bd86b9c49eea8cb18c77066a9bc", "guid": "084f85ea944c4f698c7821db9fb1300c", "credentialNo": "33**************12", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "79a0a269d5ac4693b7393751f5e119af", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "郑瀚霖", "orgLegalNo": "33**************12", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000999999994634"}, "serviceId": "3727428375175236947", "createSource": "**********", "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000999999994634"}, {"name": "esigntest新建用于实名JONE", "codeType": "CRED_ORG_USCC", "codeValue": "91**************47", "code": {"CRED_ORG_USCC": "91**************47"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "91da97cd1e7f4959becad97da0704b36", "guid": "25d2016e4f934003ab284f694a14fb9c", "organGuid": "25d2016e4f934003ab284f694a14fb9c", "ouid": "e56380d70443498884023b4dadb21936", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "bec0d49133ad43e7afc347561051b731", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113846622147"}, "serviceId": "3727426878496574737", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113846622147"}, {"name": "esigntest新建用于实名MEOR", "codeType": "CRED_ORG_USCC", "codeValue": "91**************70", "code": {"CRED_ORG_USCC": "91**************70"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "eb4facbe3ed542948a7f6833f345d340", "guid": "6b5274514a36437fa50c49f2f0a945b8", "organGuid": "6b5274514a36437fa50c49f2f0a945b8", "ouid": "778bd371743e4177a047cd2e2c2f8124", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "e17d6c1d99264cf0bde3fb1937de8574", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113819603370"}, "serviceId": "3727424857513135412", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113819603370"}, {"name": "esigntest新建用于实名ypTS", "codeType": "CRED_ORG_USCC", "codeValue": "91**************05", "code": {"CRED_ORG_USCC": "91**************05"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "1ec2dc921d5f476f8951348a1ded97e4", "guid": "8a77554d67f84b1db0be237085d35ea4", "organGuid": "8a77554d67f84b1db0be237085d35ea4", "ouid": "fb26d89b7d3748d08ce6568190c6e530", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "6ceffa6a4d234d668632532301f4327c", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113707004705"}, "serviceId": "3727423301678009600", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113707004705"}, {"name": "esigntest新建外部机构实名xauP", "codeType": "CRED_ORG_USCC", "codeValue": "91**************74", "code": {"CRED_ORG_USCC": "91**************74"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "60a6df2fc65840df9635e4510fc182df", "guid": "1f43ed18f70b4f31a8a7666810c081bd", "organGuid": "1f43ed18f70b4f31a8a7666810c081bd", "ouid": "1beb74a275ca4997a2230a1268984593", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "2e8e0aa032b941a3817d87d7604ee7db", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114700852274"}, "serviceId": "3726581303370845470", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114700852274"}, {"name": "esigntest新建用于实名QyzX", "codeType": "CRED_ORG_USCC", "codeValue": "91**************91", "code": {"CRED_ORG_USCC": "91**************91"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "18d8b5ef35a147cebd701a5b27a9f28c", "guid": "b2b2ba9de39241a78f6fa814f07b8115", "organGuid": "b2b2ba9de39241a78f6fa814f07b8115", "ouid": "bf4c93b908b54412978f61455b69caf7", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "12b287c398a3412e82c9c5240b1dcc44", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113230063991"}, "serviceId": "3726579811859565835", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113230063991"}, {"name": "esigntest新建用于实名AWPw", "codeType": "CRED_ORG_USCC", "codeValue": "91**************79", "code": {"CRED_ORG_USCC": "91**************79"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4113dc8025f441b3b1d23dd35101d072", "guid": "02cd3f69f39549518139513e9875e4fe", "organGuid": "02cd3f69f39549518139513e9875e4fe", "ouid": "5aed8c8d801c4b9086669a9697477df2", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "6bdd966ea8a34c4aad35ed2a50a578a6", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113582246779"}, "serviceId": "3726577972237831432", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113582246779"}, {"name": "esigntest新建用于实名tEgX", "codeType": "CRED_ORG_USCC", "codeValue": "91**************27", "code": {"CRED_ORG_USCC": "91**************27"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "94ea81a4432b43fbac97b9548ef3dc5f", "guid": "bc96e2b1a3114909b07b7735ad5fe62d", "organGuid": "bc96e2b1a3114909b07b7735ad5fe62d", "ouid": "76c8ccb4c1fa47e492f7bb16af636caa", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "2359bc9c00ae42e0becc9c1d5829f58f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114571042827"}, "serviceId": "3726576598603271504", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114571042827"}, {"name": "esigntest测试有年基础版2", "codeType": "CRED_ORG_USCC", "codeValue": "91**************7F", "code": {"CRED_ORG_USCC": "91**************7F"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4b89296f65bf4b41b6325226dcc431da", "guid": "200337ec7999414fa869686c243ed362", "organGuid": "200337ec7999414fa869686c243ed362", "ouid": "facf560e861041d3a944e317059e6521", "adminInfoList": [{"mobile": "150****9157", "email": null, "name": "刘森林", "ouid": "d7f439aee37740149318874d0f0fa82f", "accountUid": "995a100415a94e6e9783912135ec6f75", "guid": "463b1e973fbe42da8371cbea00ae03d5", "credentialNo": "41**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "WE_CHAT", "thirdpartyUserId": "oX8Dg0y2i1YiXDB0HE4D3J4Ak4ZM"}], "roleAmount": 1, "organId": "30299f237ea0423290e8dd7b998b00f1", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "刘森林", "orgLegalNo": "41**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "91000000540297417F"}, "serviceId": "3726522073372691797", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "91000000540297417F"}, {"name": "esigntest测试有年基础版", "codeType": "CRED_ORG_USCC", "codeValue": "91**************BM", "code": {"CRED_ORG_USCC": "91**************BM"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "7d3b419ef36249c1848ca340c6c8db97", "guid": "97fdf8a9e1724e789bd62bc59aeb0415", "organGuid": "97fdf8a9e1724e789bd62bc59aeb0415", "ouid": "a29f4dc01bd1406a9c37a5a12814ef8a", "adminInfoList": [{"mobile": "150****9157", "email": null, "name": "刘森林", "ouid": "d7f439aee37740149318874d0f0fa82f", "accountUid": "995a100415a94e6e9783912135ec6f75", "guid": "463b1e973fbe42da8371cbea00ae03d5", "credentialNo": "41**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "WE_CHAT", "thirdpartyUserId": "oX8Dg0y2i1YiXDB0HE4D3J4Ak4ZM"}], "roleAmount": 1, "organId": "8d240fe13abc4f3aa7aa919bf873c563", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "刘森林", "orgLegalNo": "41**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000053964218BM"}, "serviceId": "3726520619341712696", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000053964218BM"}, {"name": "esigntest闰土企业", "codeType": "CRED_ORG_USCC", "codeValue": "91**************5E", "code": {"CRED_ORG_USCC": "91**************5E"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "511e30e0a5234ce5a47af9211e802f6a", "guid": "c95fb7e8abb440dda166a30bddb6d74f", "organGuid": "c95fb7e8abb440dda166a30bddb6d74f", "ouid": "92e910f5740a41c79dcc996d595b1e11", "adminInfoList": [{"mobile": "133****5491", "email": null, "name": "测试闰土", "ouid": "ce1ea79a43b446ab8a751bf0104e629e", "accountUid": "6e6ef2e12f0c41f19e280d1208bfb3f2", "guid": "c07dbadc98094fcd8e22762862ff5325", "credentialNo": "36**************3X", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "41901dbaa0ef41f7bcbe93eefa7013e3", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试闰土外部", "orgLegalNo": "", "orgLegalType": "", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "91000000508527515E"}, "serviceId": "3736046085656284442", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "91000000508527515E"}, {"name": "esigntest麦当劳", "codeType": "CRED_ORG_USCC", "codeValue": "91**************9K", "code": {"CRED_ORG_USCC": "91**************9K"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "7036bdabde2a412688056b9b1cd7cdb9", "guid": "4bf161c4b6cd4e419f52295d10ca5118", "organGuid": "4bf161c4b6cd4e419f52295d10ca5118", "ouid": "06b21eae7bef4999949bf3a1c269a6e1", "adminInfoList": [{"mobile": "186****3963", "email": null, "name": "陈文涛", "ouid": "b84ce4f309314878be05cdacfb00bf7d", "accountUid": "d510d21a22ef4a518cecf99b6a63d8bd", "guid": "f39fc573e7aa4c32add3ba5c097dc8f4", "credentialNo": "36**************1X", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "DING_TALK", "thirdpartyUserId": "ghZ6Jj8dw4H1W5ICW3j61wiEiE"}], "roleAmount": 1, "organId": "17056f7a43c04c279cb5bb8cf6fc8c75", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "小麦", "orgLegalNo": "43**************5X", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "91000000401393039K"}, "serviceId": "3726289216788237635", "createSource": "RPC_REGISTER:**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "91000000401393039K"}, {"name": "esigntest测试有年基础版", "codeType": "CRED_ORG_USCC", "codeValue": "91**************2U", "code": {"CRED_ORG_USCC": "91**************2U"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "36a1368d0c51404b8a687693ee70d8a2", "guid": "a11f1c352bb442ed932c4435e3c16cab", "organGuid": "a11f1c352bb442ed932c4435e3c16cab", "ouid": "6b86bf0ddc9240c9a0f089619e5321e5", "adminInfoList": [{"mobile": "150****9157", "email": null, "name": "刘森林", "ouid": "d7f439aee37740149318874d0f0fa82f", "accountUid": "995a100415a94e6e9783912135ec6f75", "guid": "463b1e973fbe42da8371cbea00ae03d5", "credentialNo": "41**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "WE_CHAT", "thirdpartyUserId": "oX8Dg0y2i1YiXDB0HE4D3J4Ak4ZM"}], "roleAmount": 1, "organId": "2080b63f10d7457c9337b8f2cac653b9", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "刘森林", "orgLegalNo": "41**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "91000000397704382U"}, "serviceId": "3726281940408995123", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "91000000397704382U"}, {"name": "esigntest司厍艾罗柳古相对方机构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************34", "code": {"CRED_ORG_USCC": "91**************34"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "51a576c7d529477dab95bb26acf84996", "guid": "a7260867c2f94ce39e20009ae1212ff7", "organGuid": "a7260867c2f94ce39e20009ae1212ff7", "ouid": "2db96687f684481992ab47820428df45", "adminInfoList": [{"mobile": "198****0447", "email": null, "name": "测试外部慕况百", "ouid": "f9257dafcd354966ba3433e260c1ef16", "accountUid": "ffabf7fc03564702a91886df41998ab5", "guid": "264e9d5943034ff99a3a008b596089ec", "credentialNo": "11**************43", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "708ee33b88de434d90af2bb552311348", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试外部慕况百", "orgLegalNo": "11**************43", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000000810307034"}, "serviceId": "3724838267632749907", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000000810307034"}, {"name": "esigntest新建外部机构实名zFxf", "codeType": "CRED_ORG_USCC", "codeValue": "91**************26", "code": {"CRED_ORG_USCC": "91**************26"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4043046c4f684c86aa9c43bb96c0e9aa", "guid": "1dbba4e9f01b4aebb93ff7f59f97e9dd", "organGuid": "1dbba4e9f01b4aebb93ff7f59f97e9dd", "ouid": "03a43fef86c5426d8a1044841c55421f", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "ed7fc71cd28441ad96e1ec817d67f82e", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115635889026"}, "serviceId": "3721594164077923618", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115635889026"}, {"name": "esigntest新建用于实名MloG", "codeType": "CRED_ORG_USCC", "codeValue": "91**************23", "code": {"CRED_ORG_USCC": "91**************23"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "068c0678b5bb450b91a2e4ba4263e05a", "guid": "09dc7d51575b424c9692b320cef9e713", "organGuid": "09dc7d51575b424c9692b320cef9e713", "ouid": "4a10d4f6590c4a4992a25fe06417cf04", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "f07d050ec6414454b4b5517fe10af1cf", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115112664423"}, "serviceId": "3721592200791330095", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115112664423"}, {"name": "esigntest新建用于实名sEiH", "codeType": "CRED_ORG_USCC", "codeValue": "91**************74", "code": {"CRED_ORG_USCC": "91**************74"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "e54332a5fce4419b9a9bc637f06a02da", "guid": "db26b08187344fa3a9a3c136d0d915ab", "organGuid": "db26b08187344fa3a9a3c136d0d915ab", "ouid": "1a01eccd476543638566e17a51cc8524", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "6c2aac2b6fdf468a8df8405906f735a4", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115732797074"}, "serviceId": "3721590150581980465", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115732797074"}, {"name": "esigntest新建用于实名nchW", "codeType": "CRED_ORG_USCC", "codeValue": "91**************71", "code": {"CRED_ORG_USCC": "91**************71"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "90ba7212f2d242dab0deabf1ff53928e", "guid": "ab1d3ebcc5b44dc3bd07f9ec1e01e986", "organGuid": "ab1d3ebcc5b44dc3bd07f9ec1e01e986", "ouid": "192eaa2566034a2ab9ae45730fc7ab31", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "51b8005fd8174a03bcbfaa17e6dfa98d", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118720734971"}, "serviceId": "3721588707338751251", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118720734971"}, {"name": "esigntest倪平于孟廉仇机构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************62", "code": {"CRED_ORG_USCC": "91**************62"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "b6adf0cb9b154bc791e1c9bdaa0f7903", "guid": "30492e023fe44b1ea955611e7a9acccd", "organGuid": "30492e023fe44b1ea955611e7a9acccd", "ouid": "0d54694453fd49a3818e320e6bc55f10", "adminInfoList": [{"mobile": "198****4184", "email": null, "name": "测试古云樊", "ouid": "5c8672d4169b4512a0040b2b0bb21edf", "accountUid": "4500e11e298b491c89a069b5ca490edf", "guid": "ccc882bab6d54fa681d32446bd9320d9", "credentialNo": "33**************45", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "2061adc48d2745819ea4f5c15e0867ca", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试古云樊", "orgLegalNo": "33**************45", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000000884514262"}, "serviceId": "3718723839313055022", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000000884514262"}, {"name": "esigntest新建外部机构实名eKmc", "codeType": "CRED_ORG_USCC", "codeValue": "91**************33", "code": {"CRED_ORG_USCC": "91**************33"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "16cd770f317140d3a0ed8c694efc831e", "guid": "ee2bdddc797e4a0eae1f1879d6f2675e", "organGuid": "ee2bdddc797e4a0eae1f1879d6f2675e", "ouid": "4104cfa911474411938c6fcd859e4a22", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "ac782b3ae80e482b97e75bb95e880d8f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118221500533"}, "serviceId": "3717929046538456330", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118221500533"}, {"name": "esigntest新建用于实名xFFI", "codeType": "CRED_ORG_USCC", "codeValue": "91**************66", "code": {"CRED_ORG_USCC": "91**************66"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "f1a27082288b47a8aafb7d8e4f9bd6dc", "guid": "cb83ba7583184045a46b71cffad0b85d", "organGuid": "cb83ba7583184045a46b71cffad0b85d", "ouid": "e32b02f809f54f2db8ad95ab497ad5f9", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "9e3f29313da541dcaaf583053008a564", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113451761166"}, "serviceId": "3717927256577281367", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113451761166"}, {"name": "esigntest新建用于实名Kvww", "codeType": "CRED_ORG_USCC", "codeValue": "91**************70", "code": {"CRED_ORG_USCC": "91**************70"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "8dd2a47134bb4f44af0083bad0324647", "guid": "5527066adf4d46f7abae4b500ed41ca5", "organGuid": "5527066adf4d46f7abae4b500ed41ca5", "ouid": "80097ba01f7746088365acf4eb0b669f", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "9c566a16f41a43b3a0dbb4d71d7eb120", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115966784070"}, "serviceId": "3717925295522057515", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115966784070"}, {"name": "esigntest新建用于实名RWtI", "codeType": "CRED_ORG_USCC", "codeValue": "91**************53", "code": {"CRED_ORG_USCC": "91**************53"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "a2bc69d42b27407b927c553657492df9", "guid": "06851dfaf6554474a5aabe093f5a5e95", "organGuid": "06851dfaf6554474a5aabe093f5a5e95", "ouid": "71c03c36ca0e421792bf6e97591bdf64", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "5ed09586ff3d4e319436f98fbe7ecbcc", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113283369853"}, "serviceId": "3717923************", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113283369853"}, {"name": "esigntest华沙测试外部企业", "codeType": "CRED_ORG_USCC", "codeValue": "91**************EK", "code": {"CRED_ORG_USCC": "91**************EK"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "581cae522e4740c7b1a510dfb5cb94b2", "guid": "244a057aabeb46b0a53a9bceec35e568", "organGuid": "244a057aabeb46b0a53a9bceec35e568", "ouid": "23eded9b4f244c0892764fd8d4e4ff5a", "adminInfoList": [{"mobile": "159****7965", "email": null, "name": "测试楼豆豆豆", "ouid": "43c0fb48a37540a6a12017335b984908", "accountUid": "881d8a12af424f6984207eaa24aa0ed6", "guid": "5b472584179c491eb281f52fb96c81c5", "credentialNo": "42**************93", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 3, "organId": "edbc50636ca64999959856786c61ffa7", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试楼豆豆豆", "orgLegalNo": "", "orgLegalType": "", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000027728675EK"}, "serviceId": "3717696839668469080", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000027728675EK"}, {"name": "esigntest华沙测试组织2", "codeType": "CRED_ORG_USCC", "codeValue": "91**************NM", "code": {"CRED_ORG_USCC": "91**************NM"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "9684c0e78a5e4f898ddae90e9ceb1b5b", "guid": "f9f03144b5ea431dbf6fe748e5c1ce12", "organGuid": "f9f03144b5ea431dbf6fe748e5c1ce12", "ouid": "59587d5d6570462f8af16d1074585924", "adminInfoList": [{"mobile": "159****7968", "email": null, "name": "测试华沙测试组织法人", "ouid": "9f797071e3264f2ca8e0e5889045bddb", "accountUid": "4d90571f2eb64a259b436947bfc2a074", "guid": "230a991c156f4016bb53188a8b732dc6", "credentialNo": "11**************32", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "87d89a505299485a8dfc5c6ebd392e8a", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试华沙测试组织法人", "orgLegalNo": "11**************32", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000018678015NM"}, "serviceId": "3717551639272885533", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000018678015NM"}, {"name": "esigntest华沙测试组织", "codeType": "CRED_ORG_USCC", "codeValue": "91**************W3", "code": {"CRED_ORG_USCC": "91**************W3"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "16dc9c88bdd045dea7b602e2ed5ecbe1", "guid": "6752c8f584d34a608f71e9087cd145b8", "organGuid": "6752c8f584d34a608f71e9087cd145b8", "ouid": "7bce1c8e9f044b6eafe2319088b939c2", "adminInfoList": [{"mobile": "159****7968", "email": null, "name": "测试华沙测试组织法人", "ouid": "9f797071e3264f2ca8e0e5889045bddb", "accountUid": "4d90571f2eb64a259b436947bfc2a074", "guid": "230a991c156f4016bb53188a8b732dc6", "credentialNo": "11**************32", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "9e09b88ac6f743128e31ecd194b9b6b8", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试华沙测试组织法人", "orgLegalNo": "11**************32", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000005620909W3"}, "serviceId": "3717325515318103354", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000005620909W3"}, {"name": "esigntest幸璩仇从公辛机构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************03", "code": {"CRED_ORG_USCC": "91**************03"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "2cbf32354a0b4d46af341c106370dc40", "guid": "d3bf00c5be474010add3650b24bf5e3d", "organGuid": "d3bf00c5be474010add3650b24bf5e3d", "ouid": "a000b1a225674f6d9549a77c9c148ed2", "adminInfoList": [{"mobile": "198****0671", "email": null, "name": "测试杜单倪", "ouid": "e28196f2e1414ee2a3ff14aea1cc957b", "accountUid": "443c4159917645d7987de58e51619567", "guid": "19ceccb0207446e0956ea0cc45bf11c1", "credentialNo": "11**************92", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "b1e098edcf3443bdb49ad93cb4ae4908", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试杜单倪", "orgLegalNo": "11**************92", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000000385308203"}, "serviceId": "3716143922033462573", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000000385308203"}, {"name": "esigntest宰桑百鲍朱仇机构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************40", "code": {"CRED_ORG_USCC": "91**************40"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "b0e27d608d05463aa69a86a261572db6", "guid": "7582fd191f0b4af79754cebd7838e61f", "organGuid": "7582fd191f0b4af79754cebd7838e61f", "ouid": "80ab2c98a6474df48d7014cb85e0843d", "adminInfoList": [{"mobile": "198****8171", "email": null, "name": "测试空谢黄", "ouid": "b561f99c507740d0a304cb109de392bc", "accountUid": "3848a46fae1f4fc6bb4492825377658a", "guid": "0add22c2c7cf4ae092beb259aa4a3d8f", "credentialNo": "50**************71", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "5c91a63e025a4d268e1f60869fa4c455", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试空谢黄", "orgLegalNo": "50**************71", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000000131862140"}, "serviceId": "3716136381396290843", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000000131862140"}, {"name": "esigntest浙江CA中招协测试企业变更", "codeType": "CRED_ORG_USCC", "codeValue": "91**************A5", "code": {"CRED_ORG_USCC": "91**************A5"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4c94b074d5284feda02946a85ad07b9f", "guid": "9aadfe4ebf094d799e6c6ea6557b858a", "organGuid": "9aadfe4ebf094d799e6c6ea6557b858a", "ouid": "1af667c683e9494c981c048e81e46fdb", "adminInfoList": [{"mobile": "136****6023", "email": "hup*@tsign.cn", "name": "姜娇", "ouid": "d7e578af57d148d9810d0d69218cb18f", "accountUid": "c177434a71e64aef90fb08146ac16b77", "guid": "d5017987977d4913b9a4c08a7acd4939", "credentialNo": "42**************42", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "WECHAT_MINI_OPEN", "thirdpartyUserId": "oeCJr5aPhhF3j-TZrbjukF90PHeg"}], "roleAmount": 1, "organId": "8420637a8c29452da451b48e3163d7c3", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "姜娇", "orgLegalNo": "42**************42", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000017679383A5"}, "serviceId": "3715846700800675112", "createSource": "ZQB", "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:PORTAL_ZQB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000017679383A5"}, {"name": "esigntest谷冯孙衡尹雍机构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************77", "code": {"CRED_ORG_USCC": "91**************77"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "092818da4b0a49b88271a2deb4434f81", "guid": "5e81aa8039c24b16875a6a68a7336d77", "organGuid": "5e81aa8039c24b16875a6a68a7336d77", "ouid": "ece77544129a4a5da059059b2cedb798", "adminInfoList": [{"mobile": "198****9355", "email": null, "name": "测试班项尹", "ouid": "d75a00d9281045a69c68b20941612851", "accountUid": "b5606acc94b34241ad335c6c2c921ce6", "guid": "0a44d95c76ca4657826bfeb411091bd9", "credentialNo": "22**************90", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "f79363535826411d9782518e0322dcfb", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试班项尹", "orgLegalNo": "22**************90", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000000815577477"}, "serviceId": "3715835294323838273", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000000815577477"}, {"name": "esigntest测试乌野", "codeType": "CRED_ORG_USCC", "codeValue": "91**************HL", "code": {"CRED_ORG_USCC": "91**************HL"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "6bd959ecc1b143ef895e4247610be1a7", "guid": "964e22237aaa40c8902b4e22de645bd3", "organGuid": "964e22237aaa40c8902b4e22de645bd3", "ouid": "7e5a2670b12e4535b97bc4103d3a03b2", "adminInfoList": [{"mobile": "198****0846", "email": null, "name": "测试日向翔阳", "ouid": "172f27979cea445baee394604cee1483", "accountUid": "2171b95f3a874beebb09ac4a30a77bf2", "guid": "12f34301bc764003b145300163db2572", "credentialNo": "43**************51", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "b89017b069fa4b1e946dc6eeaf5568c0", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试日向翔阳", "orgLegalNo": "43**************51", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000067858238HL"}, "serviceId": "3729300444917796099", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000067858238HL"}, {"name": "esigntest桂橙新建组织最新", "codeType": "CRED_ORG_USCC", "codeValue": "91**************QA", "code": {"CRED_ORG_USCC": "91**************QA"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "fb11928e2c9747e8b2d53dfa1b2cc660", "guid": "c619abed0e024a549f8bae47389d79ed", "organGuid": "c619abed0e024a549f8bae47389d79ed", "ouid": "5b8daf01de054638b2006ace1fd06e93", "adminInfoList": [{"mobile": null, "email": null, "name": "测试桂橙新建最新", "ouid": "37b5a2c2d679461db669b8e21e0bc74e", "accountUid": "ec84573d5e91439eadfdd532ffc84a1a", "guid": "7cf4c0be5bf94016834448793f7f05a3", "credentialNo": "43**************12", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "e708c8aeae764df5855ff9ef0389da70", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试桂橙新建最新", "orgLegalNo": "43**************12", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000049346803QA"}, "serviceId": "3714700730343886123", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000049346803QA"}, {"name": "esigntest令羽企业", "codeType": "CRED_ORG_USCC", "codeValue": "91**************94", "code": {"CRED_ORG_USCC": "91**************94"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "2bca4c7018d54d3ea84bb1eeb9338ed2", "guid": "369876007d8e41a2a3ba02a54310a383", "organGuid": "369876007d8e41a2a3ba02a54310a383", "ouid": "27e4724fcd06460fa0ed6e2cfdfedd9a", "adminInfoList": [{"mobile": "131****7570", "email": "lin***@tsign.cn", "name": "赵金宝", "ouid": "f4299c63907e404087915b08b563450f", "accountUid": "1e06f72dd19e4b67bf13872f81a846d1", "guid": "085556bd3a744208bed2d00f9faf25fe", "credentialNo": "34**************77", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "1353b8470ed34e799e7eda3aa180cc8e", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "赵金宝", "orgLegalNo": "34**************77", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": "e2302a7c-ea0c-4e8b-b6f2-6fdd2995ba44esigntest令羽企业", "thirdpartyUserType": "_DEFAULT_USER", "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "917589379857987894"}, "serviceId": "3714654831488011600", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "917589379857987894"}, {"name": "esigntest赵家帮", "codeType": "CRED_ORG_USCC", "codeValue": "91**************47", "code": {"CRED_ORG_USCC": "91**************47"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4fdd51f32306451881a53c84ed0b50fa", "guid": "9264276266874f60b9e0154ecd578dd0", "organGuid": "9264276266874f60b9e0154ecd578dd0", "ouid": "19e0379c7fec4d81a0ac4680339d981c", "adminInfoList": [{"mobile": "131****7570", "email": "lin***@tsign.cn", "name": "赵金宝", "ouid": "f4299c63907e404087915b08b563450f", "accountUid": "1e06f72dd19e4b67bf13872f81a846d1", "guid": "085556bd3a744208bed2d00f9faf25fe", "credentialNo": "34**************77", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "e8bd7bd38c354c43a86f6b557d179eda", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "赵金宝", "orgLegalNo": null, "orgLegalType": null, "thirdpartyUserId": "1bc380b4-fd2c-4d2b-9164-0e09c50ed61designtest赵家帮", "thirdpartyUserType": "_DEFAULT_USER", "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "917481274987198247"}, "serviceId": "3714652076467817772", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "917481274987198247"}, {"name": "esigntest令羽企业集团2", "codeType": "CRED_ORG_USCC", "codeValue": "91**************32", "code": {"CRED_ORG_USCC": "91**************32"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "ae72462ffe844a77a221e84e4244078d", "guid": "478d2567fb10496ab66472d9928ecfcf", "organGuid": "478d2567fb10496ab66472d9928ecfcf", "ouid": "d52933266afc4d49a2de1eff27e35d24", "adminInfoList": [{"mobile": "131****7570", "email": "lin***@tsign.cn", "name": "赵金宝", "ouid": "f4299c63907e404087915b08b563450f", "accountUid": "1e06f72dd19e4b67bf13872f81a846d1", "guid": "085556bd3a744208bed2d00f9faf25fe", "credentialNo": "34**************77", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "24f6bb51786e4ad8bb95cd87ac90b0a0", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "赵金宝", "orgLegalNo": "34**************77", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "915758979911132332"}, "serviceId": "3714649071030897971", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "915758979911132332"}, {"name": "esigntest令羽测试企业修改名称", "codeType": "CRED_ORG_USCC", "codeValue": "91**************49", "code": {"CRED_ORG_USCC": "91**************49"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "a294c63aaa0e4b2eb3dc9cf9e69272a1", "guid": "fad00218851f4d0fb626e1de00017dd7", "organGuid": "fad00218851f4d0fb626e1de00017dd7", "ouid": "d72fda6874d2425b9ad1d92057bbaf2b", "adminInfoList": [{"mobile": "131****7570", "email": "lin***@tsign.cn", "name": "赵金宝", "ouid": "f4299c63907e404087915b08b563450f", "accountUid": "1e06f72dd19e4b67bf13872f81a846d1", "guid": "085556bd3a744208bed2d00f9faf25fe", "credentialNo": "34**************77", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "093d627e853247b182479c351b31e710", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "赵金宝", "orgLegalNo": null, "orgLegalType": null, "thirdpartyUserId": "9eefd89f-71f2-45d4-9a0b-be2f2a991d26esigntest令羽测试企业", "thirdpartyUserType": "_DEFAULT_USER", "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "914871298479182749"}, "serviceId": "3714645077264960850", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "914871298479182749"}, {"name": "esigntest林木测试企业", "codeType": "CRED_ORG_USCC", "codeValue": "91**************3K", "code": {"CRED_ORG_USCC": "91**************3K"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "9c524329270a41aca729cba2d5c3caaf", "guid": "6186cf7123a247898154d40b1d1da8fa", "organGuid": "6186cf7123a247898154d40b1d1da8fa", "ouid": "0b1c0f8288cc4c7cb68a0a69e3c9d07f", "adminInfoList": [{"mobile": null, "email": "lin*@qq.com", "name": "黄志兵", "ouid": "ea51a559d5214d56b7e3f8b2b52cfe22", "accountUid": "66c54d6734cb440eb9a07f8680728402", "guid": "67e6b555907643a9b522df6da063cdae", "credentialNo": "36**************18", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "2cecfdaf94284861840952110886c18c", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "黄志兵", "orgLegalNo": "36**************18", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "91340600719915893K"}, "serviceId": "3710474250596388140", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "91340600719915893K"}, {"name": "esigntest芷萱测试其他的哦", "codeType": "CRED_ORG_USCC", "codeValue": "92**************33", "code": {"CRED_ORG_USCC": "92**************33"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "b47993fa10794515ba197000d91a79fc", "guid": "71939985c6a64e9888092a4eb837de0b", "organGuid": "71939985c6a64e9888092a4eb837de0b", "ouid": "3177318345bd4dc8b9793ad6deb1287e", "adminInfoList": [{"mobile": "198****3328", "email": null, "name": "测试其他的从邀请注册进来的", "ouid": "ebeeb606f8454cf6ac733b283eab4fa9", "accountUid": "33daf43c367847709895f7f39b059b03", "guid": "af95e42fb9b942369d05752c9aad528c", "credentialNo": null, "credentialType": null, "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "b3be2bf5e36b492386701a63e0b44479", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试其他的哦", "orgLegalNo": "22**22", "orgLegalType": null, "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "920000222222233333"}, "serviceId": "3710370060595367170", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "920000222222233333"}, {"name": "esigntest静嘉测试e签宝重构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************55", "code": {"CRED_ORG_USCC": "91**************55"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4544a99c5ec541f4974a8f16d6ece621", "guid": "6cdbab9d14434529b4f45879ef0ff060", "organGuid": "6cdbab9d14434529b4f45879ef0ff060", "ouid": "55d4f45d40524a2c8c89b4d9dbcbba51", "adminInfoList": [{"mobile": "186****6617", "email": "873***@qq.com", "name": "邓冬容", "ouid": "b21ee29d236a4681940968d81834606c", "accountUid": "2a9c29c20ab143c8ae6d4fd68955fc37", "guid": "9a3d1f6c55564583b4eef095f29c2595", "credentialNo": "43**************20", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "**********", "thirdpartyUserId": "1604216398358454690"}], "roleAmount": 1, "organId": "e55c70fe813a4202af71101d15469a56", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "邓冬容", "orgLegalNo": "43**************20", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "918989343546555555"}, "serviceId": "3710259863528934661", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "918989343546555555"}, {"name": "esigntest模拟测试ca123", "codeType": "CRED_ORG_USCC", "codeValue": "91**************EH", "code": {"CRED_ORG_USCC": "91**************EH"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "0d5c3b06e5ef4b8e85b2b3da6e17dabf", "guid": "dd545c22654341f89b17207b852088dd", "organGuid": "dd545c22654341f89b17207b852088dd", "ouid": "56f22d146ca644eeb1a6c404ef133d78", "adminInfoList": [{"mobile": "184****0367", "email": "854***@qq.com", "name": "甘舰戈", "ouid": "3986d7544d9848f186ee9c5dcd342de1", "accountUid": "826a0193182d4c798a847fcc378cec2f", "guid": "31f0751167aa47d2a8c2d361efcd6308", "credentialNo": "41**************70", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "8cb3f1a800c04633b17b5354aa269897", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "甘舰戈", "orgLegalNo": null, "orgLegalType": null, "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000090439935EH"}, "serviceId": "3709171735074442525", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000090439935EH"}, {"name": "esigntest测试藤逸1991", "codeType": "CRED_ORG_USCC", "codeValue": "92**************22", "code": {"CRED_ORG_USCC": "92**************22"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "7813aae28d41464eb33fac1296a59cb0", "guid": "cd26c95157824b5390f5ea09c1283bde", "organGuid": "cd26c95157824b5390f5ea09c1283bde", "ouid": "a5e0c9d4bd3a4f9ca85a2a3a049318aa", "adminInfoList": [{"mobile": "182****6477", "email": null, "name": "杨文超", "ouid": "b113308ea9724fe4aad37b8dbb107319", "accountUid": "0311998630cf4c35831dadfe6bf1e1db", "guid": "f69b7fe9deb446ad952ec58a924d6225", "credentialNo": "41**************39", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "4596fde47c4c403cb45978f7ae9f62fb", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "杨文超", "orgLegalNo": "41**************39", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "926589762100001522"}, "serviceId": "3709123049103559940", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "926589762100001522"}, {"name": "esigntest新建外部机构实名Itnl", "codeType": "CRED_ORG_USCC", "codeValue": "91**************33", "code": {"CRED_ORG_USCC": "91**************33"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "3323064fbd1e40d5ad4037a3300a4814", "guid": "624e9a97de584416a3bb9a6b367ad24d", "organGuid": "624e9a97de584416a3bb9a6b367ad24d", "ouid": "39764d6b6b8746d695d666b005175a7f", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "60838d234709423e9f2ea586523e323d", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115941435233"}, "serviceId": "3709093129153416453", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115941435233"}, {"name": "esigntest新建用于实名MlLu", "codeType": "CRED_ORG_USCC", "codeValue": "91**************11", "code": {"CRED_ORG_USCC": "91**************11"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "6ae0af05e0ea4afd993c908df92a68fc", "guid": "a7cf25ae19cc45a0903beb3c95f01232", "organGuid": "a7cf25ae19cc45a0903beb3c95f01232", "ouid": "f5096e4c69354bd7b615955fd8743425", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "230c293755484a5c92245f83b4d12c83", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118913126811"}, "serviceId": "3709091385832901911", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118913126811"}, {"name": "esigntest新建用于实名wWmS", "codeType": "CRED_ORG_USCC", "codeValue": "91**************45", "code": {"CRED_ORG_USCC": "91**************45"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "2b9c818b7a4c4e2b853190550a92da79", "guid": "94700b58aba443c986394b485c729315", "organGuid": "94700b58aba443c986394b485c729315", "ouid": "4dddd4e0d1144b69bb36d5f6e76ec1eb", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "e421d876490b43dda196f7d046d10fef", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113773042845"}, "serviceId": "3709089468415216928", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113773042845"}, {"name": "esigntest新建用于实名qPcH", "codeType": "CRED_ORG_USCC", "codeValue": "91**************09", "code": {"CRED_ORG_USCC": "91**************09"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "bffe5e02ed404c4990bcfedde6094093", "guid": "0233ba6c95114931b336f8acfb234513", "organGuid": "0233ba6c95114931b336f8acfb234513", "ouid": "f6d17fb69d2b4b41b04c50b9baf1983c", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "54b8607532cb46efaa448d433882478b", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115654386809"}, "serviceId": "3709087905449774422", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115654386809"}, {"name": "esigntest测试其他证件号法人一", "codeType": "CRED_ORG_USCC", "codeValue": "92**************56", "code": {"CRED_ORG_USCC": "92**************56"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "384aad3eb0464e9688e8e0100cfb8806", "guid": "ddfce0425939484ea178788dcd495719", "organGuid": "ddfce0425939484ea178788dcd495719", "ouid": "625e06accb9847748712ee7cab632921", "adminInfoList": [{"mobile": "158****8803", "email": null, "name": "罗杰", "ouid": "23afff3aee5e4f88a3339be995925d7a", "accountUid": "b65d4aaf058c4390b84fb1ac3f6152dd", "guid": "82f1e146a4de4e299faf7c4fe2285636", "credentialNo": "33**************14", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "DING_TALK", "thirdpartyUserId": "KA8oQXsiPoZ88u6T4NiiOwwQiEiE"}], "roleAmount": 1, "organId": "5b523b21344f4571a29ce1349096adc7", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试其他六", "orgLegalNo": null, "orgLegalType": null, "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "921234567890123456"}, "serviceId": "3708959617058344198", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "921234567890123456"}, {"name": "esigntest新建外部机构实名rSCu", "codeType": "CRED_ORG_USCC", "codeValue": "91**************79", "code": {"CRED_ORG_USCC": "91**************79"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "d69e77f197834ad48fe2dc06c7808919", "guid": "aa1d5bf3510b4dbab9d19d9a0f4d8e05", "organGuid": "aa1d5bf3510b4dbab9d19d9a0f4d8e05", "ouid": "72f832efb5984d5ebf668ced1a2f8510", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "b649d09709fe4dd19d074c25afb8797f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115660170779"}, "serviceId": "3708887342321765724", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115660170779"}, {"name": "esigntest新建用于实名lZCb", "codeType": "CRED_ORG_USCC", "codeValue": "91**************80", "code": {"CRED_ORG_USCC": "91**************80"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "0c13d7d0e72b475cb0f150314174acc3", "guid": "52e1be8394e3457c905fc1e14a274790", "organGuid": "52e1be8394e3457c905fc1e14a274790", "ouid": "5dfe72ecca8c4e61baa1d483b06ff7da", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "e53192f2409842c5bd61f5e001c7ceca", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113608035580"}, "serviceId": "3708884806445567259", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113608035580"}, {"name": "esigntest新建用于实名UHSq", "codeType": "CRED_ORG_USCC", "codeValue": "91**************29", "code": {"CRED_ORG_USCC": "91**************29"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "295136bb3d0d4366805bed8e2588caf3", "guid": "068897b310324547932f0312067cb50e", "organGuid": "068897b310324547932f0312067cb50e", "ouid": "55d51518eb234a8d848c2972df61228f", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "85bd04c64ed34579b3d76f5414a5f602", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114764164329"}, "serviceId": "3708882815141023053", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114764164329"}, {"name": "esigntest新建用于实名ojRM", "codeType": "CRED_ORG_USCC", "codeValue": "91**************26", "code": {"CRED_ORG_USCC": "91**************26"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "d637d7ebfacc47069b15247f51eff8a1", "guid": "70e51bc3cf0a4b30a6351620bb8310ec", "organGuid": "70e51bc3cf0a4b30a6351620bb8310ec", "ouid": "aa333d2b25d1436daadf559bc4df6e8c", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "be0508b0f7064152a059bbff57b64a28", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118853369626"}, "serviceId": "3708881257678507352", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118853369626"}, {"name": "esigntest沈岸测试组织", "codeType": "CRED_ORG_USCC", "codeValue": "99**************3W", "code": {"CRED_ORG_USCC": "99**************3W"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "194d43552f714920bec0ef6b48176e84", "guid": "84f3e7560273486f8ac65a2a30d0a0f2", "organGuid": "84f3e7560273486f8ac65a2a30d0a0f2", "ouid": "7bc6f352897b48dabe3dd7f9abdf92d1", "adminInfoList": [{"mobile": "157****5574", "email": null, "name": "张东辉", "ouid": "96b266e206a94f20be55e3b6f387ae89", "accountUid": "c7d5de822a1f4417a155180895a91cd0", "guid": "958dc9b1387b46c280bccbcb1a6fa52c", "credentialNo": "41**************33", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "0ae97e31b9bd4471b9d650ca66ba6a4f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "张东辉", "orgLegalNo": null, "orgLegalType": null, "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "99100000TMJEJ1Y03W"}, "serviceId": "3708810769363111168", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "99100000TMJEJ1Y03W"}, {"name": "esigntest孙巫阴魏殴台机构", "codeType": "CRED_ORG_USCC", "codeValue": "91**************46", "code": {"CRED_ORG_USCC": "91**************46"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "a23daf52445743bba611994c2aa650d1", "guid": "dc286f804b614f50824ef616b382dd31", "organGuid": "dc286f804b614f50824ef616b382dd31", "ouid": "af778c7458de4a4a8e04c77b3d483d7a", "adminInfoList": [{"mobile": "198****2937", "email": null, "name": "测试国邱有", "ouid": "709609f90b754fa79208990046b5a851", "accountUid": "aa00f1131a7d4a118fe210e20ce2d8d5", "guid": "39af417c5b834674a71af3981426a930", "credentialNo": "50**************05", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "eba1fb057a7942a0bd4d44de16ca1f5f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试国邱有", "orgLegalNo": "50**************05", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000000671096046"}, "serviceId": "3708635902386900301", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000000671096046"}, {"name": "esigntest太白测试企业三三三", "codeType": "CRED_ORG_USCC", "codeValue": "91**************M0", "code": {"CRED_ORG_USCC": "91**************M0"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "3ce58fa22c4941f18ced373da4cc0b7e", "guid": "dddc4623ffcb46f0b24995cfaf84ec92", "organGuid": "dddc4623ffcb46f0b24995cfaf84ec92", "ouid": "7f7b1714b1c3436098b0b59adb66b7f1", "adminInfoList": [{"mobile": "130****2079", "email": null, "name": "测试太白", "ouid": "a769b753b2e04c629dff47c8a7687450", "accountUid": "73ac4af7c4c64b8bad52d014adc49d0d", "guid": "278af0726499441db4cd6eada13a18c9", "credentialNo": "34**************33", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "DING_TALK", "thirdpartyUserId": "dvSV21tPTjjmS4WWoeVmcQiEiE"}], "roleAmount": 1, "organId": "06cd0d2cd36746f3a7b510a3541c9f12", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "朱梦杰", "orgLegalNo": "34**************33", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000031075270M0"}, "serviceId": "3706007728515714377", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000031075270M0"}, {"name": "esigntest太白测试天谷CA", "codeType": "CRED_ORG_USCC", "codeValue": "91**************T4", "code": {"CRED_ORG_USCC": "91**************T4"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "bf242d5127714c0fbc7651ff774dce49", "guid": "0d2a857783084bb5acd0b02c89003212", "organGuid": "0d2a857783084bb5acd0b02c89003212", "ouid": "7c4cb8b139a54b7aa26f05b09c8607f6", "adminInfoList": [{"mobile": "130****2079", "email": null, "name": "测试太白", "ouid": "a769b753b2e04c629dff47c8a7687450", "accountUid": "73ac4af7c4c64b8bad52d014adc49d0d", "guid": "278af0726499441db4cd6eada13a18c9", "credentialNo": "34**************33", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "DING_TALK", "thirdpartyUserId": "dvSV21tPTjjmS4WWoeVmcQiEiE"}], "roleAmount": 1, "organId": "53e80c7458d54a648ed0ae7df4619eb8", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "朱梦杰", "orgLegalNo": "34**************33", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000031075270T4"}, "serviceId": "3706002369839369480", "createSource": "ZQB", "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:PORTAL_ZQB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000031075270T4"}, {"name": "esigntest新建外部机构实名KFoX", "codeType": "CRED_ORG_USCC", "codeValue": "91**************65", "code": {"CRED_ORG_USCC": "91**************65"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "3fb865b456d948e5bf45e946d29d8d2f", "guid": "11b8e7e121914dd9952694770917f8e3", "organGuid": "11b8e7e121914dd9952694770917f8e3", "ouid": "af6628fc8fc146509984ae0736537779", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "df8f32cb6ae447038ff342e7f781ad24", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115672703365"}, "serviceId": "3704696980665535768", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115672703365"}, {"name": "esigntest新建用于实名cIVU", "codeType": "CRED_ORG_USCC", "codeValue": "91**************97", "code": {"CRED_ORG_USCC": "91**************97"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "f447fb6bb66243f585fe734613f6e9b5", "guid": "838a08ab93e04d97b2cb1b4c2ff690d0", "organGuid": "838a08ab93e04d97b2cb1b4c2ff690d0", "ouid": "3cfded2d0291467e9c3411c2b17c4df6", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "6670a0584f2b4869baf018eeaf0afecc", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115649513197"}, "serviceId": "3704694656165481820", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115649513197"}, {"name": "esigntest新建用于实名ZmpK", "codeType": "CRED_ORG_USCC", "codeValue": "91**************85", "code": {"CRED_ORG_USCC": "91**************85"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "268a6dd05ac44a0bb87914b4674683f6", "guid": "54e639bb8dc94d4eb6c2dbf7bba30929", "organGuid": "54e639bb8dc94d4eb6c2dbf7bba30929", "ouid": "542c1f609060499fae7dafd7bd059357", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "f9aee52d7e7a4404aaeaa329a808fe50", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115248211485"}, "serviceId": "3704692546648673578", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115248211485"}, {"name": "esigntest新建用于实名UzYf", "codeType": "CRED_ORG_USCC", "codeValue": "91**************67", "code": {"CRED_ORG_USCC": "91**************67"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "2514e3aa2eda494b8e71481c5bd0c537", "guid": "fe61844ca4fc47dfa7491e9ea8790b9b", "organGuid": "fe61844ca4fc47dfa7491e9ea8790b9b", "ouid": "6e0cc4842e8f431fb95009793fc6256a", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "8aa99ad066874bcbb6005eb8ad2c8c9d", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115502151467"}, "serviceId": "3704691038980934947", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115502151467"}, {"name": "esigntest芷萱1230", "codeType": "CRED_ORG_USCC", "codeValue": "91**************KJ", "code": {"CRED_ORG_USCC": "91**************KJ"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "54e0f9e24fd04de4b1677800fd1924e5", "guid": "f7c726a161794dadb7942d88986c7dae", "organGuid": "f7c726a161794dadb7942d88986c7dae", "ouid": "cbf2565754b24e2893ac7886a5d7afa0", "adminInfoList": [{"mobile": "198****3433", "email": null, "name": "测试邀请注册", "ouid": "dca3ab5f978c445197f5f5ffccb5b98a", "accountUid": "5869d9cccd3c4bd5af7a19b96f659b43", "guid": "d9da880149ed43bbae4dec8d327548d0", "credentialNo": "12**************3X", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "cb9d213933aa4b33b7808e5163a2f0fa", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试邀请注册", "orgLegalNo": "12**************3X", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000050191601KJ"}, "serviceId": "3704646392175136070", "createSource": "**********", "registerSource": "SOURCE_APP_ID:**********", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000050191601KJ"}, {"name": "esigntest霁林1202测试企业4-4", "codeType": "CRED_ORG_USCC", "codeValue": "91**************07", "code": {"CRED_ORG_USCC": "91**************07"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "9b826b49d01b47848f8659ffd49489fe", "guid": "c79063b0af65425cbb5e28a552313acb", "organGuid": "c79063b0af65425cbb5e28a552313acb", "ouid": "66cd37802ffb4e218ee8204f320339b7", "adminInfoList": [{"mobile": "153****7650", "email": null, "name": "张贺", "ouid": "2f620fa9fbe54b58a51c426979152b02", "accountUid": "db8014aed1ff4db5a2b652941d154b49", "guid": "d363a69364fc4b54b52a837bdc70ffcb", "credentialNo": "23**************29", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "00ade6ddf4ad4a7e9e2cde3bd740e54a", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "张贺", "orgLegalNo": "23**************29", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "913020202503114007"}, "serviceId": "3704512903752715570", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "913020202503114007"}, {"name": "esigntest新建外部机构实名obJC", "codeType": "CRED_ORG_USCC", "codeValue": "91**************96", "code": {"CRED_ORG_USCC": "91**************96"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "dc8cbe4b718f4342b3cb8cecffb56c2a", "guid": "f3754b7acb044762890fcc09f31ad586", "organGuid": "f3754b7acb044762890fcc09f31ad586", "ouid": "fb76f338926c42d68b41461640fd884f", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "71efe80e13ff4b3e91231ceff2fd9825", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114723087896"}, "serviceId": "3700343313715957062", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114723087896"}, {"name": "esigntest新建用于实名rlXW", "codeType": "CRED_ORG_USCC", "codeValue": "91**************04", "code": {"CRED_ORG_USCC": "91**************04"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "64b7e6c8db424b899c036ce1be957cb6", "guid": "93d3cc4c001c49cd97467d56bbbbe41d", "organGuid": "93d3cc4c001c49cd97467d56bbbbe41d", "ouid": "d325620efc75488098d966d9c765d288", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "a13789c52c514e07b4ef6722f104b36f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114516742604"}, "serviceId": "3700340682947824933", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114516742604"}, {"name": "esigntest新建用于实名pGoS", "codeType": "CRED_ORG_USCC", "codeValue": "91**************84", "code": {"CRED_ORG_USCC": "91**************84"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "6ddfbf70428a46d9afc22540f830aaaa", "guid": "b5a222165768422aac273b441c908aaf", "organGuid": "b5a222165768422aac273b441c908aaf", "ouid": "abbcdc8e4fd7421aabdc8779cded51f9", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "0e0f0c187f3441d8b6c5a33effba5b92", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118648342584"}, "serviceId": "3700338486290156801", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118648342584"}, {"name": "esigntest新建用于实名CmYV", "codeType": "CRED_ORG_USCC", "codeValue": "91**************73", "code": {"CRED_ORG_USCC": "91**************73"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "7e6d9391987f4fcebb53543232c8dc57", "guid": "136fd1a67f5f4c46a454244cd5db5642", "organGuid": "136fd1a67f5f4c46a454244cd5db5642", "ouid": "1c1ce265b93e411caef6b7ccaacaf49f", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "3fe9691f90f345e4a741c40a98989261", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118144327473"}, "serviceId": "3700336886280621354", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118144327473"}, {"name": "esigntest培风测试企业1227", "codeType": "CRED_ORG_USCC", "codeValue": "91**************93", "code": {"CRED_ORG_USCC": "91**************93"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "adf3191a559c4c53a5bc1f60bedab789", "guid": "85fa436908a641aca95842ef4a20b66e", "organGuid": "85fa436908a641aca95842ef4a20b66e", "ouid": "d83f1895b85c4e80aeab43c0aebd0ae5", "adminInfoList": [{"mobile": "183****4304", "email": "pei***@tsign.cn", "name": "肖军军", "ouid": "771c4f02f6ac47ceac24872f2fe31739", "accountUid": "53eca0ab31654b0e9d316b6c920025f0", "guid": "c0294678a2d64ca1bc830e33e76dc3e8", "credentialNo": "61**************18", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "WE_CHAT_ZQB", "thirdpartyUserId": "ow19_5HE57dJNaoLdMc4Aq6rHQZs"}], "roleAmount": 1, "organId": "96153faae08a4975a8797913c9a71de4", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "肖军军", "orgLegalNo": "61**************18", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910000008480052093"}, "serviceId": "3700164585480260866", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910000008480052093"}, {"name": "esigntest新建用于实名mIzh", "codeType": "CRED_ORG_USCC", "codeValue": "91**************03", "code": {"CRED_ORG_USCC": "91**************03"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "1de2d94792c643c2a3ce0b57a39b8139", "guid": "2126efec1faf417bb5b2cbd47a33e0a8", "organGuid": "2126efec1faf417bb5b2cbd47a33e0a8", "ouid": "c5ad7504153b488fbd7d510c379c0b2b", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "2d9d96323a9c452b952a30196ee0cfc2", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118601688603"}, "serviceId": "3699924729256742205", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118601688603"}, {"name": "esigntest新建用于实名mkwz", "codeType": "CRED_ORG_USCC", "codeValue": "91**************89", "code": {"CRED_ORG_USCC": "91**************89"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "b42507dc823340cba492790fdda7bc96", "guid": "abde529ebf194923a1020c8162c35941", "organGuid": "abde529ebf194923a1020c8162c35941", "ouid": "9f0591cf20dd4e91afbd8849a7ab14ef", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "632b10922f364203a20ecafa48c6e900", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114537842089"}, "serviceId": "3699919166703668524", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114537842089"}, {"name": "esigntest新建用于实名dwAt", "codeType": "CRED_ORG_USCC", "codeValue": "91**************51", "code": {"CRED_ORG_USCC": "91**************51"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "f31061e202c84cf5b3b71fd4a842512d", "guid": "80e70716da774618b8d6229d4da7366c", "organGuid": "80e70716da774618b8d6229d4da7366c", "ouid": "9511adeb240c462db052b1ca5a5abfa7", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "584616dea68749c3bbe50e5254f40555", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115158578251"}, "serviceId": "3699912162266320174", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115158578251"}, {"name": "esigntest新建外部机构实名nhER", "codeType": "CRED_ORG_USCC", "codeValue": "91**************81", "code": {"CRED_ORG_USCC": "91**************81"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "ae0a311c061242d0a97c18a789b599be", "guid": "daf9d62c95784e93bb2a030b70500035", "organGuid": "daf9d62c95784e93bb2a030b70500035", "ouid": "140ed79882d4451d96f115ba5985b097", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "7e992cbd2c0a4e538d6b724bc8c631e4", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118166204181"}, "serviceId": "3699067514215992589", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118166204181"}, {"name": "esigntest新建外部机构实名Qlxq", "codeType": "CRED_ORG_USCC", "codeValue": "91**************42", "code": {"CRED_ORG_USCC": "91**************42"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "e8d0ef36a73e4220a7f50ab1709be143", "guid": "952465355ba64e2781b90a538d8fe3b5", "organGuid": "952465355ba64e2781b90a538d8fe3b5", "ouid": "8fff378936aa41738f24dab823ab92e6", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "a16ce5fd619f46cca5e9a186b22ea7ba", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113676571242"}, "serviceId": "3699066237620850007", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113676571242"}, {"name": "esigntest新建用于实名mHwi", "codeType": "CRED_ORG_USCC", "codeValue": "91**************01", "code": {"CRED_ORG_USCC": "91**************01"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "3d829ebdf08b474fbb2981f1923dd82f", "guid": "58129cde880c4a098f118489848a1a26", "organGuid": "58129cde880c4a098f118489848a1a26", "ouid": "f13bb1ffc57346bcb35654a6630667e0", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "65254fc93265472d8b9e695dad509486", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001113251629901"}, "serviceId": "3699064714903948597", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001113251629901"}, {"name": "esigntest新建用于实名sYkz", "codeType": "CRED_ORG_USCC", "codeValue": "91**************04", "code": {"CRED_ORG_USCC": "91**************04"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "6832788d647148dea2918e272a911d8e", "guid": "dd98d0bb6b6d47499ef3e0abe7fbc054", "organGuid": "dd98d0bb6b6d47499ef3e0abe7fbc054", "ouid": "0b7e0511d4684cd78a7135a4a3e2a6a0", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "b5dffea20d104242bf1b1011ecc997fd", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115752879704"}, "serviceId": "3699063221480066309", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115752879704"}, {"name": "esigntest高级版模拟", "codeType": "CRED_ORG_USCC", "codeValue": "91**************TE", "code": {"CRED_ORG_USCC": "91**************TE"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "5fe0d267e3e64b13bb7b0675f508ac91", "guid": "5c224b867fd34beb94def89cfb36abe8", "organGuid": "5c224b867fd34beb94def89cfb36abe8", "ouid": "10ce9feae88a4558a062392c8a2904c2", "adminInfoList": [{"mobile": "187****4095", "email": null, "name": "林建兵", "ouid": "71707de53f2447c09a55ebb7a9186202", "accountUid": "c05d831692704d3fb3459d42869ddbf4", "guid": "329d5b9fdf864cf09cf8f268305c445d", "credentialNo": "33**************73", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "DING_TALK", "thirdpartyUserId": "aLcXXEVB2rAiE"}], "roleAmount": 1, "organId": "7d6f6070c09f4cdc8bcfb6e73c0186b0", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "屈志艳", "orgLegalNo": "62**************26", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000059079676TE"}, "serviceId": "3698949277759311107", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000059079676TE"}, {"name": "esigntest桃浪企业B", "codeType": "CRED_ORG_USCC", "codeValue": "91**************MA", "code": {"CRED_ORG_USCC": "91**************MA"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "4311d9077fdc433eaa76917bef064d8d", "guid": "d916261c051543bf995fcefa8097755b", "organGuid": "d916261c051543bf995fcefa8097755b", "ouid": "e8146db4e74c43dd91968f4c90d217d6", "adminInfoList": [{"mobile": "185****5626", "email": "132***@qq.con", "name": "彭丹", "ouid": "ee46622cd1f14f8fb7b889361a952bf1", "accountUid": "56b436199c424d1da3395abfedbafcc8", "guid": "7eb869681337429cbfe4c6a17ef0f76b", "credentialNo": "43**************69", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "a00bfbcbe352469d8c71ae2c374e5f13", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "彭丹", "orgLegalNo": "43**************69", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000053387791MA"}, "serviceId": "3698933318583586105", "createSource": null, "registerSource": null, "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000053387791MA"}, {"name": "esigntest胖橘的第3000家企业", "codeType": "CRED_ORG_USCC", "codeValue": "91**************TK", "code": {"CRED_ORG_USCC": "91**************TK"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "66bdaf4972dd468db52f3aedcaeb7a94", "guid": "c230d6672f714c72a2fb6df199f7d61a", "organGuid": "c230d6672f714c72a2fb6df199f7d61a", "ouid": "92dcae87e2064bc082b466de9d56f4b7", "adminInfoList": [{"mobile": "195****2006", "email": null, "name": "倪宏辉", "ouid": "122a68f961e74314b5c52cc925ce14c1", "accountUid": "12a9ed86daae402380197c24e3bfccf0", "guid": "a3b7bbd698754c55a41a38cdb1b17dcd", "credentialNo": "41**************1X", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": "ALI_PAY", "thirdpartyUserId": "****************"}], "roleAmount": 1, "organId": "8cce9c666ec04543943949041b9dd570", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "倪宏辉", "orgLegalNo": "41**************1X", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "9100000092974259TK"}, "serviceId": "3698657775292255518", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:APP_HARMONYOS", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "9100000092974259TK"}, {"name": "esigntest新建外部机构实名LTIp", "codeType": "CRED_ORG_USCC", "codeValue": "91**************21", "code": {"CRED_ORG_USCC": "91**************21"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "7bdbf49d7d1047f6bbdf8ede97ddd4d4", "guid": "7eadddce3aa5411bb3d578dc63eeba3c", "organGuid": "7eadddce3aa5411bb3d578dc63eeba3c", "ouid": "3982acf05e5c4a9ba2ed15cd0ce824d4", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "ded00e075d13448b8a28e237915e366f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115310824421"}, "serviceId": "3698651689642364249", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115310824421"}, {"name": "esigntest新建外部机构实名YIYB", "codeType": "CRED_ORG_USCC", "codeValue": "91**************89", "code": {"CRED_ORG_USCC": "91**************89"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "a8981a3b783f4b089b4c424923957946", "guid": "12718bd44841475886a479471ebbb251", "organGuid": "12718bd44841475886a479471ebbb251", "ouid": "69d3e46f8de545879953c68d69bee36f", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 2, "organId": "11da88d24cd34c02a9d2878c07d15c74", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001114554208589"}, "serviceId": "3698650339445574973", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001114554208589"}, {"name": "esigntest新建用于实名zTBt", "codeType": "CRED_ORG_USCC", "codeValue": "91**************51", "code": {"CRED_ORG_USCC": "91**************51"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "8c37fb618a9e4e6eba326d39503697ab", "guid": "fe351632e5c849ca95ff912f4239176a", "organGuid": "fe351632e5c849ca95ff912f4239176a", "ouid": "ce1701d20c01430c98b724c03f1bb9d3", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "813a911f454a4113afeace8c45e2af4f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115123606051"}, "serviceId": "3698648859309247757", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115123606051"}, {"name": "esigntest新建用于实名xBOV", "codeType": "CRED_ORG_USCC", "codeValue": "91**************22", "code": {"CRED_ORG_USCC": "91**************22"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "e9e8346f9301483dbf7d729875d91a3b", "guid": "896a0f0846c148fe8e36dd8fd8be6d8d", "organGuid": "896a0f0846c148fe8e36dd8fd8be6d8d", "ouid": "5fb5bc1fdd854a9783850537a36aac23", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "753d41bcbe8a4f81b6164f66457c4d1f", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001115189950622"}, "serviceId": "3698647332985244976", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001115189950622"}, {"name": "esigntest新建外部机构实名DMSc", "codeType": "CRED_ORG_USCC", "codeValue": "91**************04", "code": {"CRED_ORG_USCC": "91**************04"}, "realNameStatus": "ACCEPT", "createTime": *************, "modifyTime": *************, "accountUid": "aae2129632044c4bb32683cd262525ce", "guid": "366d97d80dea40e3809dbfdfa9e74fe7", "organGuid": "366d97d80dea40e3809dbfdfa9e74fe7", "ouid": "797a8e7679694b5fa3dc0987aa262a82", "adminInfoList": [{"mobile": "191****0010", "email": null, "name": "测试签署十", "ouid": "1931aaa864c24859a03b8f3c03387a5a", "accountUid": "f01226af78e840699b3852ffb741aca4", "guid": "76df39d392cc4916a935f0a7afdc72f2", "credentialNo": "11**************57", "credentialType": "CRED_PSN_CH_IDCARD", "thirdpartyUserType": null, "thirdpartyUserId": null}], "roleAmount": 1, "organId": "13775918b2a84c96a3d8f3e8ae81d3be", "deleted": false, "productId": "**********", "productName": "PRODUCT_STD", "orgLegalName": "测试签署十", "orgLegalNo": "11**************57", "orgLegalType": "CRED_PSN_CH_IDCARD", "thirdpartyUserId": null, "thirdpartyUserType": null, "gidInfo": {"codeType": "CRED_ORG_USCC", "codeValue": "910001118077823404"}, "serviceId": "3697631372883004749", "createSource": null, "registerSource": "SOURCE_APP_ID:**********,SOURCE_END_POINT:WEB", "activate": 1, "realNameOrganTransferRecord": null, "realNameOrganTransferSource": null, "addOrganChecklist": 0, "normalCodeValue": "910001118077823404"}], "total": 5467}}