- config:
    name: 签署人姓名、昵称校验
    base_url: ${ENV(footstone_api_url)}
    variables:
      - name_org: ''
      - signerAuthorizerId: ''
      - signerAuthorizerType: 0
      - signerSignRoles: 0
- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 使用第三方userId 创建账号
    variables:
      - json:
          {
            "name": 梁贤红,
            "thirdPartyUserId": $tPUserId
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId


- test:
    name: 一步创建流程：姓名+昵称
    variables:
      - name: 指定
      - nickname: 测试
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 梁贤红]
      - eq: ["content.data.signers.0.signerNickname", 测试]



- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 梁贤红]
      - eq: ["content.data.signers.0.signerNickname", 测试]




- test:
    name: 一步创建流程:姓名无昵称
    variables:
      - name: 指定
      - nickname:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 梁贤红]



- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 梁贤红]

- test:
    name: 创建账号无信息
    variables:
      - email:
      - mobile:
      - email2:
      - mobile2:
      - thirdPartyUserId:
    api: api/user/accounts/create_accounts_v2.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId



- test:
    name: 一步创建流程：指定姓名+昵称
    variables:
      - name: 指定姓名
      - nickname: 测试
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 指定姓名]
      - eq: ["content.data.signers.0.signerNickname", 测试]



- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 指定姓名]
      - eq: ["content.data.signers.0.signerNickname", 测试]


- test:
    name: 一步创建流程：指定姓名无昵称
    variables:
      - name: 指定姓名
      - nickname:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 指定姓名]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 指定姓名]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 创建账号联系方式手机号
    variables:
      - mobile: $m1
      - mobile2:
      - email:
      - email2:
      - thirdPartyUserId:
    api: api/user/accounts/create_accounts_v2.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId



- test:
    name: 一步创建流程：联系方式手机号+昵称
    variables:
      - name:
      - nickname: 测试
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]

- test:
    name: 一步创建流程：联系方式手机号无昵称
    variables:
      - name:
      - nickname:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]


- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", $m1]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", $m1]
      - eq: ["content.data.signers.0.signerNickname", Null]

- test:
    name: 创建账号登录方式手机号
    variables:
      - email:
      - mobile:
      - email2:
      - mobile2: $m2
      - thirdPartyUserId:
    api: api/user/accounts/create_accounts_v2.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId



- test:
    name: 一步创建流程：登录方式手机号+昵称
    variables:
      - name:
      - nickname: 测试
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]

- test:
    name: 一步创建流程：登录方式手机号无昵称
    variables:
      - name:
      - nickname:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
      - signerAuthorizerId: $accountId
      - signerAuthorizerType: 0
      - autoInitiate: true
      - signerSignRoles: 0
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", $m2]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName",$m2]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 创建账号联系方式邮箱
    variables:
      - email: $e1
      - mobile:
      - email2:
      - mobile2:
      - thirdPartyUserId:
    api: api/user/accounts/create_accounts_v2.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId



- test:
    name: 一步创建流程：联系方式邮箱+昵称
    variables:
      - name:
      - nickname: 测试
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]

- test:
    name: 一步创建流程：联系方式邮箱无昵称
    variables:
      - name:
      - nickname:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", $e1]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", $e1]
      - eq: ["content.data.signers.0.signerNickname", Null]

- test:
    name: 创建账号登录方式邮箱
    variables:
      - email:
      - mobile:
      - email2: $e2
      - mobile2:
      - thirdPartyUserId:
    api: api/user/accounts/create_accounts_v2.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId



- test:
    name: 一步创建流程：登录方式邮箱+昵称
    variables:
      - name:
      - nickname: 测试
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]

- test:
    name: 一步创建流程：登录方式邮箱无昵称
    variables:
      - name:
      - nickname:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", $e2]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", $e2]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 创建账号第三方id+昵称
    api: api/user/accounts/create_accounts_v2.yml
    variables:
      - email:
      - mobile:
      - email2:
      - mobile2:
      - thirdPartyUserId: abcdefghijklmn320721199305232634
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
    output:
      - $accountId



- test:
    name: 一步创建流程：第三方id+昵称
    variables:
      - name:
      - nickname: 测试
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", 测试]
      - eq: ["content.data.signers.0.signerNickname", 测试]

- test:
    name: 一步创建流程：第三方id无昵称
    variables:
      - name:
      - nickname:
      - businessScene: "一步创建流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/createAllAtOnce.yml
    extract:
      - flowId: content.data.flowIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]



- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", abcdefghijklmn320721199305232634]
      - eq: ["content.data.signers.0.signerNickname", Null]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerName", abcdefghijklmn320721199305232634]
      - eq: ["content.data.signers.0.signerNickname", Null]

- test:
    name: 一步更新流程：第三方id加昵称
    variables:
      - name:
      - nickname: 昵称
      - businessScene: "一步更新流程"
      - initiatorAccountId: $accountId
      - signerAccountId: $accountId
    api: api/signflow/signflows/updateAllAtOnce.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", 成功]


- test:
    name: 查询流程V1
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV1.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", 昵称]
      - eq: ["content.data.signers.0.signerNickname", 昵称]


- test:
    name: 查询流程V2
    variables:
      - flowId: $flowId
      - queryAccountId: $accountId
      - operatorid: $accountId
    api: api/signflow/signflows/flowsDetailV2.yml

    validate:
      - eq: [status_code, 200]
      - contains: ["content.data.signers.0.signerName", 昵称]
      - eq: ["content.data.signers.0.signerNickname", 昵称]
