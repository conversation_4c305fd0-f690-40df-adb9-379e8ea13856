#!/bin/sh

#用途，统计接口耗时

hrun tests/testcases/ --log-file output.log

grep -n '/*/' -A 1 output.log > url_time.log

grep -n '/*/*/' url_time.log | grep -v "environment variables" | grep -v "Html report" | awk '{print $4","$3}' | grep -v "application"  | grep -v "成功" | grep -v "Found"  > url.out

grep response_time url_time.log |awk '{print $6}'> time.out

paste -d , time.out url.out  > all.out

sort -t',' -k1 -n -r all.out | grep -v "aliyuncs" > perf_result.txt

sed -i '' 's#http://************:8099##g' perf_result.txt  #把host去掉

rm -rf output.log url_time.log url.out time.out all.out all.out2