- config:
    name: 流程二维码配置
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id1: ${ENV(X-Tsign-Open-App-Id)}
      - app_id: ${ENV(X-Tsign-Open-App-Id)}
      - extend: {}
      - tPUserId: autotest123${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
    "request":
      "base_url":
      "headers":
        "Content-Type": "application/json"

- test:
    name: 使用第三方userId 创建账号
    variables:
      - json:
          {
            "name": 梁贤红,
            "thirdPartyUserId": $tPUserId,
            "idNumber":"331082199211223080",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********"
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - oId1: content.data.accountId
    output:
      - $oId1


- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $oId1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]


- test:
    name: 创建流程和文档：esignCertSign、signQrSwitch、esignQrPos字段不传
    variables:
      - autoArchive: false
      - configInfo:
      - contractValidity:
      - extend:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId: $oId1
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - docs_1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_11: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：signQrSwitch字段不传
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_11
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]

- test:
    name: 创建流程和文档：esignCertSign、signQrSwitch为true、esignQrPos字段2
    variables:
      - autoArchive: false
      - configInfo: {'esignCertSign': true, 'signQrSwitch': true, 'esignQrPos': 2}
      - contractValidity:
      - extend:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId: $oId1
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - docs_1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_11: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_11
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","2"]

- test:
    name: 创建流程和文档：esignCertSign、signQrSwitch为true、esignQrPos字段9
    variables:
      - autoArchive: false
      - configInfo: {'esignCertSign': true, 'signQrSwitch': true, 'esignQrPos': 9}
      - contractValidity:
      - extend:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId: $oId1
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - docs_1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 天谷证书或二维码显示位置不支持']


- test:
    name: 一步发起-创建流程：esignCertSign、signQrSwitch、esignQrPos字段不传
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 查询流程：signQrSwitch字段不传
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_1
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]

- test:
    name: 创建流程：esignCertSign、signQrSwitch、esignQrPos字段不传
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',archiveLock: ''}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：signQrSwitch字段不传
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId1
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]

- test:
    name: 一步发起-创建流程：创建流程：esignCertSign、signQrSwitch、esignQrPos字段为空
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", esignCertSign: '',signQrSwitch: '',esignQrPos: '',redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 查询流程：appid的字段值为true、0
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_2
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]


- test:
    name: 创建流程：esignCertSign、signQrSwitch、esignQrPos字段为空
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',esignCertSign: '',signQrSwitch: '',esignQrPos: ''}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：appid的字段值为true、0
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId2
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]

- test:
    name: 一步发起-创建流程：创建流程：signQrSwitch字段值为false,esignQrPos不传
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", esignCertSign: false,signQrSwitch: false,esignQrPos: "",redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 查询流程：signQrSwitch字段值为false,esignQrPos为appid配置0
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_3
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",false]
      - eq: ["content.data.configInfo.signQrSwitch",false]
      - contains: ["content.data.configInfo.esignQrPos","0"]




- test:
    name: 创建流程：signQrSwitch字段值为false,esignQrPos不传
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',esignCertSign: false,signQrSwitch: false,esignQrPos: ""}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：signQrSwitch字段值为false,esignQrPos为appid配置0
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId3
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",false]
      - eq: ["content.data.configInfo.signQrSwitch",false]
      - contains: ["content.data.configInfo.esignQrPos","0"]

- test:
    name: 一步发起-创建流程：signQrSwitch字段值为true,esignQrPos不传
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signQrSwitch: true,redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 查询流程详情：signQrSwitch为true，esignQrPos为配置0
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_4
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]

- test:
    name: 创建流程：signQrSwitch字段值为true,esignQrPos不传
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',signQrSwitch: true}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程详情：signQrSwitch为true，esignQrPos为配置0
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId4
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]


- test:
    name: 一步发起-创建流程：只传esignQrPos
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", esignQrPos: 2, redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_5: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: 查询流程详情:signQrSwitch取appid配置
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_5
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","2"]

- test:
    name: 创建流程：只传esignQrPos
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',esignQrPos: 2}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId5: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程详情:signQrSwitch取appid配置
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId5
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","2"]

- test:
    name: 一步发起-创建流程：signQrSwitch字段值为true,esignQrPos传5
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signQrSwitch: true,esignQrPos: 'hello', redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - contains: ["content.message",'参数错误: 天谷证书或二维码显示位置不支持']

- test:
    name: 创建流程：signQrSwitch字段值为true,esignQrPos传5
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',signQrSwitch: true,esignQrPos: 5}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "参数错误: 天谷证书或二维码显示位置不支持"]


- test:
    name: "一步到位创建流程-对内：signQrSwitch字段值为true,esignQrPos传5"
    variables:
      - app_id: $app_id
      - group: ''
      - json: {"accountId":$oId1,"createWay":"normal","autoInitiate":true,"autoArchive":true,"businessScene":"二维码","configInfo":{"signQrSwitch": true,"esignQrPos": 5,"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"http://172.20.62.157/simpleTools/notice/","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[{"attachmentName":"附件","fileId":$fileId}],"docs":[{"fileId":$fileId}],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":0,"signerSignRoles":0,"signerAccount":"","signerAccountId":$oId1,"signerAccountName":"","signerAuthorizerId":$oId1,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{ "handDrawnWay":1,"fileId":$fileId,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":1,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 天谷证书或二维码显示位置不支持"]

- test:
    name: 一步发起-创建流程：signQrSwitch字段值为true,esignQrPos传3
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", esignCertSign: false,signQrSwitch: true,esignQrPos: 3, redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId_6: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: 查询流程详情
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId_6
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","3"]

- test:
    name: 创建流程：signQrSwitch字段值为true,esignQrPos传3
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',esignCertSign: false,signQrSwitch: true,esignQrPos: 3}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId6: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: 查询流程详情
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId6
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","3"]



- test:
    name: 创建流程：esignCertSign为true、signQrSwitch、esignQrPos为fasle
    variables:
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3',esignCertSign: 'true',signQrSwitch: '',esignQrPos: ''}
      - autoArchive: true
      - businessScene: 签署流程
      - signValidity:
      - contractValidity:
      - initiatorAccountId: $oId1
      - payerAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId7: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程：esignCertSign为true、signQrSwitch、esignQrPos为fasle
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId7
      - queryAccountId: $oId1
      - operatorid: $oId1
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.configInfo.esignCertSign",true]
      - eq: ["content.data.configInfo.signQrSwitch",true]
      - contains: ["content.data.configInfo.esignQrPos","0"]

- test:
    name: 一步发起-创建流程：signQrSwitch字段值为true,esignQrPos传5
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signQrSwitch: "hello",esignQrPos: '', redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: { signerAccountId: $oId1,authorizedAccountId: $oId1}, signfields: [ {  autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 400]
