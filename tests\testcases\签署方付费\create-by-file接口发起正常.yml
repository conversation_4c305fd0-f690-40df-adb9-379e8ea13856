- config:
    name: 通过文件创建签署流程
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(saas_noapproval)}
      - file_id: ${ENV(file_id_in_saas_noapproval)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_zn_tsign: ${ENV(account_jm_in_tsign)}
      - account_id_4_tsign: ab9e503fb5b24b988da20e2a718a552a
      - account_id_5_tsign: ${ENV(account_id_zn_tsign)}
      - sealId_sx: ${ENV(sealId_sx)}
      - sealId_zn: ${ENV(sealId_jm)}
      - sealId_qh: ${ENV(sealId_qh)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}

#正常发起 签署 签署方付费

- test:
    name: "创建清欢账号"
    variables:
      - thirdPartyUserId: nyl
      - name: 聂艳龙
      - idNumber: ******************
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_nly: content.data.accountId


- test:
    name: "创建泗县"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: sixian20191111241132
      - creator: $accountId_nly
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

#- test:
#    name: "创建浙农"
#    variables:
#
#        - thirdPartyUserId: zhenong201911fg1132
#        - creator: $accountId_nly
#        - idNumber: 913300000605902843
#        - idType: CRED_ORG_USCC
#        - name: esigntest浙江浙农实业投资有限公司
#        - orgLegalIdNumber: 330183199104242113
#        - orgLegalName: 俞杭峰
#    api: api/iterate_cases/o_createByThirdPartyUserId.yml
#    extract:
#        - zhenong_organize_id: content.data.orgId

- test:
    name: "创建浙农（实际esigntest江门市诚峰镖局物流有限公司（印章平台测试企业））"
    variables:
      - thirdPartyUserId: dongyingfhj2221141231
      - creator: $accountId_nly
      - idNumber: 91440705MA52G73E89
      - idType: CRED_ORG_USCC
      - name: esigntest江门市诚峰镖局物流有限公司（印章平台测试企业）
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - zhenong_organize_id: content.data.orgId



#- test:
#    name: "流程发起成功-非自动归档 签署方付费"
#    api: api/sign-flow/create-by-file.yml
#    variables:
#      json: {
#          "signFlowInitiator": {
#            "orgInitiator": {
#              "orgId": $account_id_sx_tsign,
#              "transactor": {
#                "psnId": "$account_id_4_tsign"
#              }
#            }
#          },
#          "docs": [
#          {
#            "fileEditPwd": "",
#            "fileId": "$file_id",
#            "fileName": "测试文档1.pdf",
#            "neededPwd": false
#          }
#          ],
#
#          "signFlowConfig": {
#            "signFlowTitle": "流程发起成功-非自动归档 签署方付费",
#            "authConfig": {
#              "audioVideoTemplateId": "",
#              "orgAvailableAuthModes": ["ORG_BANK_TRANSFER","ORG_ALIPAY_CREDIT","ORG_LEGALREP_AUTHORIZATION","ORG_LEGALREP"],
#              "orgEditableFields": [],
#              "psnAvailableAuthModes": ["PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE"],
#              "psnEditableFields": [],
#              "willingnessAuthModes": ["CODE_SMS","CODE_EMAIL","PSN_FACE_ALIPAY","PSN_FACE_TECENT","PSN_FACE_ESIGN","PSN_FACE_WECHAT"]
#            },
#            "autoFinish": false,
#            "autoStart": false,
#            "chargeConfig": {
#              "chargeMode": 2,
#              "orderType":"DISTRIBUTION"
#
#            },
#            "noticeConfig": {
#              "noticeTypes": "1,2"
#            },
#            "notifyUrl": "",
#            "redirectConfig": {
#              "redirectDelayTime": 2,
#              "redirectUrl": "https://www.baidu.com"
#            },
#            "signConfig": {
#              "availableSignClientTypes": "",
#              "showBatchDropSealButton": true
#            }
#          },
#          "signers": [
#          {
#            "noticeConfig": {
#              "noticeTypes": "1"
#            },
#
#            "psnSignerInfo": {
#              "psnAccount": "",
#              "psnId": "$account_id_4_tsign"
#
#            },
#
#            "signConfig": {
#              "forcedReadingTime": 0,
#              "signOrder": 1
#            },
#            "signFields": [
#            {
#              "customBizNum": "xxx",
#              "fileId": "$file_id",
#              "normalSignFieldConfig": {
#                "assignedSealId": "",
#                "autoSign": false,
#                "availableSealIds": [],
#                "freeMode": false,
#                "movableSignField": true,
#                "orgSealBizTypes": "",
#                "psnSealStyles": "",
#                "signFieldPosition": {
#                  "acrossPageMode": "",
#                  "positionPage": "1",
#                  "positionX": 180,
#                  "positionY": 180
#                },
#                "signFieldSize": 0,
#                "signFieldStyle": 1
#              },
#              "signFieldType": 0
#            }
#            ],
#            "signerType": 0
#          },
#
#          {
#            "noticeConfig": {
#              "noticeTypes": "1"
#            },
#
#            "psnSignerInfo": {
#              "psnAccount": "",
#              "psnId": "$account_id_4_tsign"
#
#            },
#
#            "signConfig": {
#              "forcedReadingTime": 0,
#              "signOrder": 1
#            },
#            "signFields": [
#            {
#              "customBizNum": "xxx",
#              "fileId": "$file_id",
#              "normalSignFieldConfig": {
#                "assignedSealId": "",
#                "autoSign": false,
#                "availableSealIds": [],
#                "freeMode": false,
#                "movableSignField": true,
#                "orgSealBizTypes": "",
#                "psnSealStyles": "",
#                "signFieldPosition": {
#                  "acrossPageMode": "",
#                  "positionPage": "1",
#                  "positionX": 280,
#                  "positionY": 280
#                },
#                "signFieldSize": 0,
#                "signFieldStyle": 1
#              },
#              "signFieldType": 0
#            }
#            ],
#            "signerType": 0
#          }
#          ]
#        }
#
#    extract:
#      - flowId1: content.data.signFlowId
#    validate:
#      - eq: [status_code, 200]
#      - eq: [content.code, 0]
#      - eq: [content.message, '成功']
#
#
#- test:
#    name: "V1查询流程 - 接口返回计费模式（Integer chargeMode）"
#    variables:
#      - flowId: $flowId1
#      - queryAccountId: $account_id_4_tsign
#      - operator_id: $account_id_4_tsign
#    api: api/mobile-shield/signflows_search_V1.yml
#    extract:
#      - signfieldId1: content.data.signDocs.0.signfields.0.signfieldId
#      - signfieldId2: content.data.signDocs.0.signfields.1.signfieldId
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", "成功"]
#      - eq: ["content.data.configInfo.chargeMode", 2]
#
#
#
#- test:
#    name: "V2查询流程 - 接口返回计费模式（Integer chargeMode）"
#    variables:
#      - flowId: $flowId1
#      - queryAccountId: $account_id_4_tsign
#      - operator_id: $account_id_4_tsign
#    api: api/mobile-shield/signflows_search_V2.yml
#
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", "成功"]
#      - eq: ["content.data.configInfo.chargeMode", 2]
#
#
#- test:
#    name: "添加签署区：个人-陈凯丽-顺序2"
#    variables:
#
#      - flowId: $flowId1
#      - signfields: [{"fileId":$file_id,"signerAccountId":$accountId_nly,"actorIndentityType":"0","authorizedAccountId":$accountId_nly,"assignedPosbean":true,"order":2,"posBean": {"addSignTime": true,"posPage": "1","posX": 100,"posY": 400},"sealType": null,"sealId": "","signType": 1,"sealIds": [] } ]
#    api: api/iterate_cases/signfieldsCreate_handSign.yml
#    extract:
#      - signfieldId3: content.data.signfieldBeans.0.signfieldId
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#
#- test:
#    name: "添加签署区：企业-浙农-顺序1"
#    variables:
#
#      - flowId: $flowId1
#      - signfields: [{"fileId":$file_id,"signerAccountId":$accountId_nly,"actorIndentityType":"2","authorizedAccountId":$zhenong_organize_id,"assignedPosbean":true,"order":1,"posBean": {"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
#    api: api/iterate_cases/signfieldsCreate_handSign.yml
#    extract:
#      - signfieldId4: content.data.signfieldBeans.0.signfieldId
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#
#- test:
#    name: "添加签署区：签署方为发起方-顺序1"
#    variables:
#      - flowId: $flowId1
#      - signfields: [{"fileId": "$file_id","signerAccountId": "$accountId_nly","actorIndentityType": "2","authorizedAccountId": "$sixian_organize_id","assignedPosbean": true,"order": 1,"posBean": {"addSignTime": true,"posPage": "1","posX": 100,"posY": 400},"sealType": null,"sealId": "","signType": 1,"sealIds": []}]
#    api: api/iterate_cases/signfieldsCreate_handSign.yml
#    extract:
#      - signfieldId5: content.data.signfieldBeans.0.signfieldId
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#
#
#- test:
#    name: "开启签署流程"
#    variables:
#      - flowId: $flowId1
#    api: api/signflow/signflows/signflowsStart.yml
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", "成功"]
#
#
##意愿认证
#- test:
#    name: 意愿认证-发送验证码
#    api: api/iterate_cases/sendSms.yml
#    variables:
#      - accountId: $account_id_4_tsign
#      - flowId: $flowId1
#    extract:
#      - bizId: content.data.bizId
#    validate:
#      - eq: [status_code, 200]
#      - len_gt: ["content.data.bizId",0]
#
#- test:
#    name: 意愿认证-校验验证码
#    variables:
#      - authcode: 123456
#      - accountId: $account_id_4_tsign
#      - bizId: $bizId
#    api: api/iterate_cases/checkSms.yml
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#
#- test:
#    name: 顺序1的签署区执行签署（签署区1、2、4、5）
#    variables:
#      - accountId: $account_id_4_tsign
#      - flowId: $flowId1
#      - posBean: {addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0}
#      - addSignfields: []
#      - updateSignfield1: {authorizedAccountId: $account_id_4_tsign, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $sealId_qh, signfieldId: $signfieldId1, signerOperatorAuthorizerId: $account_id_4_tsign, signerOperatorId: $account_id_4_tsign}
#      - updateSignfield2: {authorizedAccountId: $account_id_4_tsign, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $sealId_qh, signfieldId: $signfieldId2, signerOperatorAuthorizerId: $account_id_4_tsign, signerOperatorId: $account_id_4_tsign}
#      - updateSignfield4: {authorizedAccountId: $zhenong_organize_id, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $sealId_zn, signfieldId: $signfieldId4, signerOperatorAuthorizerId: $account_id_zn_tsign, signerOperatorId: $account_id_4_tsign}
#      - updateSignfield5: {authorizedAccountId: $sixian_organize_id, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $sealId_sx, signfieldId: $signfieldId5, signerOperatorAuthorizerId: $account_id_sx_tsign, signerOperatorId: $account_id_4_tsign}
#
#      - updateSignfields: [$updateSignfield1,$updateSignfield2,$updateSignfield4,$updateSignfield5]
#      - signfieldIds: []
#      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
#      - dingUser:   #钉钉用户数据，钉钉审批时必传
#    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#      - eq: ["content.message", "成功"]
#      - eq: ["content.data.docSignResult.0.optResult", 0]
#
#
#- test:
#    name: 顺序2的签署区执行签署（签署区3）
#    variables:
#      - accountId: $account_id_4_tsign
#      - flowId: $flowId1
#      - posBean: {addSignTime: True, keyword: '', posPage: '1', posX: 400, posY: 600, qrcodeSign: True, width: 0}
#      - addSignfields: []
#      - updateSignfield3: {authorizedAccountId: $account_id_4_tsign, extendFieldDatas: {}, posBean: $posBean, sealFileKey: '', sealId: $sealId_qh, signfieldId: $signfieldId3, signerOperatorAuthorizerId: $account_id_4_tsign, signerOperatorId: $account_id_4_tsign}
#
#      - updateSignfields: [$updateSignfield3]
#      - signfieldIds: []
#      - approvalPolicy: 1  #审批策略，1-标准审批，2-钉钉审批，3-钉钉混合审批流,默认1
#      - dingUser:   #钉钉用户数据，钉钉审批时必传
#    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]
#      - eq: ["content.message", "成功"]
#      - eq: ["content.data.docSignResult.0.optResult", 0]
#
#
#- test:
#    name: 归档流程
#    variables:
#      - flowId: $flowId1
#    api: api/signflow/signflows/signflowsArchive.yml
#    validate:
#      - eq: [status_code, 200]
#      - eq: ["content.code",0]

# 指定签署方付费
- test:
    name: "流程发起成功-非自动归档-指定签署方付费"
    api: api/sign-flow/create-by-file.yml
    variables:
      json: {
          "signFlowInitiator": {
            "orgInitiator": {
              "orgId": $account_id_sx_tsign,
              "transactor": {
                "psnId": "$account_id_4_tsign"
              }
            }
          },
          "docs": [
            {
              "fileId": "$file_id",
              "fileName": "测试文档1.pdf",
            }
          ],

          "signFlowConfig": {
            "signFlowTitle": "流程发起成功-非自动归档-指定签署方付费",
            "autoFinish": true,
            "autoStart": true,
            "chargeConfig": {
              "chargeMode": 2,
              "orderType": "DISTRIBUTION",
              "chargeOrgName": ["非常异端咖啡馆"],
              "chargeOrgId": ["b55d438d4a574b48b4e9bf4603f026c7"] # "esigntest蛋V2企业甲",
            }
          },
          "signers": [
            {
              "orgSignerInfo": {
                  "orgName": "非常异端咖啡馆",
                  "transactorInfo": {
                      "psnAccount": "***********",
                      "psnName": "测试猫猫零"
                  }
              },
              "signerType": 1,
              "signFields": [
                  {
                    "signFieldType": "0",
                      "fileId": "$file_id",
                      "normalSignFieldConfig": {
                          "signFieldStyle": 1,
                          "autoSign-": true,
                          "freeMode": false,
                          "signFieldPosition": {
                              "positionPage": "1",
                              "positionX": "150",
                              "positionY": "250"
                          }
                      }
                  }
              ]
            },
            {
              "orgSignerInfo": {
                  "orgId":  "b55d438d4a574b48b4e9bf4603f026c7", #"esigntest蛋V2企业甲",
                  "transactorInfo": {
                      "psnId": "8a48c66df52e40b4a5b29320eaa87117"
                  }
              },
              "signerType": 1,
              "signFields": [
                  {
                    "signFieldType": "0",
                      "fileId": "$file_id",
                      "normalSignFieldConfig": {
                          "signFieldStyle": 1,
                          "autoSign-": true,
                          "freeMode": false,
                          "signFieldPosition": {
                              "positionPage": "1",
                              "positionX": "150",
                              "positionY": "250"
                          }
                      }
                  }
              ]
            }
          ]
        }

    extract:
      - flowId2: content.data.signFlowId
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 0]
      - eq: [content.message, '成功']

- test:
    name: "V2查询流程-chargeOrgId接口返回计费模式（Integer chargeMode）"
    variables:
      - flowId: $flowId2
      - queryAccountId: "b55d438d4a574b48b4e9bf4603f026c7"
      - operator_id: "8a48c66df52e40b4a5b29320eaa87117"
    api: api/mobile-shield/signflows_search_V2.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.configInfo.chargeMode", 2]