#def: org_create_by_third($creator, $idNumber, $idType, $name, $thirdPartyUserId)
request:
  url: ${ENV(footstone_user_url)}/v1/v1/organizations/createByThirdPartyUserId
  method: POST
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)}
    X-Tsign-Open-Auth-Mode: $auth_mode
  json:
    {
      "creator": $creator,
      "idNumber": $idNumber,
      "idType": $idType,
      "name": $name,
      "thirdPartyUserId": $thirdPartyUserId
    }
validate:
  - eq: [status_code, 200]
#获取账号详情
