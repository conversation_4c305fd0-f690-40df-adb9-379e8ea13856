- config:
    name: 发起个人静默授权
    base_url: ${ENV(base_url)}
    variables:
      - app_Id: "**********"
      - app_id: "**********"
      - account_id: c737c211e0f34abcb726ad879c0337fe
      - client_type: PC
      - language: "zh-CN"
      - redirect_url: https://www.esign.cn/
      - test_phone: ***********
      - test_idcard: 320703199605082292
      - test_name: 测试诸宏
      - notifyUrl: "http://libaohui.com.cn/callback/ding"


- test:
    name: 正常用例-发起个人静默授权
    api: api/signAuthApi/authUrl.yml
    variables:
      - json: {
          "appId": "$app_Id",
          "accountId": "$account_id",
          "clientType": "$client_type",
          "language": "$language",
          "redirectUrl": "$redirect_url",
          "notifyUrl": "$notifyUrl"
        }
    extract:
      - authId: content.data.authId
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 查询运营支撑平台
    variables:
      - app_Id: "$app_Id"
      - json: {"authId": "", "certNo": "519835297135178216", "appId": "**********", "pageNum": 1, "pageSize": 5}
    api: api/authRecordSelectPage/input.yml
    validate:
      - eq: ["status_code", 200]
      - gt: ["content.data.total", 5]

- test:
    name: 正常初始化静默授权
    api: api/authRecordSelectPage/silent_authorization_initialize.yml
    variables:
      - authId: "${authId}"
    validate:
      - eq: ["status_code", 200]


- test:
    name: 正常获取静默授权识别URL
    api: api/authRecordSelectPage/silent_authorization_identify_url.yml
    variables:
      - authId: "${authId}"
      - appId: "${app_id}"
      - json: {
          bizId: "${authId}",
          bizType: "authSign",
          appId: "${appId}"}
    validate:
      - eq: ["status_code", 200]


- test:
    name: 取消静默授权
    variables:
      - json: { "authId": "${authId}" }
    api: api/v1/signAuthApi/cancelAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]


- test:
    name: 账号不属于当前开发者
    api: api/signAuthApi/authUrl.yml
    variables:
      - json: {
          "appId": "$account_id",
          "accountId": "713e3c56d0b24b8d98766693d6e675b8",
          "clientType": "PC"
        }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 账号不属于当前开发者"]


- test:
    name: 账号证件号和传入证件号不能同时为空
    api: api/signAuthApi/authUrl.yml
    variables:
      - json: {
          "appId": "$app_Id",
          "accountId": "25f3f9d2b27846ccabc6690dce0afcd8",
          "clientType": "$client_type",
          "language": "$language",
          "redirectUrl": "$redirect_url"
        }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 账号证件号和传入证件号不能同时为空"]
      - eq: ["content.data", null]

- test:
    name: 缺失必填参数_accountId
    api: api/signAuthApi/authUrl.yml
    variables:
      - json: {
          "appId": "$app_Id",
          "clientType": "$client_type",
          "language": "$language",
          "redirectUrl": "$redirect_url"
        }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: 授权个人账号oid不能为空"]
      - eq: ["content.data", null]

- test:
    name: 非法枚举值_clientType
    api: api/signAuthApi/authUrl.yml
    variables:
      - client_type_invalid: TABLET
      - json: {
          "appId": "$app_Id",
          "accountId": "$account_id",
          "clientType": "$client_type_invalid",
          "language": "$language",
          "redirectUrl": "$redirect_url"
        }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: clientType无对应枚举值"]
      - eq: ["content.data", null]

- test:
    name: 枚举值验证_clientType_MOBILE
    api: api/signAuthApi/authUrl.yml
    variables:
      - client_type_enum: MOBILE
      - json: {
          "appId": "$app_Id",
          "accountId": "$account_id",
          "clientType": "$client_type_enum",
          "language": "$language",
          "redirectUrl": "$redirect_url"
        }
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: clientType无对应枚举值"]
      - eq: ["content.data", null]

- test:
    name: 枚举值验证_clientType_PC
    api: api/signAuthApi/authUrl.yml
    variables:
      - client_type_enum: PC
      - json: {
          "appId": "$app_Id",
          "accountId": "$account_id",
          "clientType": "$client_type_enum",
          "language": "$language",
          "redirectUrl": "$redirect_url"
        }
    extract:
      - authId: content.data.authId
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 取消静默授权2222
    variables:
      - json: { "authId": "${authId}" }
    api: api/v1/signAuthApi/cancelAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 枚举值验证_language_zh-CN
    api: api/signAuthApi/authUrl.yml
    variables:
      - language_enum: "en-US"
      - json: {
          "appId": "$app_Id",
          "accountId": "$account_id",
          "clientType": "$client_type",
          "language": "$language_enum",
          "redirectUrl": "$redirect_url"
        }
    extract:
      - authId: content.data.authId
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: 取消静默授权33333
    variables:
      - json: { "authId": "${authId}" }
    api: api/v1/signAuthApi/cancelAuth.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]