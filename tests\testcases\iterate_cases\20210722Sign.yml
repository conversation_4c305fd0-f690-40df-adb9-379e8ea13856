- config:
    name: "0722接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - account_id_1: ${ENV(accountId_lbh_tsign)}

- test:
    name: "accountId为空"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: ''
      - authorizedAccountId: $account_id_1
      - flowId: ${ENV(flow_id_with_two_people)}
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,1435002 ]
      - eq: [ content.message,'参数错误: accountId不能为空' ]

- test:
    name: "flowId为空"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: $account_id_1
      - authorizedAccountId: $account_id_1
      - flowId: ''
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,1435002 ]
      - contains: [ content.message,'flowId不能为空' ]

- test:
    name: "accountId与flowId不匹配"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: ${ENV(finish_accountid)}
      - authorizedAccountId: $account_id_1
      - flowId: ${ENV(flow_id_with_two_people)}
    api: api/iterate_cases/identity_detail.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,[ ] ]

- test:
    name: "流程未归档-********迭代可查询"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: $account_id_1
      - authorizedAccountId: $account_id_1
      - flowId: ${ENV(unarchived_flow_id)}
    api: api/iterate_cases/identity_detail.yml
    validate:
      - eq: [ content.code,0 ]
      - eq: [ status_code, 200 ]
      - eq: [ content.message,'成功' ]
      - len_eq: [ content.data,1 ]


- test:
    name: "存在一个签署区"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: $account_id_1
      - authorizedAccountId: $account_id_1
      - flowId: ${ENV(flow_id_with_one_area)}
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ content.message,'成功' ]
      - len_eq: [ content.data,1 ]

- test:
    name: "存在两个签署区"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: $account_id_1
      - authorizedAccountId: $account_id_1
      - flowId: ${ENV(flow_id_with_two_areas)}
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ status_code, 200 ]
      - eq: [ content.message,'成功' ]
      - len_eq: [ content.data,2 ]

- test:
    name: "多人签署,查询时只能返回一个1"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: $account_id_1
      - authorizedAccountId: $account_id_1
      - flowId: ${ENV(flow_id_with_two_people)}
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ status_code, 200 ]
      - eq: [ content.message,'成功' ]
      - len_eq: [ content.data,1 ]

- test:
    name: "多人签署,查询时只能返回一个2"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: ${ENV(account_id_2)}
      - authorizedAccountId: ${ENV(account_id_2)}
      - flowId: ${ENV(flow_id_with_two_people)}
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ status_code, 200 ]
      - eq: [ content.message,'成功' ]
      - len_eq: [ content.data,1 ]

- test:
    name: "无签署意愿返回data为空"
    variables:
      - app_id: ${ENV(app_id_wiliness)}
      - accountId: $account_id_1
      - authorizedAccountId: $account_id_1
      - flowId: ${ENV(flow_id_without_wiliness)}
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ status_code, 200 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data,[ ] ]

- test:
    #悟空+意愿+未实名+开启实名认证
    name: "实名代替意愿"
    variables:
      - accountId: ${ENV(account_id_sign_by_un_real_name)}
      - authorizedAccountId: ${ENV(account_id_sign_by_un_real_name)}
      - flowId: ${ENV(flow_id_sign_by_un_real_name)}
      - app_id: ${ENV(app_id_in_wukong)}
    api: api/iterate_cases/identity_detail.yml

    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ content.message,'成功' ]
      - eq: [ content.data.0.identityType,FACEAUTH_TECENT_CLOUD ]
      - eq: [ content.data.0.identityBizType,2 ]

- test:
    name: "审批代替意愿"
    variables:
      - app_id: ${ENV(app_id_without_wiliness)}
      - accountId: e5f0bd069c634978bc3c5220aea1b8ea
      - authorizedAccountId: ${ENV(authorizedAccountId)}
      - flowId: ${ENV(audit_instead_of_read_name_flow_id)}
    api: api/iterate_cases/identity_detail.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ content.message,成功 ]
      - eq: [ content.data.0.identityType,CODE_SMS ]
      - eq: [ content.data.0.identityBizType,1 ]

- test:
    name: "银行卡实名代替意愿"
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - accountId: ${ENV(account_id_in_bank_card_4_factor_instead_of_read_name)}
      - authorizedAccountId: ${ENV(account_id_in_bank_card_4_factor_instead_of_read_name)}
      - flowId: ${ENV(bank_card_4_factor_instead_of_read_name_flow_id)}
    api: api/iterate_cases/identity_detail.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code,0 ]
      - eq: [ content.message,成功 ]
      - eq: [ content.data.0.identityType,INDIVIDUAL_BANKCARD_4_FACTOR ]
      - eq: [ content.data.0.identityBizType,2 ]