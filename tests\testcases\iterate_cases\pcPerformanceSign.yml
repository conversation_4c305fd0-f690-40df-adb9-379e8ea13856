- config:
    name: "pc性能优化项目迭代用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/劳动合同书12.pdf
      - group: ''
      - app_Id1: ${ENV(xuanyuan_realname)}

- test:
    name: "simpleConfig接口测试"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(wukong_flowId_testify)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(yuzan)}
      - operator_id: ${ENV(yuzan)}
    api: api/iterate_cases/simpleConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "Config接口测试"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(wukong_flowId_testify)}
      - queryAccountId: ${ENV(yuzan)}
      - querySpaceAccountId: ${ENV(yuzan)}
      - operator_id: ${ENV(yuzan)}
    api: api/iterate_cases/config.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]



