- config:
    name: "创建流程的noticeType和redirectDelayTime参数校验"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - group: ''
      - app_Id1: ${ENV(xuanyuan_realname)}

- test:
    name: "新数据-noticeType为空字符串-v1detail接口校验"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(configFlowId4)}
      - queryAccountId: ${ENV(configQueryAccountId)}
      - operatorid: ${ENV(configQueryAccountId)}
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ["content.data.configInfo.redirectDelayTime",0]
      - eq: ["content.data.configInfo.noticeType",'']

- test:
    name: "新数据-noticeType为空字符串-v2detail接口校验"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(configFlowId4)}
      - queryAccountId: ${ENV(configQueryAccountId)}
      - operatorid: ${ENV(configQueryAccountId)}
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ["content.data.configInfo.redirectDelayTime",0]
      - eq: ["content.data.configInfo.noticeType",'']

- test:
    name: "新数据-noticeType为null-v1detail接口校验"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(configFlowId5)}
      - queryAccountId: ${ENV(configQueryAccountId)}
      - operatorid: ${ENV(configQueryAccountId)}
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ["content.data.configInfo.redirectDelayTime",0]
      - eq: ["content.data.configInfo.noticeType",'1']

- test:
    name: "新数据-noticeType为null-v2detail接口校验"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(configFlowId5)}
      - queryAccountId: ${ENV(configQueryAccountId)}
      - operatorid: ${ENV(configQueryAccountId)}
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ["content.data.configInfo.redirectDelayTime",0]
      - eq: ["content.data.configInfo.noticeType",'1']

- test:
    name: "新数据-noticeType为1-v1detail接口校验"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(configFlowId6)}
      - queryAccountId: ${ENV(configQueryAccountId)}
      - operatorid: ${ENV(configQueryAccountId)}
    api: api/signflow/signflows/flowsDetailV1.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ["content.data.configInfo.redirectDelayTime",0]
      - eq: ["content.data.configInfo.noticeType",'1']

- test:
    name: "新数据-noticeType为1-v2detail接口校验"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(configFlowId6)}
      - queryAccountId: ${ENV(configQueryAccountId)}
      - operatorid: ${ENV(configQueryAccountId)}
    api: api/signflow/signflows/flowsDetailV2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]
      - eq: ["content.data.configInfo.redirectDelayTime",0]
      - eq: ["content.data.configInfo.noticeType",'1']




