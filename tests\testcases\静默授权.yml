- config:
    name: "静默授权"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
      - group: ${ENV(envCode)}
      - env: ${ENV(env)}
      - file: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_password: ${ENV(file_with_password_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - psn_1: ${ENV(tengqing_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - org_1: ${ENV(org_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - org_2: ${ENV(org_id_2_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - org_name: ${ENV(xuanyuan_sixian_name)}
      - org_code: ${ENV(sixian_org_code)}

- test:
    name: 发起企业授权书线下签署任务
    variables:
      - json: { "accountId": "${org_1}","fileType": "1","notifyUrl": "","redirectUrl": "","sealScope": "111","sendNotice": true, "validDate": "********" }
    api: api/v1/signAuthApi/offline.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - authId: content.data.authId

- test:
    name: v2一步发起流程，未授权无法发起自动签
    variables:
      - json: { "docs": [ { "fileId": "${file}","fileName": "2页.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "签署" },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "authorizedAccountId": "${org_1}","noticeType": "" },"signfields": [ { "fieldType": 0,"assignedPosbean": false,"autoExecute": true,"actorIndentityType": 2,"sealId": "","fileId": "${file}","posBean": { "posPage": "1","posX": 200,"posY": 200 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 150,"posY": 150 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",用户未授权或授权已过期 ]

- test:
    name: 查询授权记录
    variables:
      - params: authId=${authId}
    api: api/v1/signAuthApi/queryAuthResult.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.status",1 ]

- test:
    name: 上传已盖章授权书文件
    variables:
      - json: { "authId": "${authId}", "fileId": "${file}" }
    api: api/v1/signAuthApi/upload.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(20)}

