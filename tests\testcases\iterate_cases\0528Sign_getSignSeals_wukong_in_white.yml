- config:
    name: "0528接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(wukong_realname_in_white)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: "wukong_realname_in_white下创建签署人王瑶济账号"
    variables:
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - thirdPartyUserId: si201913331141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixian_organize_id
      - alias: 印章1
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixian_organize_id
      - alias: 印章2
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixian_organize_id
      - alias: 印章11
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业1的印章"
    teardown_hooks:
      - ${teardown_seals_chaoshi($response)}
    variables:
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - list1: list11
      - list2: list22
      - list3: list33
      - list4: list44
      - list5: list55
      - sealsId1: sealsId1
      - sealsId1_list: sealsId1_list
      - org_sealIds: org_sealIds
      - sealsId4: sealsId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

###################-个企业签署区###################################
- test:
    name: "创建签署流程-一个签署区"
    variables:
      - autoArchive: true
      - businessScene: 悟空实名-白名单签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个企业签署区-无sealIds和sealId"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表-返回所有包含企业和法人的印章"
    teardown_hooks:
      - ${teardown_seals_list($response,$org_sealIds)}
    variables:
      - flowId: $sign_flowId1
      - accountId: $yuzan_accountId
      - authorizerId: $sixian_organize_id
      - signerAccountId: $yuzan_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
#      - eq: ["isPass",True]

- test:
    name: "创建签署流程-一个签署区"
    variables:
      - autoArchive: true
      - businessScene: 悟空实名-白名单签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个企业签署区-sealIds"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":$list1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表"
    teardown_hooks:
      - ${teardown_seals_list($response,$list1)}
    variables:
      - flowId: $sign_flowId1
      - accountId: $yuzan_accountId
      - authorizerId: $sixian_organize_id
      - signerAccountId: $yuzan_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["isPass",True]

- test:
    name: "创建签署流程-一个签署区"
    variables:
      - autoArchive: true
      - businessScene: 悟空实名-白名单签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个企业签署区-sealId"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":$sealsId1,"signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
#        validate:
#        -   eq: ["content.code",1435002]
#        -   eq: ["content.message","参数错误: 实名签署不允许指定印章id"]

############################两个签署区############################################
- test:
    name: "创建签署流程-两个签署区"
    variables:
      - autoArchive: true
      - businessScene: 悟空实名-白名单签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个企业签署区-A:sealIds,B:sealIds"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":$list1},{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":$list2}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表"
    teardown_hooks:
      - ${teardown_seals_list($response,$list3)}
    variables:
      - flowId: $sign_flowId1
      - accountId: $yuzan_accountId
      - authorizerId: $sixian_organize_id
      - signerAccountId: $yuzan_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["isPass",True]




