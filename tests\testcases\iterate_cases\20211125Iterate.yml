- config:
    name: "1125迭代测试脚本"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - group:  ${ENV(envCode)}
      - fileId: ${ENV(file_with_spin)}
      - account_id0_tsign: ${ENV(account_id3_in_tsign)}
      - account_id1_tsign: ${ENV(account_id1_in_tsign)}
      - account_id2_tsign: ${ENV(account_id2_in_tsign)}
      - account_id3_tsign: ${ENV(original_flowId_accountId)}
      - account_dy_tsign: ${ENV(orgid_dy)}
      - account_sx: ${ENV(orgid_sx)}
      - operator_id: ''

- test:
    name: "根据多个关键字获取坐标"
    api: api/iterate_cases/searchWordsPosition.yml
    variables:
      - fileId: $fileId
      - keywords: 钢铁产品销售合同,合约号:I0A0894
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.0.positionList.0.coordinateList.0.posx", 692.38]
      - eq: ["content.data.0.positionList.0.coordinateList.0.posy", 669.5]
      - eq: ["content.data.0.positionList.1.coordinateList.0.posx", 669.5]
      - eq: ["content.data.0.positionList.1.coordinateList.0.posy", 99.619995]
      - eq: ["content.data.0.positionList.2.coordinateList.0.posx", 99.619995]
      - eq: ["content.data.0.positionList.2.coordinateList.0.posy", 410.5]
      - eq: ["content.data.0.positionList.3.coordinateList.0.posx", 410.5]
      - eq: ["content.data.0.positionList.3.coordinateList.0.posy", 692.38]
      - eq: ["content.data.1.positionList.0.coordinateList.0.posx", 682.34]
      - eq: ["content.data.1.positionList.0.coordinateList.0.posy", 178.0]
      - eq: ["content.data.1.positionList.1.coordinateList.0.posx", 178.0]
      - eq: ["content.data.1.positionList.1.coordinateList.0.posy", 109.65997]
      - eq: ["content.data.1.positionList.2.coordinateList.0.posx", 109.65997]
      - eq: ["content.data.1.positionList.2.coordinateList.0.posy", 902.0]
      - eq: ["content.data.1.positionList.3.coordinateList.0.posx", 902.0]
      - eq: ["content.data.1.positionList.3.coordinateList.0.posy", 682.34]

- test:
    name: "v3一步发起，A签署区配置countdown=5，B签署区未传入countdown，C签署区配置countdown=0  A B C 为不同签署人"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":5,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id2_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"forceReadTime":0,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id3_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId", 32]
    extract:
      - flowId: content.data.flowId

- test:
    name: "获取A签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "获取B签署区的倒计时：10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id2_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: "发起的签署中，A签署区配置countdown=5，B签署区未传入countdown，C签署区配置countdown=0 A B 为相同签署人相同签署主体 有序签，C为不同签署人，"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":5,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"signOrder":2,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"forceReadTime":0,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id3_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId", 32]
    extract:
      - flowId: content.data.flowId

- test:
    name: "获取A签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: "发起的签署中，A签署区配置countdown=5，B签署区为传入countdown，C签署区配置countdown=0  A B 为相同签署人相同签署主体 无序签，C为不同签署人，"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":5,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"forceReadTime":0,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id3_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId", 32]
    extract:
      - flowId: content.data.flowId

- test:
    name: "获取A签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: "发起的签署中，A签署区配置countdown=5，B签署区为传入countdown，C签署区配置countdown=0  A B 为相同签署人不同签署主体 无序签，C为不同签署人，"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":5,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_sx","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"forceReadTime":0,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id3_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId", 32]
    extract:
      - flowId: content.data.flowId

- test:
    name: "指定签署主体，获取A签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "指定签署主体，获取B签署区的倒计时：10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_sx
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "未指定签署主体，获取A B签署区的倒计时：10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: '$account_sx,$account_dy_tsign'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: "发起的签署中，A签署区配置countdown=5，B签署区为传入countdown，C签署区配置countdown=0  A C 为相同签署人不同签署主体 无序签，B为不同签署人"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":5,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id2_tsign","authorizedAccountId":"$account_sx","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"forceReadTime":0,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_sx","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId", 32]
    extract:
      - flowId: content.data.flowId

- test:
    name: "获取A签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "获取A签署区的倒计时：10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id2_tsign
      - querySpaceAccountId: $account_sx
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_sx
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: "发起的签署中，forceReadTime 传了-1，发起失败"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":-1,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"forceReadTime":1,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "发起的签署中，forceReadTime 传了1000，发起失败"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":1000,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "发起的签署中，forceReadTime 传了999，发起成功"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true}}},"signers":[{"platformSign":false,"forceReadTime":999,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_dy_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId", 32]

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id0_tsign
      - initiatorAuthorizedAccountId: $account_id0_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加A签署区配置countdown=5，B签署区未传入countdown，C签署区配置countdown=0 A B C 为不同签署人
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": 5,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$account_id2_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","forceReadTime": 0,"actorIndentityType": 2,signerAccountId: "$account_id3_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "获取A签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "获取B签署区的倒计时：10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id2_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]


- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id0_tsign
      - initiatorAuthorizedAccountId: $account_id0_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加A签署区配置countdown=5，B签署区未传入countdown，C签署区配置countdown=0 A B 为相同签署人相同签署主体 有序签，C为不同签署人
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": 5,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 2,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","forceReadTime": 0,"actorIndentityType": 2,signerAccountId: "$account_id3_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "获取A签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id0_tsign
      - initiatorAuthorizedAccountId: $account_id0_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加A签署区配置countdown=5，B签署区未传入countdown，C签署区配置countdown=0 A B 为相同签署人相同签署主体 无序签，C为不同签署人
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": 5,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","forceReadTime": 0,"actorIndentityType": 2,signerAccountId: "$account_id3_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "获取A B签署区的倒计时：5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id0_tsign
      - initiatorAuthorizedAccountId: $account_id0_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加A签署区配置countdown=5，B签署区未传入countdown，C签署区配置countdown=0 A B 为相同签署人不同签署主体 无序签，C为不同签署人
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": 5,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_sx,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","forceReadTime": 0,"actorIndentityType": 2,signerAccountId: "$account_id3_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "签署链接未指定签署主体时，获取的A B 签署链接倒计时10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: '$account_sx,$account_dy_tsign'
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "签署链接指定签署主体时，A倒计时5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "签署链接指定签署主体时，B倒计时10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_sx
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id3_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]


- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id0_tsign
      - initiatorAuthorizedAccountId: $account_id0_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加A签署区配置countdown=5，B签署区未传入countdown，C签署区配置countdown=0 A C 为相同签署人不同签署主体 无序签，B为不同签署人
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": 5,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","actorIndentityType": 2,signerAccountId: "$account_id2_tsign","authorizedAccountId": $account_sx,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","forceReadTime": 0,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_sx,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程开启
    variables:
      - flowId: $flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "签署区A倒计时5秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_dy_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 5]

- test:
    name: "签署区B倒计时10秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id2_tsign
      - querySpaceAccountId: $account_sx
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 10]

- test:
    name: "获取C签署区的倒计时：0秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_sx
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 0]

- test:
    name: 分布发起流程
    variables:
      - autoArchive: true
      - businessScene: 签签署署
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id0_tsign
      - initiatorAuthorizedAccountId: $account_id0_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加签署区配置countdown=-1 发起失败
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": -1,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: 添加签署区配置countdown=1000 发起失败
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": 1000,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1},{fileId: "$fileId","forceReadTime": 10,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: 添加签署区配置countdown=999 发起成功
    variables:
      - flowId: $flowId
      - json: {signfields: [{fileId: "$fileId","forceReadTime": 999,"actorIndentityType": 2,signerAccountId: "$account_id1_tsign","authorizedAccountId": $account_dy_tsign,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]}
    api: api/signflow/v3/handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: "v3一步发起，流程维度countdown=7，A B 两个签署区为相同人，不同签署主体"
    api: api/signflow/v3/createFlowOneStep.yml
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"签署文件.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署","contractRemind":65,"initiatorAccountId":"$account_id0_tsign","initiatorAuthorizedAccountId":"$account_id0_tsign","flowConfigInfo":{"identificationConfig":{"willTypes":["SIGN_PWD","CODE_SMS"]},"signPlatform":"1,2,3,4,5","buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":true},"signConfig":{"countdown":7}}},"signers":[{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_id1_tsign","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":0,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"},{"platformSign":false,"signOrder":1,"signerAccount":{"signerAccountId":"$account_id1_tsign","authorizedAccountId":"$account_sx","noticeType":""},"signfields":[{"assignedPosbean":true,"autoExecute":false,"actorIndentityType":2,"sealId":"","fileId":"$fileId","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"","signDateBeanType":"1","signDateBean":{"fontSize":20,"posX":220,"posY":250},"signType":1,"width":150}],"thirdOrderNo":"111"}]}
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.flowId", 32]
    extract:
      - flowId: content.data.flowId

- test:
    name: "获取A签署区的倒计时：7秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_id1_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 7]

- test:
    name: "获取B签署区的倒计时：7秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_sx
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 7]

- test:
    name: "指定多主体获取倒计时：7秒"
    api: api/iterate_cases/config.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $account_id1_tsign
      - querySpaceAccountId: $account_sx,$account_id1_tsign
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.pageConfig.countdown", 7]