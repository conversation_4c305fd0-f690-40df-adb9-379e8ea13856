import json
from datetime import datetime

import requests


def 吊销证书(oid):
    import requests
    import json

    url = 'http://cert-service.testk8s.tsign.cn/cloudCertDelete/model'
    headers = {
        'Content-Type': 'application/json',
        'Cookie': 'JSESSIONID=05702062E39637122894276274E94B69'
    }
    data = {
        "oid": oid
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))

    # 打印响应

    print(response.json())
    print(f"吊销证书{oid}，{response.json()}")


"""signFieldType  签署区类型，默认值为 0
0 - 签章区 （添加印章、签名等）
1 - 备注区（添加备注文字信息等）（点击了解 备注签署）
2 - 独立签署日期（添加单独的签署日期）

actorIndentityType  0个人 2企业 3法人 4经办人

signerType签署方类型，  0 - 个人，1 - 企业/机构，2 - 法定代表人，3 - 经办人
"""


def 获取当前年月日时分秒(name):
    print("当前执行类型是:{}".format(name))
    now = datetime.now()
    return name + now.strftime("%Y-%m-%d %H:%M:%S")


class 法人():
    signerAccountId = "713e3c56d0b24b8d98766693d6e675b8"
    psnAccount = "***********"


class 管理员():
    signerAccountId = "24410509f77448fcb8cab2951ea853b6"
    psnAccount = "***********"


class 普通个人():
    signerAccountId = "3986d7544d9848f186ee9c5dcd342de1"
    psnAccount = "***********"


class 企业():
    authorizedAccountId = "90fc72de45174e66b70653c7ec1f7aae"


class 非实名个人():
    signerAccountId = "c4b4b21c3a014c73a078617d4928757e"
    psnAccount = "***********"


def 获取填写人入参(签署类型=1, 是否吊销证书=False):
    if type(签署类型) == int:
        签署类型2 = [签署类型]  # [1,2,3] 处理成列表 可以处理多个
    else:
        签署类型2 = 签署类型
    oidDList = []
    psnAccountList = []
    titleA = ""
    originX = 100
    originY = 100
    for i in 签署类型2:
        签署类型 = i
        originX += 50
        originY += 50
        if 签署类型 == 1:
            title = 获取当前年月日时分秒("法人-法人签")
            signerAccountId = 法人.signerAccountId
            authorizedAccountId = 企业.authorizedAccountId
            actorIndentityType = 3
            signType = 1
            psnAccount = 法人.psnAccount

        elif 签署类型 == 2:
            title = 获取当前年月日时分秒("法人-企业签")
            signerAccountId = 法人.signerAccountId
            authorizedAccountId = 企业.authorizedAccountId
            actorIndentityType = 2
            signType = 1
            psnAccount = 法人.psnAccount
        elif 签署类型 == 3:
            title = 获取当前年月日时分秒("法人-个人签")
            signerAccountId = 法人.signerAccountId
            authorizedAccountId = 法人.signerAccountId
            actorIndentityType = 0
            signType = 1
            psnAccount = 法人.psnAccount

        elif 签署类型 == 4:
            title = 获取当前年月日时分秒("管理员-企业")
            signerAccountId = 管理员.signerAccountId
            authorizedAccountId = 企业.authorizedAccountId
            actorIndentityType = 2
            signType = 1
            psnAccount = 管理员.psnAccount
        elif 签署类型 == 5:
            title = 获取当前年月日时分秒("管理员-个人")
            signerAccountId = 管理员.signerAccountId
            authorizedAccountId = 管理员.signerAccountId
            actorIndentityType = 0
            signType = 1
            psnAccount = 管理员.psnAccount
        elif 签署类型 == 6:
            title = 获取当前年月日时分秒("普通个人-企业")
            signerAccountId = 普通个人.signerAccountId
            authorizedAccountId = 企业.authorizedAccountId
            actorIndentityType = 2
            signType = 1
            psnAccount = 普通个人.psnAccount
        elif 签署类型 == 7:
            title = 获取当前年月日时分秒("普通个人-个人")
            signerAccountId = 普通个人.signerAccountId
            authorizedAccountId = 普通个人.signerAccountId
            actorIndentityType = 0
            signType = 1
            psnAccount = 普通个人.psnAccount
        elif 签署类型 == 8:
            return
        titleA += title
        if 是否吊销证书:
            if 签署类型 == 1:
                吊销证书(signerAccountId)
            else:
                吊销证书(authorizedAccountId)

        oidD = {
            "signOrder": 1,
            "signerAccount": {
                "signerAccountId": signerAccountId,
                "authorizedAccountId": authorizedAccountId
            },
            "signfields": [
                {
                    "autoExecute": False,
                    "actorIndentityType": actorIndentityType,
                    "certId": "",
                    "fileId": "63a913cb97ad4ef9b8834947b48e8862",
                    "sealType": "",
                    "posBean": {
                        "posX": originX,
                        "posY": originY,
                        "posPage": "1"  # 注意：这里有两个 posPage，建议检查是否正确
                    },
                    "signType": 1,

                }
            ],
            "thirdOrderNo": "xyz"
        }

        """signType  签署区类型，默认值为 0
        0 - 签章区 （添加印章、签名等）
        1 - 备注区（添加备注文字信息等）（点击了解 备注签署）
        2 - 独立签署日期（添加单独的签署日期）"""

        psnAccountList.append(psnAccount)
        oidDList.append(oidD)
    return {"oidDList": oidDList, "title": titleA, "psnAccount": set(psnAccountList)}


# 示例调用
def getSignUrl2(签署类型=1, appid版本=1, 是否吊销证书=False, 是否项目环境=1):  # 管理员  ***********  oid 24410509f77448fcb8cab2951ea853b6
    """
    全灰          **********  走意愿弹框  领e签宝ca
    全白          **********  走签署弹框  领天津ca
    弹框灰 证书不灰 **********  走意愿弹框  领天津ca
    弹框不灰 证书灰 **********  走老版签署弹框 领天津ca
    """

    if appid版本 == 1:
        appid = "**********"
    elif appid版本 == 2:
        appid = "**********"
    elif appid版本 == 3:
        appid = "**********"
    else:
        appid = "**********"

    headers = {
        'X-Tsign-Open-App-Id': appid,
        'X-Tsign-Open-Auth-Mode': 'simple',
        'Content-Type': 'application/json;charset=UTF-8',

    }

    headers['X-Tsign-Service-Group'] = 是否项目环境


    """ 1法人-法人签  2法人-企业签  3法人-个人签
        4管理员-企业 5管理员-个人
        6个人-企业 7个人-个人

    """
    res = 获取填写人入参(签署类型=签署类型, 是否吊销证书=是否吊销证书)
    oidDList = res["oidDList"]
    title = res["title"]
    psnAccount = res["psnAccount"]
    url = 'http://in-test-openapi.tsign.cn/api/v3/signflows/createFlowOneStep'

    data = {
        "docs": [
            {
                "encryption": 0,
                "fileId": "63a913cb97ad4ef9b8834947b48e8862",
                "fileName": "测试文档.pdf",
                "source": 0
            }
        ],
        "flowInfo": {
            "autoArchive": True,
            "autoInitiate": True,
            "businessScene": title,
            "flowConfigInfo": {
                "notifyConfig": {
                    "noticeDeveloperUrl": "",
                    "noticeType": "1,2,4"
                },
                "signConfig": {
                    "redirectUrl": "",
                    "signPlatform": "1,2,3,5"
                }
            },
            "hashSign": False,
            "remark": "ca2期",
            "signValidity": *************
        },
        "signers": oidDList
    }

    response = requests.post(url, headers=headers, json=data)

    # 打印响应
    print(f"发起签署的请求体{data}")
    res = response.json()
    print(f"响应体：{res}")
    flowId = res.get("data").get("flowId")
    print("获取的flowid是".format(flowId))

    for psn in psnAccount:
        # 获取签署链接
        url = 'http://in-test-openapi.tsign.cn/v3/sign-flow/{}/sign-url'.format(flowId)
        data = {
            "operator": {
                "psnId": "",
                "psnAccount": psn
            },
            "organization": {
                "orgId": "",
                "orgName": ""
            },
            "clientType": "",
            "appScheme": "",
            "needLogin": False,
            "redirectConfig": {
                "redirectDelayTime": 3,
                "redirectUrl": "https://www.baidu.com"
            }
        }
        response = requests.post(url, headers=headers, data=json.dumps(data))
        res = response.json()
        shortUrl = res.get('data').get('shortUrl')
        print(f"{psn}获取签署链接：{shortUrl}")

    if 1:
        #发送给tengqing的校验接口

        url = "http://ui-e2e.testk8s.tsign.cn/ui/auto/run"
        headers = {
            'Content-Type': 'application/json;charset=UTF-8'
        }
        data = {
            "app": "test",
            "url": shortUrl,
            "terminal": "PC",
            "env": "test",
            "scene": "ca2期单流程主体" if 是否吊销证书 else "ca2期单流程主体不勾选协议",
            "flowId": flowId,
            "commitId": flowId,
            "at": True
        }

        response = requests.post(url, headers=headers, json=data)
        print(f"调用tengqing的校验接口返回的为:{response.json()}")

def getSignUrl(签署类型=1,appid版本=1,是否注销证书=True,是否项目环境=0):
    runD = [1,2,3,4,5,7]# 1,2,3,4,5,6,7
    for i in runD:
        getSignUrl2(签署类型=i, appid版本=appid版本, 是否吊销证书=是否注销证书, 是否项目环境=是否项目环境)


if __name__ == '__main__':
    """  1法人-法人签  2法人-企业签  3法人-个人签
         4管理员-企业  5管理员-个人
         6个人-企业     7个人-个人"""

    """
        1全灰          **********  走意愿弹框  领e签宝ca  0219 1528 改成了无需意愿
        2全白          **********  走签署弹框  领天津ca
        3弹框灰 证书不灰 **********  走意愿弹框  领天津ca
        4弹框不灰 证书灰 **********  走老版签署弹框 领天津ca
    """
    runD = [[6,7]] #1,2,3,4,5,6,7
    for i in runD:
        getSignUrl(签署类型=i,appid版本=3,是否吊销证书=True,是否项目环境=0) #管理员  ***********  oid 24410509f77448fcb8cab2951ea853b6
    # for i in range(1, 8):
    #     getSignUrl(i)

    #0214 已测版本3  把e签宝的全部恢复成天津的