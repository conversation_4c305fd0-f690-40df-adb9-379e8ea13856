- config:
    name: "旋转pdf不显示日期-回调通知中的thirdNo"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf1: data/损坏的pdf.pdf
      - path_pdf2: data/加密.pdf
      - path_pdf3: data/劳动合同书.pdf
      - group: ''
      - app_id: ${ENV(gan_appid_download)}
      - fileId1: "1ee89eebc07c41e38a97dc6789a99d8c"
      - fileId2: "1ee89eebc07c41e38a97dc6789a99d8c"
      - fileId3: "1ee89eebc07c41e38a97dc6789a99d8c"
      - imagfileId: "1ee89eebc07c41e38a97dc6789a99d8c"

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: A1112021121518291111166
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建签署人第二个账号 gan"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: A11120211215182911111
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan2: content.data.accountId
      - yuzan_accountId2: content.data.accountId

- test:
    name: "创建签署人账号 - gan第三个账号"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan_3: content.data.accountId
      - yuzan_accountId_3: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建机构-东营伟信建筑安装工程有限公司"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - thirdPartyUserId: dongying201211
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId

- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - orgId: "8fbe5926547e40149978fb8c5a448394"
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]



- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - accountId: $yuzan_accountId
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [ status_code, 200 ]


#手动将数据取出  用固定的数据
- test:
    name: 一步发起 使用加密的fileid  同一gid对应的两个oid  分别对应两个主体
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: { encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0 }
      - attachments: [ { "attachmentName": "附件","fileId": $imagfileId } ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: { batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers: [
        {
          "signOrder": 1,
          "signerAccount":
            {
              "signerAccountId": $yuzan_accountId,
              "authorizedAccountId": $sixian_organize_id
            },
          "signfields":
            [
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "signerRoleType": "2",
                "certId": "",
                "fileId": $fileId3,
                "sealType": "",
                "posBean":
                  {
                    #                "posPage": "1",
                    "posX": 300,
                    "posY": 300
                  },
                "signDateBeanType": 1,
                "signType": 0

              },
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "signerRoleType": "2",
                "certId": "",
                "fileId": $fileId3,
                "sealType": "",
                "posBean":
                  {
                    #                "posPage": "1",
                    "posX": 400,
                    "posY": 500
                  },
                "signDateBeanType": 1,
                "signType": 0

              }
            ],
          "thirdOrderNo": "企业1"
        },
        {
          "signOrder": 1,
          "signerAccount":
            {
              "signerAccountId": $yuzan_accountId2,
              "authorizedAccountId": $dongying_organize_id
            },
          "signfields":
            [
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "signerRoleType": "2",
                "certId": "",
                "fileId": $fileId3,
                "sealType": "",
                "posBean":
                  {
                    #                  "posPage": "1",
                    "posX": 600,
                    "posY": 600
                  },
                "signDateBeanType": 1,
                "signType": 0

              }
            ],
          "thirdOrderNo": "企业2"
        }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",'成功' ]

- test:
    name: 查询签署区
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code",200 ] #content.data.signfields.0.signerAccountId
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId
      - signfieldId1: content.data.signfields.0.signfieldId



- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]


- test:
    name: "查看合同详情-1"
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId
      - operatorid: $yuzan_accountId
    extract:
      #        -   content_data: content.data
      #        -   content_data_configInfo: content.data.configInfo
      - processId: content.data.processId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]
      - eq: [ "content.data.flowStatus",1 ]

- test:
    name: "查看合同详情-2"
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId2
      - operatorid: $yuzan_accountId2
    extract:
      #        -   content_data: content.data
      #        -   content_data_configInfo: content.data.configInfo
      - processId: content.data.processId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]
      - eq: [ "content.data.flowStatus",1 ]

##手动将数据取出  用固定的数据
- test:
    name: 第二次发起  一步发起   两个gid对应的两个oid  分别对应两个主体
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - doc: { encryption: 0,fileId: $fileId3, fileName: '文件1', source: 0 }
      - attachments: [ { "attachmentName": "附件","fileId": $imagfileId } ]
      - docs: [ $doc ]
      - copiers: [ ]
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: { batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers: [
        {
          "signOrder": 1,
          "signerAccount":
            {
              "signerAccountId": $yuzan_accountId_3,
              "authorizedAccountId": $sixian_organize_id
            },
          "signfields":
            [
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "signerRoleType": "2",
                "certId": "",
                "fileId": $fileId3,
                "sealType": "",
                "posBean":
                  {
                    #                "posPage": "1",
                    "posX": 300,
                    "posY": 300
                  },
                "signDateBeanType": 1,
                "signType": 0

              },
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "signerRoleType": "2",
                "certId": "",
                "fileId": $fileId3,
                "sealType": "",
                "posBean":
                  {
                    #                "posPage": "1",
                    "posX": 400,
                    "posY": 500
                  },
                "signDateBeanType": 1,
                "signType": 0

              }
            ],
          "thirdOrderNo": "1"
        },
        {
          "signOrder": 1,
          "signerAccount":
            {
              "signerAccountId": $yuzan_accountId2,
              "authorizedAccountId": $dongying_organize_id
            },
          "signfields":
            [
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "signerRoleType": "2",
                "certId": "",
                "fileId": $fileId3,
                "sealType": "",
                "posBean":
                  {
                    #                  "posPage": "1",
                    "posX": 600,
                    "posY": 600
                  },
                "signDateBeanType": 1,
                "signType": 0

              }
            ],
          "thirdOrderNo": "2"
        }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",'成功' ]

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: "查看合同详情-3"
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId_3
      - operatorid: $yuzan_accountId_3
    extract:
      #        -   content_data: content.data
      #        -   content_data_configInfo: content.data.configInfo
      - processId: content.data.processId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]
      - eq: [ "content.data.flowStatus",1 ]

- test:
    name: "查看合同详情-4"
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flow_one
      - queryAccountId: $yuzan_accountId2
      - operatorid: $yuzan_accountId2
    extract:
      #        -   content_data: content.data
      #        -   content_data_configInfo: content.data.configInfo
      - processId: content.data.processId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "status_code", 200 ]
      - eq: [ "content.data.flowStatus",1 ]
