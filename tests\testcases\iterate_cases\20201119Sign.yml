- config:
    name: "********迭代接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/劳动合同书12.pdf
      - group: ''
      - app_Id1: ${ENV(xuanyuan_realname)}

- test:
    name: "查询签署区五种状态都返回updateTime字段"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(allStatusFlowId)}
      - accountId:
    api: api/iterate_cases/search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - gt: ["content.data.signfields.0.updateTime",0]
      - gt: ["content.data.signfields.1.updateTime",0]
      - gt: ["content.data.signfields.2.updateTime",0]
      - gt: ["content.data.signfields.3.updateTime",0]
      - gt: ["content.data.signfields.4.updateTime",0]


- test:
    name: "发起人被注销查询流程存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(initiatorLogoutFlowId)}
    api: api/iterate_cases/queryFlowEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "发起人被注销查询流程和文档存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(initiatorLogoutFlowId)}
      - step: 0
    api: api/iterate_cases/queryFlowInfoWithDocEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "发起人被注销查询流程签署存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(initiatorLogoutFlowId)}
      - signSerialId:
    api: api/iterate_cases/queryFlowSignEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "发起主体人被注销查询流程存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(subjectLogoutFlowId)}
    api: api/iterate_cases/queryFlowEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "发起主体人被注销查询流程和文档存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(subjectLogoutFlowId)}
      - step: 0
    api: api/iterate_cases/queryFlowInfoWithDocEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "发起主体人被注销查询流程签署存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(subjectLogoutFlowId)}
      - signSerialId:
    api: api/iterate_cases/queryFlowSignEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "查询正常流程存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(stuFlowId)}
    api: api/iterate_cases/queryFlowEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "查询正常和文档存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(stuFlowId)}
      - step: 0
    api: api/iterate_cases/queryFlowInfoWithDocEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "查询正常流程签署存证信息"
    variables:
      - app_id: $app_Id1
      - flowId: ${ENV(stuFlowId)}
      - signSerialId:
    api: api/iterate_cases/queryFlowSignEvi.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



