config:
  name: 签署人姓名、昵称校验
  variables:
    - m1: "19922222222"
    - m2: "19800000000"
    - e1: <EMAIL>
    - e2: <EMAIL>
    - personOid1: ${getOid($m1)}
    - personOid2: ${getOid($m2)}
    - personOid3: ${getOid($e1)}
    - idType_person: CRED_PSN_CH_IDCARD
    - tPUserId: autotest${get_randomNo()}
    - app_id: ${ENV(BZQ-App-Id)}
    - path_pdf: data/个人借贷合同.pdf
  base_url: ${ENV(footstone_api_url)}

testcases:

  签署人姓名、昵称校验:
    testcase: testcases/signer_check/signerName_check.yml
