- config:
    name: "静默授权"
    base_url: ${ENV(base_url)}
#    variables:
#      - app_id: ${ENV(app_id_tengqing_PaaS_with_willing_and_real_name)}
#      - group: ${ENV(envCode)}
#      - env: ${ENV(env)}
#      - file: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
#      - file_password: ${ENV(file_with_password_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
#      - psn_1: ${ENV(tengqing_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
#      - org_1: ${ENV(org_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
#      - org_2: ${ENV(org_id_2_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
#      - org_name: ${ENV(xuanyuan_sixian_name)}
#      - org_code: ${ENV(sixian_org_code)}

- test:
    name: 模板发起-模板被授权了多枚印章自动签，但模板中签署区未指定具体印章
    variables:
      - json: {"appId": "**********","clientId": "WEB","docs": [{"fileId": "863e9f5cd84447979883b1ff5733bdcb","fileName": "未命名文件1.html","source": 0}],"flowInfo": {"allowToRescind": true,"autoArchive": true,"autoInitiate": true,"businessScene": "模板授权多印章测试","createWay": "template","initiatorAccountId": "cce026d3d4e341979c9576fe18d745a1","initiatorAuthorizedAccountId": "4d856c801d49485bb6e7bf74f1053fef","payerAccountId": "4d856c801d49485bb6e7bf74f1053fef","templateCooperationId": "77188c186b20494db1cc66ab4a4294a2","templateId": "418dd0d0cb7f488585b5f7a91cfc685a"},"signers": [{"signFields": [{"actorIndentityType": "2","autoExecute": false,"fileId": "863e9f5cd84447979883b1ff5733bdcb","posBean": {"posPage": "1","posX": 192.42877,"posY": 310.3003,"signDateBeanType": 1},"signType": 1},{"actorIndentityType": "2","autoExecute": false,"certId": "","fileId": "863e9f5cd84447979883b1ff5733bdcb","handDrawnWay": "","posBean": {"posPage": "1","posX": 390.37726,"posY": 296.0503,"signDateBeanType": 0},"signType": 1}],"signOrder": 0,"signerAccount": {"authorizedAccountId": "4d856c801d49485bb6e7bf74f1053fef","signerAccountId": "cce026d3d4e341979c9576fe18d745a1" } } ]}
    api: api/RPC/createFlowOneStep_input.yml
    validate:
      - ne: [ "content.code",0 ]
      - contains: [ "content.message","模板中签署区未指定具体印章"]

- test:
    name: 模板发起-模板指定的印章ID被删除
    variables:
      - json: {"appId": "**********","clientId": "WEB","docs": [{"fileId": "863e9f5cd84447979883b1ff5733bdcb","fileName": "未命名文件1.html"}],"flowInfo": {"businessScene": "模板授权多印章测试","createWay": "template","initiatorAccountId": "cce026d3d4e341979c9576fe18d745a1","initiatorAuthorizedAccountId": "4d856c801d49485bb6e7bf74f1053fef","payerAccountId": "4d856c801d49485bb6e7bf74f1053fef"},"signers": [{"signFields": [{"actorIndentityType": "2","autoExecute": false,"fileId": "863e9f5cd84447979883b1ff5733bdcb","posBean": {"posPage": "1","posX": 390.37726,"posY": 296.0503,"signDateBeanType": 0},"sealId": "328ace59-bf18-401a-b611-3e75fa6a0c63","signType": 1}],"signOrder": 0,"signerAccount": {"authWay": "","authorizedAccountId": "4d856c801d49485bb6e7bf74f1053fef","signerAccountId": "cce026d3d4e341979c9576fe18d745a1"} }]}
    api: api/RPC/createFlowOneStep_input.yml
    validate:
      - ne: [ "content.code",0 ]
      - contains: [ "content.message","模板中指定的印章已被删除"]

- test:
    name: 模板发起-仅有一枚印章授权模板-发起后自动签-api创建的模板
    variables:
      - json: {"appId": "**********","clientId": "OPEN_SERVICE","flowInfo": {"autoArchive": true,"businessScene": "桃浪流程模板发起-2","initiatorAccountId": "3e60c0b6f3fc4e1082e24b6836a589af","initiatorAuthorizedAccountId": "4af6af757f7b45ab99d7f014c1f2f98b","flowConfigInfo": {"signConfig": {"signPlatform": "1","batchDropSeal": true},"billConfig": {"chargeMode": 0} },"createWay": "template","autoInitiate": true,"templateId": "f2431770071842b7bb7463a9ff4e789c","allowToRescind": true},"docs": [{"fileId": "5d149a15d27b45a0bd551b2709e02ff3","fileName": "小白花假装霸总女友协议.pdf","encryption": 0,"source": 0}],"signers": [{"signOrder": 1,"signerAccount": {"signerRoleLabel": "签署方1","signerAccountId": "3e60c0b6f3fc4e1082e24b6836a589af","signerAccountName": "测试猫猫八","authorizedAccountId": "4af6af757f7b45ab99d7f014c1f2f98b","willTypes": ["FACE","CODE_SMS"],"requiredRealName": true},"platformSign": false,"signFields": [{"actorIndentityType": "2","fileId": "5d149a15d27b45a0bd551b2709e02ff3","signType": 1,"autoExecute": false,"posBean": {"posPage": "1","posX": 173.7463,"posY": 749.9159,"sealSpecs": 1,"signDateBeanType": 2}},{"actorIndentityType": "2","fileId": "5d149a15d27b45a0bd551b2709e02ff3","signType": 1,"autoExecute": false,"posBean": {"posPage": "1","posX": 471.4436,"posY": 751.009,"sealSpecs": 1,"signDateBeanType": 2 } }] } ] }
    api: api/RPC/createFlowOneStep_input.yml
    validate:
      - eq: [ "content.code",0 ]
      - contains: [ "content.message","执行成功"]
    extract:
      - flowId: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 发起后自动签-查询流程状态-已签署完成
    variables:
      - flowId: ${flowId}
      - app_id: "**********"
    api: api/sign-flow/detail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.signFlowStatus",2]
      - eq: ["content.data.signers.0.signStatus",2]


- test:
    name: 模板发起-有多枚印章授权模板-发起后自动签-saas创建的模板
    variables:
      - json: {"appId": "**********","clientId": "OPEN_SERVICE","flowInfo": {"autoArchive": true,"businessScene": "桃浪流程模板发起_1742989227","initiatorAccountId": "3e60c0b6f3fc4e1082e24b6836a589af","initiatorAuthorizedAccountId": "4af6af757f7b45ab99d7f014c1f2f98b","flowConfigInfo": {"signConfig": {"signPlatform": "1","batchDropSeal": true,"signMode": "NORMAL"},"notifyConfig": {"sendNotice": false},"billConfig": {"chargeMode": 0}},"createWay": "template","autoInitiate": true,"templateId": "83cbbf30038b4c64954474b5ec170521","allowToRescind": true},"docs": [{"fileId": "d39cce1375bb432789d8667596e68b15","fileName": "小白花假装霸总女友协议.pdf","encryption": 0,"source": 0}],"signers": [{"signOrder": 1,"signerAccount": {"signerRoleLabel": "签署方1","signerAccountId": "3e60c0b6f3fc4e1082e24b6836a589af","signerAccountName": "测试猫猫八","authorizedAccountId": "4af6af757f7b45ab99d7f014c1f2f98b","willTypes": ["FACE","CODE_SMS","EMAIL","SIGN_PWD"],"requiredWilling": true,"authWay": "REAL_NAME_AUTH","requiredRealName": true},"platformSign": false,"signFields": [{"actorIndentityType": "2","fileId": "d39cce1375bb432789d8667596e68b15","sealId": "70a5e954-cc47-4591-ac71-233f4973ecb0","signType": 1,"autoExecute": false,"posBean": {"posPage": "1","posX": 122.82,"posY": 748.47,"sealSpecs": 1,"signDateBeanType": 2}},{"actorIndentityType": "2","fileId": "d39cce1375bb432789d8667596e68b15","sealId": "ea128958-64df-4623-8933-be9e00de9f93","signType": 1,"autoExecute": false,"posBean": {"posPage": "1","posX": 319.93,"posY": 747.62,"sealSpecs": 1,"signDateBeanType": 2}},{"actorIndentityType": "3","fileId": "d39cce1375bb432789d8667596e68b15","sealId": "30f361f9-2a61-4153-9d4c-ce03cadb605b","signType": 1,"autoExecute": false,"posBean": {"posPage": "1","posX": 489.25,"posY": 738.22,"sealSpecs": 1,"signDateBeanType": 2 } }] }]}
    api: api/RPC/createFlowOneStep_input.yml
    validate:
      - eq: [ "content.code",0 ]
      - contains: [ "content.message","执行成功"]
    extract:
      - flowId2: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 发起后自动签-查询流程状态-已签署完成
    variables:
      - flowId: ${flowId2}
      - app_id: "**********"
    api: api/sign-flow/detail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.signFlowStatus",2]
      - eq: ["content.data.signers.0.signStatus",2]