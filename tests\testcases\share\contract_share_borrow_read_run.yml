- config:
    name: "分享 - 合同借阅"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - app_id: ${ENV(xuanyuan_appid)}
      - path_pdf: 个人借贷合同.pdf
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      # 指定分享的签署流程flow信息，用于创建分享，注：发起人或签署人账号，需要有分享权限
      - initatorAccountId: ${ENV(share_flowid_complete_oid_yes)}
      # -   dongying_realname_orgId: bb4364e663ad43ee9381e65232b5ae1f
      - sign_flowId_completed_for_share: ${ENV(share_flowid_complete)}
      # -   shareId_expired: ${ENV(share_resourceShareId_expired)}
      - shareId_invalid: 123456
      - targetOperatorAccount: ***********
      - targetOperatorName1: 王默默
      - targetOperatorName2: 王二

- test:
    name: "创建非分享对象账号 - 王默默，非实名"
    variables:
      - thirdPartyUserId: autotest-wmm
      - name: 王默默
      - idNumber: 320721199305231234
      - idType: CRED_PSN_CH_IDCARD
      - email: <EMAIL>
      - mobile:
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - no_share_accountId: content.data.accountId

- test:
    name: "创建分享对象账号(非合同参与人) - 王二，非实名"
    variables:
      - thirdPartyUserId: wanger-*********
      - name: 王二
      - idNumber: 320721199305232002
      - idType: CRED_PSN_CH_IDCARD
      - email: <EMAIL>
      - mobile:
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - share_accountId1_norealname: content.data.accountId

- test:
    name: "创建分享对象账号(非合同参与人) - 曾艳，通过手机号/邮箱 查询实名用户oid"
    api: api/user/accounts/get_checkMobileEmail.yml
    variables:
      - principal: ***********
    extract:
      - share_accountId2_realname: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]


- test:
    name: "流程参与人查询 -detail_V2"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $initatorAccountId
      - resourceShareId: ""
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message",成功]
      - eq: ["content.data.flowStatus",2]
    extract:
      - processId: content.data.processId
      - dongying_realname_orgId: content.data.initiatorAuthorizedAccountId

- test:
    api: api/saas-common-manage/createShare.yml
    name: 文件分享创建
    variables:
      accountId: $initatorAccountId
      needShareCode: true
      needShareQrCode: false
      resourceId: $processId
      resourceType: 'PROCESS'
      shareType: 2
      shareOperateType: 'BORROW_READ'
      # shareOperateType: 'CHECK' # 文件核验
      # shareOperateType: 'NORMAL_SHARE' # 催办
      subjectId: $dongying_realname_orgId
      router: 'share'
      shareEndTime: ${get_timestamp_13(3600000)}
      shareTargets:
        [
        {
          targetOperatorOid: $share_accountId1_norealname,
          targetSubjectOid: $share_accountId1_norealname,
          targetAccount: $share_accountId1_norealname,
          targetOperatorAccount: $targetOperatorAccount,
          targetOperatorName: $targetOperatorName1,
          targetAccountType: 0
        },
        {
          targetOperatorOid: $share_accountId2_realname,
          targetSubjectOid: $share_accountId2_realname,
          targetAccount: $share_accountId2_realname,
          targetOperatorAccount: $targetOperatorAccount,
          targetOperatorName: $targetOperatorName2,
          targetAccountType: 0
        }
        ]
    validate:
      - eq: [status_code, 200]
      - contains:
          - content.message
          - '成功'
    extract:
      resourceShareIdNew: content.data.resourceShareId

- test:
    api: api/saas-common-manage/createShare.yml
    name: 创建过期的分享
    variables:
      accountId: $initatorAccountId
      needShareCode: true
      needShareQrCode: false
      resourceId: $processId
      resourceType: 'PROCESS'
      shareType: 2
      shareOperateType: 'BORROW_READ'
      # shareOperateType: 'CHECK' # 文件核验
      # shareOperateType: 'NORMAL_SHARE' # 催办
      subjectId: $dongying_realname_orgId
      router: 'share'
      shareEndTime: 0
      shareTargets:
        [
        {
          targetOperatorOid: $share_accountId2_realname,
          targetSubjectOid: $share_accountId2_realname,
          targetAccount: $share_accountId2_realname,
          targetOperatorAccount: $targetOperatorAccount,
          targetOperatorName: $targetOperatorName2,
          targetAccountType: 0
        }
        ]
    validate:
      - eq: [status_code, 200]
      - contains:
          - content.message
          - '成功'
    extract:
      shareId_expired: content.data.resourceShareId

- test:
    name: "分享参与人（未实名）查询 - 有resourceShareId -signDocDetail"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId1_norealname
      - resourceShareId: $resourceShareIdNew
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-1437183]
      - eq: ["content.message","未完成实名认证，不允许访问。"]


- test:
    name: "分享参与人（已实名）查询 - 有resourceShareId -signDocDetail"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId2_realname
      - resourceShareId: $resourceShareIdNew
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.flowDocs.0.docStatus",2]



- test:
    name: "非分享参与人查询 -signDocDetail: 无权限操作"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $no_share_accountId
      - resourceShareId: $resourceShareIdNew
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-1437184]
      - eq: ["content.message","无权限操作"]


- test:
    name: "分享参与人（已实名）查询 -signDocDetail: resourceShareId expired"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId2_realname
      - resourceShareId: $shareId_expired
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-********]
      - eq: ["content.message","分享状态已失效，不允许访问。"]


- test:
    name: "分享参与人（已实名）查询 -signDocDetail: resourceShareId invalid"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId2_realname
      - resourceShareId: $shareId_invalid
    api: api/share/signDocDetail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-1437184]
      - eq: ["content.message","无权限操作"]


- test:
    name: "分享参与人（未实名）查询 - 有resourceShareId -detail_V2"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId1_norealname
      - resourceShareId: $resourceShareIdNew
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-1437183]
      - eq: ["content.message","未完成实名认证，不允许访问。"]


- test:
    name: "分享参与人（已实名）查询 - 有resourceShareId -detail_V2"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId2_realname
      - resourceShareId: $resourceShareIdNew
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.data.flowStatus",2]
      - eq: ["content.data.signedDocs.0.signStatus",2]

- test:
    name: "非分享参与人查询 - 有resourceShareId -detail_V2"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $no_share_accountId
      - resourceShareId: $resourceShareIdNew
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-1437184]
      - eq: ["content.message","无权限操作"]

- test:
    name: "分享参与人（已实名）查询 -detail_V2: resourceShareId expired"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId2_realname
      - resourceShareId: $shareId_expired
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-********]
      - eq: ["content.message","分享状态已失效，不允许访问。"]


- test:
    name: "分享参与人（已实名）查询 -detail_V2: resourceShareId invalid"
    variables:
      - flowId: $sign_flowId_completed_for_share
      - queryAccountId: $share_accountId2_realname
      - resourceShareId: $shareId_invalid
    api: api/share/signflows_detail_V2.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",-1437184]
      - eq: ["content.message","无权限操作"]