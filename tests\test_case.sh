#!/usr/bin/env bash
#workdir=$(cd $(dirname $0); pwd)
#echo $workdir


function var_parse() {
   filename=$1
   testcase_name=$2
   api_var=$3
   api_call=`grep -n 'suite:\s*'$testcase_name  $filename |awk -F':' '{print $1}'`

   deff=0
   for row_num in $api_call
   do
        row_num=`expr $row_num + $deff`
        a=""
        b=`sed -n  $row_num'p' $filename | awk '{if(/#/) {print 1} else{print 0}}'`

        arr=(${api_var//,/ })
        length3=${#arr[@]}
        if [ $length3 -gt 0 ] && [  "$b" -eq "0" ]
        then
             var2=`sed -n  $row_num'p' $filename | sed 's|.*suite:\s*'$testcase_name'(\(.*\)).*|\1|g;'`
             arr2=(${var2//,/ })
             echo  $row_num'==== 传入参数: '$arr2
             for ((i3=0; i3<$length3; i3++))
             do
                  a=$a'\n           '${arr[$i3]}': '${arr2[$i3]}
             done
             deff=`expr $deff + $length3`
#             echo $row_num':'$a
             sed -i $row_num's|suite: .*|variables: '"$a"'|;'   $filename
        else
            sed -i $row_num'd' $filename
        fi
   done
   ##end  variables
}


function replace_testcase_path() {
        echo ""$1
        testcase_file=$1
        #查找原suite定义的方法名，# 如 def: idno_org_action($creator, $name, $idNumber, $idType) 获取idno_org_action
        testcase_name=`sed -n  '/def: /p' $testcase_file | sed 's/def: \s*\([a-zA-Z0-9_.-]*\)(.*).*/\1/g; s/^\s*//g'`
if [ -n "$testcase_name" ]
then
        testcase_var=`sed -n  '/def: /p' $testcase_file | sed -e 's/def: \s*[a-zA-Z0-9_.-]*(\(.*\)).*/\1/g; s/[\$]//g'`

        echo '==== 搜索调用suite :'$testcase_name'()的文件 '
        echo '==== 替换为：testcase:'  "$testcase_file"
        echo '==== 原参数: '$testcase_var
        #sed -i 's|suite:\s*'$testcase_name'(.*)|testcase: '$testcase_file'|g;' `grep -rl $testcase_name`

        call_files=`grep -rl 'suite:\s*'$testcase_name  $suite_dir`
        for filename in $call_files
        do
               echo '====  suite_file: '$filename
               sed -i '/suite:\s*'$testcase_name'/p;s|suite:\s*'$testcase_name'(.*)|testcase: '$testcase_file'|g;'  $filename
               ##将def的参数生成variables
               var_parse  $filename  "$testcase_name"  "$testcase_var"
        done
fi
}

function testcase_formate() {
  testcasefiles=`find $case_dir -type f -name "*.yml"`
  for testcase_file in $testcasefiles
    do
        replace_testcase_path  $testcase_file
        # '将原来suite里定义的def：action_function()转换调'
        sed -i 's/def:/#def:/g; ' $testcase_file
#        sed -i 's/def:/id:/g; s/(.*)//g;' $testcase_file
    done
}

#suite_dir=$1'testcases/'
#case_dir=$1'suite/'

if [ ! -d 'testsuites' ]; then  mv  $1'testcases' $1'testsuites';   fi
if [ ! -d 'testcases' ]; then  mv $1'suite' $1'testcases';   fi
suite_dir=$1'testsuites/'
case_dir=$1'testcases/'

testcase_formate >> tmp_testcase.log

#replace_testcase_path  "$case_dir"org_is_deleted.yml