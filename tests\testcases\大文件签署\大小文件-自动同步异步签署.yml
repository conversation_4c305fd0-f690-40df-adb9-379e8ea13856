- config:
    name: 大文件签署-自动签+手动签
    base_url: ${ENV(footstone_signflow_initiate_url)}
    variables:
      - app_id: ${ENV(mobile_shield_wukong_appid)}

      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}

      - bigfile30M: ${ENV(bigfile30M_id_in_app_id_mobile_shield_wukong_appid)}
      - bigfile22M: ${ENV(bigfile22M_id_in_app_id_mobile_shield_wukong_appid)}
      - file_small: ${ENV(file_id_in_app_id_mobile_shield_wukong_appid)}
      - liangxianhong_stan: ${ENV(orgdy_legal_oid)}
      - yuzan_stan: ${ENV(oid_wang_tsign)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
      - ganning_saas_oid: ${ENV(gn_oid_tsign)}
      - sealId_dy_approval: ${ENV(sealId_dy_approval)}
      - sealId_dy_noapproval: ${ENV(sealId_dy_noapproval)}
      - sealId_zl: ${ENV(sealId_zl)}
      - sealId_gn: ${ENV(sealId_gn)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}

- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: fhg123456ws
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建甘宁账号"
    variables:
      - thirdPartyUserId: z111zzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司"
    variables:
      - thirdPartyUserId: don11321231wer
      - creator: $accountId_gan
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId




- test:
    name: "静默授权 - 钟灵"
    variables:
      - accountId: $accountId_ckl
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]

- test:
    name: "静默授权 - 甘宁"
    variables:
      - accountId: $accountId_gan
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]

- test:
    name: "静默授权 - esigntest东营伟信建筑安装工程有限公司"
    variables:
      - accountId: $dongying_organize_id
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]

#平台自动签
- test:
    name: createFlowOneStep - 平台自动签 - 顺序1大文件小文件 - 顺序2小文件 - 3个签署区均异步签署
    variables:
      - doc1: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $bigfile30M, fileName: "30M.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $bigfile22M, fileName: "22M.pdf", source: 0}
      - attachments: []
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: false, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 顺序1大文件小文件 - 顺序2小文件",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: true, signOrder: 1,signerAccount: { }, signfields: [ { certId: "", autoExecute: true,  fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },{platformSign: true, signOrder: 1,signerAccount: { }, signfields: [ { certId: "", autoExecute: true,  fileId: $bigfile30M,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },{platformSign: true, signOrder: 2,signerAccount: { }, signfields: [ { certId: "", autoExecute: true,  fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId1: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: 查询签署区信息
    variables:
      - flowId: $flowId1
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signfields.0.status", 4]
      - eq: ["content.data.signfields.0.statusDescription", 执行完成]
      - eq: ["content.data.signfields.1.status", 4]
      - eq: ["content.data.signfields.2.status", 4]


- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: createFlowOneStep - 追加平台自动签 - 顺序2小文件 - 同步签
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':260, 'posY':200}
      - signfield_file_small: { 'fileId':$file_small,  'order':2,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file_small]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: createFlowOneStep - 追加用户自动签 - 顺序2个人签署区小文件&企业签署大文件
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':400, 'posY':200}
      - signfield_file1: { 'fileId':$file_small, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'0', 'authorizedAccountId':$accountId_ckl,'assignedPosbean':True, 'order':2,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfield_file2: { 'fileId':$bigfile22M, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$dongying_organize_id,'assignedPosbean':True, 'order':2,  'posBean':$posBean, 'sealType':'','sealId': $sealId_dy_noapproval,'signType':1}
      - signfields: [$signfield_file1,$signfield_file2]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: createFlowOneStep - 追加用户自动签 - 顺序3个人签署区小文件&企业签署小文件 - 不同签署人
    variables:
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':400, 'posY':200}
      - signfield_file1: { 'fileId':$file_small, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'0', 'authorizedAccountId':$accountId_ckl,'assignedPosbean':True, 'order':3,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfield_file2: { 'fileId':$file_small, 'signerAccountId': $accountId_ckl, 'actorIndentityType':'2', 'authorizedAccountId':$dongying_organize_id,'assignedPosbean':True, 'order':3,  'posBean':$posBean, 'sealType':'','sealId': $sealId_dy_noapproval,'signType':1}
      - signfield_file3: { 'fileId':$file_small, 'signerAccountId': $accountId_gan, 'actorIndentityType':'2', 'authorizedAccountId':$dongying_organize_id,'assignedPosbean':True, 'order':3,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1,$signfield_file2,$signfield_file3]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: createFlowOneStep - 追加用户手动签 - 顺序3个人签署区小文件-钟灵
    variables:
      - flowId: $flowId1
      - signfields: [{"fileId":$file_small,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":3,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_3_small: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: createFlowOneStep - 追加用户手动签 - 顺序3企业签署区大文件-钟灵
    variables:
      - flowId: $flowId1
      - signfields: [{"fileId":$bigfile22M,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId":$dongying_organize_id,"assignedPosbean":true,"order":3,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_3_big: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $accountId_ckl
      - flowId: $flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $accountId_ckl
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    setup_hooks:
      - ${sleep_N_secs(10)}
    name: 顺序3手动签署区执行签署
    variables:
      - flowId: $flowId1
      - accountId: $accountId_ckl
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield1: {"fileId":$file_small,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":3,"posBean":$posBean,"sealType":"","sealId":$sealId_zl,"signType":1,"signfieldId": $signfieldId_3_small}
      - updateSignfield2: {"fileId":$bigfile22M,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId":$account_id_dy_tsign,"assignedPosbean":true,"order":3,"posBean":$posBean,"sealType":"","sealId":$sealId_dy_noapproval,"signType":1,"signfieldId": $signfieldId_3_big}
      - addSignfields: []
      - updateSignfields: [$updateSignfield1,$updateSignfield2]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $accountId_ckl
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: ["content.data.serviceId",1]
    extract:
      - serviceId: content.data.serviceId

- test:
    setup_hooks:
      - ${sleep_N_secs(3)}
    name: 归档流程
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]




#一步发起-添加平台自动签、用户自动签、用户手动签
- test:
    name: createFlowOneStep - 顺序1平台自动签小文件 - 顺序1个人自动签大文件 - 顺序1企业自动签小文件 - 顺序1个人手动签大文件 - 顺序2个人自动签小文件
    variables:
      - doc1: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $bigfile30M, fileName: "30M.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $bigfile22M, fileName: "22M.pdf", source: 0}
      - attachments: []
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: false, autoInitiate: true, businessScene: " createFlowOneStep - 平台自动签 - 顺序1大文件小文件 - 顺序2小文件",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signer1: {platformSign: true, signOrder: 1,signerAccount: { }, signfields: [ { certId: "", autoExecute: true,  fileId: $file_small,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }
      - signer2: {platformSign: false, signOrder: 1,signerAccount: {signerAccountId: $accountId_ckl,"authorizedAccountId": $accountId_ckl},signfields: [ { certId: "", autoExecute: true, 'actorIndentityType':'0',  fileId: $bigfile22M,   posBean: { posPage: 1,  posX: 210,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 776}
      - signer3: {platformSign: false, signOrder: 1,signerAccount: {signerAccountId: $accountId_ckl,"authorizedAccountId": $dongying_organize_id}, signfields: [ { certId: "", autoExecute: true, 'actorIndentityType':'2', "sealId": $sealId_dy_noapproval, fileId: $file_small,   posBean: { posPage: 1,  posX: 310,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 78 }
      #        - signer4: {platformSign: false, signOrder: 1,signerAccount: {signerAccountId: $accountId_ckl,"authorizedAccountId": $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, 'actorIndentityType':'0',  fileId: $bigfile22M,   posBean: { posPage: 1,  posX: 310,   posY: 110  }, signType: 2,   width: 150 }],  thirdOrderNo: 727 }
      - signer5: {platformSign: false, signOrder: 2,signerAccount: {signerAccountId: $accountId_ckl,"authorizedAccountId": $accountId_ckl}, signfields: [ { certId: "", autoExecute: true, 'actorIndentityType':'0',  fileId: $file_small,   posBean: { posPage: 1,  posX: 310,   posY: 210  }, signType: 1,   width: 150 }],  thirdOrderNo: 717 }
      - signers: [$signer1, $signer2, $signer3, $signer5]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId2: content.data.flowId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}


      #- test:
      #    name: 流程2查询签署区信息
      #    variables:
      #        - flowId: $flowId2
      #    api: api/mobile-shield/signflows_search_signfields.yml
      #    validate:
#      - eq: ["content.code", 0]
#      - eq: ["content.message", "成功"]
      #    extract:
      #        - flowId2_signfieldId_handsign: content.data.signfields.3.signfieldId


      #
      #- test:
      #    name: 意愿认证-发送验证码
      #    api: api/iterate_cases/sendSms.yml
      #    variables:
      #      - accountId: $accountId_ckl
      #      - flowId: $flowId2
      #    extract:
      #      - bizId: content.data.bizId
      #    validate:
      #      - len_gt: ["content.data.bizId",0]
      #
      #- test:
      #    name: 意愿认证-校验验证码
      #    variables:
      #      - authcode: 123456
      #      - accountId: $accountId_ckl
      #      - bizId: $bizId
      #    api: api/iterate_cases/checkSms.yml
      #    validate:
#      - eq: ["content.code",0]
#
#
#- test:
#    name: 流程2顺序1手动签署区执行签署
#    variables:
#      - flowId: $flowId2
#      - accountId: $accountId_ckl
#      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
#      - updateSignfield1: {"fileId":$bigfile22M,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealId_zl,"signType":1,"signfieldId": $flowId2_signfieldId_handsign}
#      - addSignfields: []
#      - updateSignfields: [$updateSignfield1]
#      - signfieldIds: []
#      - dingUser: {}
#      - approvalPolicy: 0
#      - operator_id: $accountId_ckl
#    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
#    validate:
#      - contains: ["content.message","成功"]
#      - len_gt: ["content.data.serviceId",1]
#    extract:
#      - serviceId: content.data.serviceId
#
#
#- test:
#    name: 归档流程
#    variables:
#      - flowId: $flowId2
#    api: api/signflow/signflows/signflowsArchive.yml
#    validate:
#      - eq: [status_code, 200]
#      - contains: ["content.message","成功"]