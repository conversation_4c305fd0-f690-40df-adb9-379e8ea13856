- config:
    name: "审批相关的测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: "${ENV(BZQ-App-Id)}"
      #         - stu_accountId: "1337d059ea4d422099c80c9e2e2fb2c3"
      - mobile: ***********
      - realname_orgId: "${ENV(seal_realname_org)}"

- test:
    name: "根据登陆凭证获取accountId"
    variables:
      - idcard: $mobile
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId: content.data.items.0.accountId

- test:
    name: "查询实名组织下的企业印章"
    variables:
      - orgId: $realname_orgId
    api: api/seals/seals/select_c_seals.yml
    extract:
      - sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "文件直传创建一个文件"
    variables:
      - app_id: $app_id
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - app_id: $app_id
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: "创建签署流程"
    variables:
      - autoArchive: true
      - businessScene: "您有一份待签署的任务"
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $stu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: $app_id
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "企业签名域-单页签署"
    variables:
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$stu_accountId,"actorIndentityType":"2","authorizedAccountId":$realname_orgId,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "签署人-运营商三要素意愿认证-意愿发送验证码"
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $stu_accountId
      - flowId: $sign_flowId
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.bizId",0]

- test:
    name: "回填三要素验证码"
    variables:
      - authcode: 123456
      - accountId: $stu_accountId
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加或更新签署区并执行签署"
    variables:
      - flowId: $sign_flowId
      - accountId: $stu_accountId
      - addSignfields:
      - updateSignfields: [{"sealId":$sealId1,"signerOperatorAuthorizerId":$realname_orgId,"signerOperatorId":$stu_accountId,"signfieldId":$signfieldId1}]
      - signfieldIds:
    api: api/iterate_cases/addUpdateExecute_sign.yml
    extract:
      - approveFlowId1: content.data.signfieldSignResult.0.approveFlowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


##################### 用印审批管理员 ################################
- test:
    name: "审批列表 - 我发起的用印审批中"
    variables:
      - app_id: $app_id
      - type: 2
      - status: [1]
      - operator: $stu_accountId
      - space_orgId: $realname_orgId
    api: api/seals/approvalflows/approvalflows_list.yml
    extract:
      - batchId: content.data.approvals.0.batchId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "审批管理 - 撤回审批"
    variables:
      - app_id: $app_id
      - accountId: $stu_accountId
      - batchIds: [$batchId]
      - operator: $stu_accountId
      - space_orgId: $realname_orgId
    api: api/seals/approvalflows/approvalflows_batchCancel.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]

- test:
    name: "审批列表-审批撤回"
    variables:
      - flowId: $sign_flowId
      - params: order=&queryAccountId=$stu_accountId&querySubjectId=$realname_orgId
    api: api/v3/signflows/flowId/sealApproval/list.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.0.status", 1]

- test:
    name: "审批详情-审批撤回"
    variables:
      - flowId: $sign_flowId
      - params: approvalId=$approveFlowId1&operatorId=$stu_accountId&operatorSubjectId=$realname_orgId
    api: api/v3/signflows/flowId/sealApproval/detail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.taskInfoList.0.taskStatus", 1]

- test:
    name: "审批列表 - 我发起的用印审批撤回"
    variables:
      - app_id: $app_id
      - type: 2
      - status: [3]
      - operator: $stu_accountId
      - space_orgId: $realname_orgId
      - sortType: 3
    api: api/seals/approvalflows/approvalflows_list.yml
    #    extract:
    #        - batchId: content.data.approvals.0.batchId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
#      - eq: ["content.data.approvals.0.batchId", -1]

- test:
    name: "审批管理 - 撤回审批"
    variables:
      - flowId: $sign_flowId
      - json: {"approvalId":"$approveFlowId1","operatorId":"$stu_accountId","operatorSubjectId":"$realname_orgId","revokeReason":"审批撤回"}
    api: api/v3/signflows/flowId/sealApproval/revoke.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]