- config:
    name: 用户自动签校验
    base_url: ${ENV(footstone_api_url)}
    variables:
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_Id1: ${ENV(appId_sealPlatform)}
      - app_id1: ${ENV(appId_sealPlatform)}
      - path_pdf: data/个人借贷合同.pdf
      - mobile: "***********"
      - name: 黄华南
      - idNo: 330328199308301118
      - thirdPartyUserId1: a${get_randomString()}
      - thirdPartyUserId2: b${get_randomString()}
      - mobile: "***********"
      - name1: esigntest江门市诚峰镖局物流有限公司（印章平台测试企业）
      - idNo1: 91440705MA52G73E89

- test:
    name: 创建流程1：轩辕套餐
    variables:
      - app_id: $app_Id1
      - autoArchive: True
      - businessScene: 创建自动签流程：轩辕套餐
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]


- test:
    name: 创建个人账号，轩辕套餐
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo
      - mobile: $mobile
      - name: $name
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid1: content.data.accountId

- test:
    name: 创建企业账号，轩辕套餐
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo1
      - name: $name1
      - thirdPartyUserId: $thirdPartyUserId2
      - creator: $oid1
    extract:
      - orgId1: content.data.orgId

- test:
    name: 创建个人模板印章，轩辕套餐
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id1
      - accountId: $oid1
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId1: content.data.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 创建企业模板印章，轩辕套餐
    api: api/seals/seals/create_c_template_seal_appid.yml
    variables:
      - app_id: $app_Id1
      - orgId: $orgId1
      - color: "BLUE"
      - height: 159
      - width: 159
      - alias: "企业章-蓝色"
      - type: "TEMPLATE_OVAL"
      - central: "NONE"
      - hText: "财务"
      - qText: "2019年"

    extract:
      - sealId_org1: content.data.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 获取上传文件url
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $oid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 添加流程文档，轩辕套餐
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

      #- test:
      #    name: 对接平台自动签署_模板章_轩辕
      #    variables:
      #      - app_id: $app_Id1
      #      #      - flowId: $flowId1
      #      - posBean: {'posPage':'1','posX':300, 'posY':400}
      #      - signfield1: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean,'sealId':"4a60b285-4811-405d-893d-b8d9270b9033",'signType':2}
      #      - signfields: [$signfield1]
      #    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
      #    validate:
      - eq: ["content.code",0]

- test:
    name: 对接平台自动签署_云印章平台章_轩辕
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield1: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean,'signType':2}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程1
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]