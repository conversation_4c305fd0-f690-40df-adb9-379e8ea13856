- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(base_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: ***********
      - path_pdf: 个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - idNo1: 130202199104016781
      - name1: esigntest-测试用户1
      - thirdPartyUserId1: esigntest-130202199104016781
      - idNo2: 230124200712193556
      - name2: esigntest-测试用户2
      - thirdPartyUserId2: esigntest-230124200712193556
      - idNo3: 230803201510286771
      - name3: esigntest-测试用户3
      - thirdPartyUserId3: esigntest-230803201510286771
      - client_id: pc
- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: 130202199104016781
      - name: esigntest-测试用户1
      - thirdPartyUserId: esigntest-1302021991sdfgn04016781
    api: api/user/account_third/create_account_appid.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230124200712193556
      - name: esigntest-测试用户2
      - thirdPartyUserId: esigntest-230124200712wergnvcerty1
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "用户1创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章1
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
- test:
    name: "用户2创建个人模版印章"
    variables:
      - accountId: $personOid2
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章2
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 获取文件信息
    variables:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 静默签署场景-创建静默签流程:自动归档
    variables:
      - autoArchive: true
      - businessScene: 静默签署流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-添加流程文档：文档1
    variables:
      - flowId: $flowId4
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-添加流程签署区：静默签署，账户未授权，失败
    variables:
      - flowId: $flowId4
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_data_V2(0,1,$fileId1,$flowId4,$posBean,1,$personOid2,$personOid2,$p2_sealId,1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-签署授权:个人账户2
    variables:
      - accountId: $personOid2
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-查询授权状态
    variables:
      - accountId: $personOid2
    api: api/signflow/signAuth/signAuth_get.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data",True]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-添加流程签署区：静默签署，账户授权后，签署成功
    variables:
      - flowId: $flowId4
      - posBean1: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_data_V2(0,1,$fileId1,$flowId4,$posBean1,1,$personOid2,$personOid2,$p2_sealId,1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(8)}
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(8)}
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-查询签署区：已归档
    variables:
      - accountId: $personOid2
      - flowId: $flowId4
      - signfieldIds: ""
    api: api/signflow/signfields/signfieldsSelect.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signfields.0.status",4]
      - eq: ["status_code", 200]

- test:
    name: 静默签署场景-取消签署授权:个人账户2
    variables:
      - accountId: $personOid2
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
