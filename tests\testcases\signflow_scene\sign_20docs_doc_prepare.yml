- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    ###def: sign_20docs_doc_prepare()
    base_url: ${ENV(base_url)}


- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId:
    api: api/file_template/files/get_uploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同1.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同2.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同3.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId3: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同4.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId4: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同5.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId5: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同6.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId6: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同7.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId7: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同8.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId8: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同9.pdf
      - accountId:
      - fileKey: $fileKey5
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId9: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同10.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId10: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同11.pdf
      - accountId:
      - fileKey: $fileKey6
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId11: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同12.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId12: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同13.pdf
      - accountId:
      - fileKey: $fileKey7
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId13: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同14.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId14: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同15.pdf
      - accountId:
      - fileKey: $fileKey8
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId15: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同16.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId16: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同17.pdf
      - accountId:
      - fileKey: $fileKey9
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId17: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同18.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId18: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同19.pdf
      - accountId:
      - fileKey: $fileKey9
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId19: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - fileName: 个人借贷合同20.pdf
      - accountId:
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId20: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]