- config:
    name: 账号相关的测试
    variables:
        - idNo: "513230198904164146"
        - idType: "CRED_PSN_CH_IDCARD"
        - name: "唐飞燕"
        - email: "<EMAIL>"
        - mobile: "***********"
    request:
        "base_url": ${ENV(footstone_api_url)}
        "headers":
        "Content-Type": "application/json"
- test:
    name: 账号操作，包含创建、更新、查询、注销等
    variables: 
       idNo: $idNo
       idType: $idType
       name: $name
       email: $email
       mobile: $mobile
    testcase: testcases/user/account_action.yml