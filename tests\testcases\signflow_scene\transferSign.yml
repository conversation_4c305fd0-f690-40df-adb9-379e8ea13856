- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(base_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: ***********
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - idNo1: 130202199104016781
      - name1: esigntest-测试用户1
      - thirdPartyUserId1: esigntest-130202199104016781
      - idNo2: 230124200712193556
      - name2: esigntest-测试用户2
      - thirdPartyUserId2: esigntest-230124200712193556
      - idNo3: 230803201510286771
      - name3: esigntest-测试用户3
      - thirdPartyUserId3: esigntest-230803201510286771
      - client_id: pc

- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: 130202199104016781
      - name: esigntest-测试用户1
      - thirdPartyUserId: esigntest-1302021ert99104016781
    api: api/user/account_third/create_account_appid.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230124200712193556
      - name: esigntest-测试用户2
      - thirdPartyUserId: esigntest-2301242007bgdcs12193556
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户3，只输入必填项（用户名、证件号），转签用"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230803201510286771
      - name: esigntest-测试用户3
      - thirdPartyUserId: esigntest-23080320151028ijhbvrdx6771
    extract:
      - personOid3: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "用户1创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章1
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户2创建个人模版印章"
    variables:
      - accountId: $personOid2
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章2
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户3创建个人模版印章"
    variables:
      - accountId: $personOid3
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章3
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 获取文件信息
    variables:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 转签场景-创建流程:不自动归档
    variables:
      - autoArchive: False
      - businessScene: 多人多文档
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - extend: {}
      - initiatorAccountId:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId5: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-开启流程，预期结果：失败，未添加流程文档
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",流程没有文档不允许开启流程]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-添加流程代理人
    variables:
      - flowId: $flowId5
      - recipients: [{"recipientAccountId": $personOid1,"recipientIdentityAccountId": $personOid1,"recipientIdentityAccountType":0,"roleType": 1}]
    api: api/mobile-shield/recipients.yml
    extract:
      - recipientId1: content.data.recipientIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-添加流程文档：文档1
    variables:
      - flowId: $flowId5
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-添加流程附件：附件1
    variables:
      - flowId: $flowId5
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-删除流程附件
    variables:
      - fileIds: $fileId1
      - flowId: $flowId5
    api: api/signflow/flowAttachments/attachmentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-添加流程附件：附件2
    variables:
      - flowId: $flowId5
      - attachments: [{"attachmentName": "附件2.pdf", "fileId": $fileId2}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-更新流程附件：更新附件2的名称
    variables:
      - flowId: $flowId5
      - attachments: [{"attachmentName": "附件2重命名.pdf", "fileId": $fileId2}]
    api: api/signflow/flowAttachments/attachmentsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-查询个人2印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-开启流程，预期结果：成功，不自动归档的流程允许零签署区时开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-添加流程签署区：签署人账号-个人账户2、手动签署、签约主体-个人、文档1、指定签署区-指定位置、指定个人印章ID
    variables:
      - flowId: $flowId5
      - posBean1: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId5,$posBean1,1,$personOid2,,$p2_sealId,1)}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-查询流程状态
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",1]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-流程参与人转签-将账号2转签给账号3
    variables:
      - flowId: $flowId5
      - signfieldIds: [$signfieldId]
      - transferSourceAccountId: $personOid2
      - transferTargetAccountId: $personOid3
    api: api/signflow/signfields/signfieldsTransfer.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: 转签场景-查询个人3印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid3
    extract:
      - p3_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-查询流程签署人：已变成账号3
    api: api/signflow/flowSigners/get_flow_signers.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.signers.0.signerAccountId",$personOid3]
      - len_eq: ["content.data.signers",1]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-更新流程签署区：数据补充
    variables:
      - flowId: $flowId5
      - posBean: {addSignTime: False, key: '', posPage: '1', posX: 0, posY: 600, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_update_data($personOid3, , $posBean, ,$p3_sealId,$signfieldId)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-执行流程签署区
    variables:
      - signfieldIds: [$signfieldId]
      - flowId: $flowId5
      - accountId: $personOid3
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 转签场景-查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]
      - eq: ["status_code", 200]

      #- test:
      #    name: 转签场景-查询操作日志，包含转签
      #    api: api/signflow/flowLogs/logsSelect.yml
      #    variables:
      #      - jsonKey: logType
      #      - jsonValue: 24
      #      - flowId: $flowId5
      #      - pageNum: ""
      #      - pageSize: ""
      #      - type: ""
      #    extract:
      #      - logs: content.data.logs
      #    validate:
#      - eq: ["${containItems($logs,$jsonValue,$jsonKey)}",True]
