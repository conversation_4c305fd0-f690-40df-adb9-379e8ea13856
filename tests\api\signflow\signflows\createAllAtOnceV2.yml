validate:
  - eq: [status_code, 200]
request:
  url: /v2/signflows/createAllAtOnce
  method: POST
  headers: ${get_headers($app_id)}

  json:
    { createWay: $createWay,
      accountId: $signerAccountId,
      businessScene: $businessScene,
      attachments: [{accountId: $signerAccountId ,attachmentName: , bizFileId: $fileId, fileId: $fileId, operatorAccountId: $signerAccountId,status: 0 }  ],
      docs: [
      {
        encryption: 0,
        fileId: $fileId,
        fileName: "22",
        source: 0
      }
      ],
      recipients: [ {recipientAccount: "",recipientAccountId: $signerAccountId,recipientIdentityAccountId: $signerAccountId,recipientIdentityAccountName: "",recipientIdentityAccountTyp: 0, recipientName: "",recipientNickname: "", roleType: 0 }],
      signers: [
      {
        signOrder: 1,
        seperateSigner: $seperateSigner,
        signerAccounts: [
        {

          signerAccountId: $signerAccountId,
          signerAccountName: $name,
          signerAuthorizerId: $signerAuthorizerId,
          signerAuthorizerName: $name_org,
          signerNickname: $nickname,
          signerAuthorizerType: $signerAuthorizerType,
          signerSignRoles: $signerSignRoles
        }
        ],
        signfields: [
        {
          autoExecute: false,
          fileId: $fileId,
          posBean: {
            addSignTime: true,
            posPage: "1",
            posX: 110,
            posY: 110,
            qrcodeSign: false,
            signTimeFormat: "yyyy-mmmm-dddd",
            width: 150
          },
          signType: 1,
          signerRoleType: "0",
          thirdOrderNo: "111"
        }
        ]
      }

      ]}

#一步更新流程
