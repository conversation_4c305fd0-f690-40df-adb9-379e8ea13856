- config:
    name: 大文件签署-手动签
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - bigfile30M: ${ENV(bigfile30M_id_in_app_id_tengqing_SaaS_with_willing)}
      - bigfile22M: ${ENV(bigfile22M_id_in_app_id_tengqing_SaaS_with_willing)}
      - file_small: ${ENV(file_id_in_app_id_tengqing_SaaS_with_willing)}
      - liangxianhong_stan: ${ENV(orgdy_legal_oid)}
      - yuzan_stan: ${ENV(oid_wang_tsign)}
      - account_id_sx_tsign: ${ENV(account_id_sx_tsign)}
      - account_id_dy_tsign: ${ENV(orgid_dy)}
      - account_id_1_tsign: ${ENV(account_id1_in_tsign)}
#      - ganning_saas_oid: ${ENV(gn_oid_tsign)}
      - sealId_dy_approval: ${ENV(sealId_dy_approval)}
      - sealId_dy_noapproval: ${ENV(sealId_dy_noapproval)}
      - sealId_zl: ${ENV(sealId_zl)}
      - sealId_gn: ${ENV(sealId_gn)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - name_dy: ${ENV(name_dy)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}


- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: fhg123456ws
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId

- test:
    name: "创建甘宁账号"
    variables:
      - thirdPartyUserId: z111zzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司"
    variables:
      - thirdPartyUserId: don11321231wer
      - creator: $accountId_gan
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId




- test:
    name: "创建流程-顺序1同一签署人大小文件无审批走异步 & 顺序2同一签署人大小文件有审批走同步 & 顺序3不同签署人同步异步签"
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 创建流程-顺序1同一签署人大小文件无审批走异步 & 顺序2同一签署人大小文件有审批走同步 & 顺序3不同签署人同步异步签
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId_ckl
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档 - 1个小文件&2个大文件
    variables:
      - doc1: {encryption: 0,fileId: $file_small, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $bigfile30M, fileName: "30M.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $bigfile22M, fileName: "22M.pdf", source: 0}

      - docs: [$doc1,$doc2,$doc3]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "顺序1-大文件-个人-陈凯丽-指定位置"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_1_big: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "顺序1-大文件-个人-陈凯丽-指定位置-自由签署区"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":400},"sealType":null,"sealId":"","signType":0,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_1_big_free: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "顺序1-小文件-个人-陈凯丽"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$file_small,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_1_small: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]












- test:
    name: "顺序2-小文件-个人-陈凯丽"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$file_small,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"2","posX":100,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_2_small: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "顺序2-大文件1-企业-签署人陈凯丽"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId":$dongying_organize_id,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"3","posX":200,"posY":400},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_2_big_org_approval: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "顺序2-大文件2-企业-签署人陈凯丽"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$bigfile22M,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId":$dongying_organize_id,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"3","posX":200,"posY":100},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_2_big_org_noapproval: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]









- test:
    name: "顺序3-大文件-个人-陈凯丽"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":3,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_3_big: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: "顺序3-小文件-个人-甘宁"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$file_small,"signerAccountId": $accountId_gan,"actorIndentityType":"0","authorizedAccountId":$accountId_gan,"assignedPosbean":true,"order":3,"posBean":{"addSignTime":true,"posPage":"1","posX":333,"posY":321},"sealType":null,"sealId":"","signType":1,"sealIds":[]}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId_3_small: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 开启流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 查询流程详情
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $sign_flowId1
      - queryAccountId: $accountId_gan
      - operatorid: $accountId_gan
    extract:
      - flow_name: content.data.businessScene
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]



- test:
    name: "V1获取H5详情页链接 - urlType传2，clientType不传，发起人获取多主体链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - urlType: 2
      - multiSubject: true
      - organizeId: 0
      - compressContext: true
      - clientType: PC
    api: api/flow-manager/V1_get_flow_executeUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - contains: ["content.data.url","context"]
      - contains: ["content.data.shortUrl","sign"]
      - len_gt: ["content.data.shortUrl",20]

#顺序1签署
- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $accountId_ckl
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $accountId_ckl
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 顺序1签署区执行签署
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield1: {"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealId_zl,"signType":1,"signfieldId": $signfieldId_1_big}
      - updateSignfield2: {"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealId_zl,"signType":1,"signfieldId": $signfieldId_1_big_free}
      - updateSignfield3: {"fileId":$file_small,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":1,"posBean":$posBean,"sealType":"","sealId":$sealId_zl,"signType":1,"signfieldId": $signfieldId_1_small}
      - addSignfields: []
      - updateSignfields: [$updateSignfield1,$updateSignfield2,$updateSignfield3]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $accountId_ckl
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: ["content.data.serviceId",1]
    extract:
      - serviceId: content.data.serviceId
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}



#顺序2签署
- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $accountId_ckl
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $accountId_ckl
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 顺序2签署区执行签署
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield1: {"fileId":$file_small,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":2,"posBean":$posBean,"sealType":"","sealId":$sealId_zl,"signType":1,"signfieldId": $signfieldId_2_small}
      - updateSignfield2: {"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId":$account_id_dy_tsign,"assignedPosbean":true,"order":2,"posBean":$posBean,"sealType":"","sealId":$sealId_dy_approval,"signType":1,"signfieldId": $signfieldId_2_big_org_approval}
      - updateSignfield3: {"fileId":$bigfile22M,"signerAccountId":$accountId_ckl,"actorIndentityType":"2","authorizedAccountId":$account_id_dy_tsign,"assignedPosbean":true,"order":2,"posBean":$posBean,"sealType":"","sealId":$sealId_dy_noapproval,"signType":1,"signfieldId": $signfieldId_2_big_org_noapproval}
      - addSignfields: []
      - updateSignfields: [$updateSignfield1,$updateSignfield2,$updateSignfield3]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $accountId_ckl
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: ["content.data.serviceId",1]
    extract:
      - serviceId: content.data.serviceId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}


#查询审批人审批列表
- test:
    name: "审批列表 - 查询"
    variables:
      - operator: $yuzan_stan
      - space_orgId: $account_id_dy_tsign
      - json: {
        "approvalId": "",
        "approvalName": "",
        "approvalStatus": [],
        "approvalType": 1,
        "initiatorName": "",
        "queryType": 3,
        "participantName": "",
        "pageNum": 1,
        "pageSize": 10
      }
    api: api/seals/approvalflows/approvalflows_list_v2.yml
    extract:
      - batchId: content.data.approvals.0.batchId
      - approveFlowId4: content.data.approvals.0.approvalId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]




- test:
    name: 同意审批-获取审批人意愿地址
    api: api/signflow/signflows/identifyUrlWillWithJson.yml
    variables:
      - accountId: $yuzan_stan
      - approvalId: $approveFlowId4
      - json: {
        "bizCtxIds":
          [
            $sign_flowId1     #flowid
          ],
        "bizType": "",
        "redirectUrl": "www.baidu.com",
        "clientType": "PC",
        "wukongOid": $yuzan_stan,
        "app_id":  $app_id,
        "space": $yuzan_stan,
        "scene": "2",
        "willTypeHided": "5"
      }
    #    teardown_hooks:
    #      - ${hook_sleep_n_secs(5)}
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 意愿认证-短信验证码发送
    variables:
      - accountId: $yuzan_stan
      - bizId: $bizId
      - approvalId: $approveFlowId4
      - bizType:  "SIGN"
      - sendType: "SMS"
    api: api/realname/realname/willCreateCodeAuth.yml
    extract:
      - willAuthId3: content.data.willAuthId
    #    teardown_hooks:
    #      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authCode: 123456
      - accountId: $yuzan_stan
      - willAuthId: $willAuthId3
      - bizId: $bizId
      - bizType:  "SIGN"
    api: api/realname/realname/checkVerCodeModel.yml
    #    teardown_hooks:
    #      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 同意审批
    variables:
      - approvalId: $approveFlowId4
      - accountId: $yuzan_stan
      - securityCode: ""
    api: api/signflow_approval/approval/agreeApproval.yml
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#顺序3的签署区进行签署
- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $accountId_ckl
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $accountId_ckl
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 顺序3签署区执行签署-钟灵-走异步
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_ckl
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield1: {"fileId":$bigfile30M,"signerAccountId":$accountId_ckl,"actorIndentityType":"0","authorizedAccountId":$accountId_ckl,"assignedPosbean":true,"order":3,"posBean":$posBean,"sealType":"","sealId":$sealId_zl,"signType":1,"signfieldId": $signfieldId_3_big}
      - addSignfields: []
      - updateSignfields: [$updateSignfield1]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $accountId_ckl
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: ["content.data.serviceId",1]
    extract:
      - serviceId: content.data.serviceId




- test:
    name: 意愿认证-发送验证码
    api: api/iterate_cases/sendSms.yml
    variables:
      - accountId: $accountId_gan
      - flowId: $sign_flowId1
    extract:
      - bizId: content.data.bizId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $accountId_gan
      - bizId: $bizId
    api: api/iterate_cases/checkSms.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序3签署区执行签署-甘宁同步签-第1次签-调同步签接口-同步异步并发-报错
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_gan
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield1: {"fileId":$file_small,"signerAccountId":$accountId_gan,"actorIndentityType":"0","authorizedAccountId":$accountId_gan,"assignedPosbean":true,"order":3,"posBean":$posBean,"sealType":"","sealId":$sealId_gn,"signType":0,"signfieldId": $signfieldId_3_small}
      - addSignfields: []
      - updateSignfields: [$updateSignfield1]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - eq: ["content.data.signfieldSignResult.0.optResult", 1]
      - eq: ["content.data.signfieldSignResult.0.failedReason", '签署失败:当前流程已有签署任务正在执行，请耐心等待完成后再次签署']


- test:
    setup_hooks:
      - ${sleep_N_secs(3)}
    name: 顺序3签署区执行签署-甘宁同步签-第2次签-调异步签接口-先等待异步签署完成
    variables:
      - flowId: $sign_flowId1
      - accountId: $accountId_gan
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - updateSignfield1: {"fileId":$file_small,"signerAccountId":$accountId_gan,"actorIndentityType":"0","authorizedAccountId":$accountId_gan,"assignedPosbean":true,"order":3,"posBean":$posBean,"sealType":"","sealId":$sealId_gn,"signType":1,"signfieldId": $signfieldId_3_small}
      - addSignfields: []
      - updateSignfields: [$updateSignfield1]
      - signfieldIds: []
      - dingUser: {}
      - approvalPolicy: 0
      - operator_id: $accountId_gan
    api: api/signflow/aggregate_sign/addUpdateExecute_sign_async.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]
      - len_gt: ["content.data.serviceId",1]
    extract:
      - serviceId: content.data.serviceId


- test:
    setup_hooks:
      - ${sleep_N_secs(3)}
    name: 归档流程
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","成功"]