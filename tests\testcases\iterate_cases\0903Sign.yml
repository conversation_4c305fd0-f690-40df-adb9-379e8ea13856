- config:
    name: "0903迭代测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - app_id: ${ENV(wukong_no_forcewill)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf
      - group: ''
      - m1: ***********
      - pOid1: ${getOid($m1)}
      - language: zh-US
      - app_Id1: ${ENV(wukong_forcewill)}
      - random_16: ${get_randomNo_16()}
      - random_32: ${get_randomNo_32()}
      - random_letter: ${random_letter()}
      - random_str: ${random_str()}

- test:
    name: "创建签署人账号"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_1
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId: content.data.accountId

- test:
    name: "创建机构-东营伟信建筑安装工程有限公司"
    variables:
      - thirdPartyUserId: dongying2011211
      - creator: $accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying_organize_id: content.data.orgId



#上传无电子签名文件
- test:
    name: "文件直传创建一个无电子签名的文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:

      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]



#上传有电子签名无篡改的文件
- test:
    name: "文件直传创建一个有电子签名无篡改的文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf2)}
      - contentType: application/pdf
      - fileName: 有签名无篡改.pdf
      - fileSize: ${get_file_size($path_pdf2)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:

      - contentMd5: ${get_file_base64_md5($path_pdf2)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf2)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]

      - eq: ["content.msg", "成功"]



#上传有电子签名且被篡改的文件
- test:
    name: "文件直传创建一个有电子签名且被篡改的文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf3)}
      - contentType: application/pdf
      - fileName: 被篡改的文档1.pdf
      - fileSize: ${get_file_size($path_pdf3)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId3: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:

      - contentMd5: ${get_file_base64_md5($path_pdf3)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf3)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]

      - eq: ["content.msg", "成功"]



#上传附件
- test:
    name: "文件直传创建一个附件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf4)}
      - contentType: application/pdf
      - fileName: 附件2.pdf
      - fileSize: ${get_file_size($path_pdf4)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId4: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:

      - contentMd5: ${get_file_base64_md5($path_pdf4)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf4)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]

      - eq: ["content.msg", "成功"]




#创建流程1，再用/v1/signflows/{flowId}/documents接口添加混合的多文档
- test:
    name: 创建流程
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档：多文档
    variables:

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}

      - docs: [$doc1,$doc2,$doc3]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

#创建流程2，再用/v1/signflows/{flowId}/documents接口添加文档 - 无电子签名文档
- test:
    name: 创建流程
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档：无电子签名
    variables:

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - docs: [$doc1]
      - flowId: $sign_flowId2
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



#创建流程3，再用/v1/signflows/{flowId}/documents接口添加文档 - 有电子签名无篡改
- test:
    name: 创建流程
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档：有电子签名无篡改
    variables:

      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}

      - docs: [$doc2]
      - flowId: $sign_flowId3
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#创建流程4，再用/v1/signflows/{flowId}/documents接口添加文档 - 有电子签名有篡改
- test:
    name: 创建流程
    variables:
      - extend:
      - autoArchive: False
      - businessScene: 创建流程
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId: $accountId
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - sign_flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加流程文档：有电子签名有篡改
    variables:

      - doc2: {encryption: 0,fileId: $fileId3, fileName: "有签名无篡改.pdf", source: 0}

      - docs: [$doc2]
      - flowId: $sign_flowId4
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


#非标合规二期case
- test:
    name: 一步发起：发起企业授权免意愿-失败
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,agreeSkipWillingness: true, signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $dongying_organize_id}, signfields: [  { autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 签署主体非个人时，不可设置agreeSkipWillingness"]

- test:
    name: 一步发起：发起企业静默授权-失败
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,agreeAutoSign: true, signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $dongying_organize_id}, signfields: [  { autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", '参数错误: 签署主体非个人时，不可设置agreeAutoSign']


- test:
    name: 一步发起：发起个人静默授权-成功
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,agreeAutoSign: true, signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId_shouquan: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 一步发起：发起个人授权免意愿-成功
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: false, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,agreeSkipWillingness: true, signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flowId_mianyiyuan: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 分布追加发起：个人授权免意愿-成功
    variables:
      - flowId: $sign_flowId_mianyiyuan
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId1, agreeSkipWillingness: true,'signerAccountId': $accountId, 'actorIndentityType':'0', 'authorizedAccountId':$accountId,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 分布追加发起：企业授权免意愿-失败
    variables:
      - flowId: $sign_flowId_mianyiyuan
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId1, agreeSkipWillingness: true,'signerAccountId': $accountId, 'actorIndentityType':'2', 'authorizedAccountId':$dongying_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 签署主体非个人时，不可设置agreeSkipWillingness"]


- test:
    name: 分布追加发起：个人静默授权-失败
    variables:
      - flowId: $sign_flowId_mianyiyuan
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId1, agreeAutoSign: true,'signerAccountId': $accountId, 'actorIndentityType':'0', 'authorizedAccountId':$accountId,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 该签署方已经设置需要授权静默签/免意愿签，无法再设置另一种"]

- test:
    name: 分布追加发起：企业静默授权-失败
    variables:
      - flowId: $sign_flowId_mianyiyuan
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield_file1: { 'fileId':$fileId1, agreeAutoSign: true,'signerAccountId': $accountId, 'actorIndentityType':'2', 'authorizedAccountId':$dongying_organize_id,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','sealId':"",'signType':1}
      - signfields: [$signfield_file1]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: 签署主体非个人时，不可设置agreeAutoSign"]


- test:
    name: 一步发起：同时发起个人授权免意愿+静默授权-失败
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,agreeSkipWillingness: true,agreeAutoSign: true, signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message", "参数错误: agreeSkipWillingness和agreeAutoSign不可同时设置为true"]







#/api/v2/signflows/createFlowOneStep，添加混合文档
- test:
    name: 一步发起：添加文档list、附件
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc1,$doc2,$doc3]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]



#/api/v2/signflows/createFlowOneStep，添加文档 - 无电子签名
- test:
    name: 一步发起：添加无电子签名文档、附件
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}

      - attachments: [$attachment1]
      - docs: [$doc1]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]

#/api/v2/signflows/createFlowOneStep，添加文档 - 有电子签名无篡改
- test:
    name: 一步发起：添加有电子签名无篡改文档、附件
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}


      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}

      - attachments: [$attachment1]
      - docs: [$doc2]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId2,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]


#/api/v2/signflows/createFlowOneStep，添加文档 - 有电子签名有篡改
- test:
    name: 一步发起：添加有电子签名有篡改文档、附件
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}


      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - attachments: [$attachment1]
      - docs: [$doc3]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: false, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $accountId,authorizedAccountId: $accountId}, signfields: [  { autoExecute: false, actorIndentityType: 0, fileId: $fileId3,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 0,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]









#/v2/signflows/createAllAtOnce，添加文档 -混合
- test:
    name: "一步到位创建流程-对内-添加文档list"
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}
      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - json: {"accountId":$accountId,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[$attachment1],"docs":[$doc1,$doc2,$doc3],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$accountId,"signerAccountName":"","signerAuthorizerId":$accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":0,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId2: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]


#/v2/signflows/createAllAtOnce，添加文档 -无电子签名
- test:
    name: "一步到位创建流程-对内-添加文档-无电子签名、附件"
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}

      - doc1: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}

      - json: {"accountId":$accountId,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[$attachment1],"docs":[$doc1],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$accountId,"signerAccountName":"","signerAuthorizerId":$accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId1,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":0,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId2: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]


#/v2/signflows/createAllAtOnce，添加文档 -有电子签名无篡改
- test:
    name: "一步到位创建流程-对内-添加文档-有电子签名无篡改、附件"
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}


      - doc2: {encryption: 0,fileId: $fileId2, fileName: "有签名无篡改.pdf", source: 0}

      - json: {"accountId":$accountId,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[$attachment1],"docs":[$doc2],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$accountId,"signerAccountName":"","signerAuthorizerId":$accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId2,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":0,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId2: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]


#/v2/signflows/createAllAtOnce，添加文档 -有电子签名有篡改
- test:
    name: "一步到位创建流程-对内-添加文档-有电子签名有篡改、附件"
    variables:
      - attachment1: {fileId: $fileId4, attachmentName: "附件2.pdf"}


      - doc3: {encryption: 0,fileId: $fileId3, fileName: "被篡改的文档1.pdf", source: 0}
      - json: {"accountId":$accountId,"createWay":"normal","autoInitiate":false,"autoArchive":true,"businessScene":"签署任务","configInfo":{"noticeType":"1,2,3,4","signPlatform":"1,2,3","redirectUrl":"","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}"},"attachments":[$attachment1],"docs":[$doc3],"signValidity":"","contractValidity":"","signers":[{"seperateSigner":false,"signOrder":1,"signerAccounts":[{"signerAuthorizerType":1,"signerSignRoles":"2","signerAccount":"","signerAccountId":$accountId,"signerAccountName":"","signerAuthorizerId":$accountId,"signerAuthorizerName":"","signerNickname":""}],"signerRoleLabel":"甲方","signfields":[{"fileId":$fileId3,"autoExecute":false,"assignedPosbean":true,"assignedSeal":false,"posBean":{"signDateBean":null,"signDateBeanType":0,"keyword":"","addSignTime":false,"signTimeFormat":"","posPage":1,"posX":100,"posY":200,"qrcodeSign":false},"sealFileKey":"","sealId":"","sealType":"1","signType":0,"signerRoleType":"0","thirdOrderNo":""}]}],"recipients":[]}
    extract:
      - sign_flowId2: content.data.flowIds.0
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]










































