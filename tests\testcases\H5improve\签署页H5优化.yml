- config:
    name: 签署页H5优化
    base_url: ${ENV(footstone_api_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - psn_id_1: ${ENV(account_id1_in_tsign)}
      - file_id_1: ${ENV(file_id_in_app_id_tengqing_SaaS_with_willing)}
      - org_id_1: ${ENV(orgid_dy)}
      - org_id_2: ${ENV(orgid_sx)}

- test:
    name: 创建流程
    api: api/signflow/v3/signflows.yml
    variables:
      - json: {"autoArchive":true,"autoInitiate":"true","businessScene":"签署页H5优化","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,5,6,7,11"},"notifyConfig":{"noticeType":"1,2","sendNotice":true}},"initiatorAccountId":"${psn_id_1}","initiatorAuthorizedAccountId":"${psn_id_1}"}
    extract:
      - flowId: content.data.flowId

- test:
    name: 添加文档
    api: api/iterate_cases/addFlowDoc.yml
    variables:
      - docs: [{"fileId":"${file_id_1}"}]
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]

- test:
    name: 添加签名域
    api: api/signflow/v3/handSign.yml
    variables:
      - json: {"signfields":[{"actorIndentityType":"2","authorizedAccountId":"${org_id_2}","fieldType":0,"fileId":"${file_id_1}","forceReadTime":0,"handDrawnWay":"0","order":1,"posBean":{"posPage":"1","posX":"100","posY":"100"},"sealBizTypes":"","sealId":"","sealType":"1","signDateBeanType":"1","signType":1,"signerAccountId":"${psn_id_1}"},{"actorIndentityType":"2","authorizedAccountId":"${org_id_2}","fieldType":0,"fileId":"${file_id_1}","forceReadTime":0,"handDrawnWay":"0","order":2,"posBean":{"posPage":"2","posX":"100","posY":"100"},"sealBizTypes":"","sealId":"","sealType":"1","signDateBeanType":"1","signType":1,"signerAccountId":"${psn_id_1}"}]}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]