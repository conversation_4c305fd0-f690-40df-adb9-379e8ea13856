#!/usr/bin/env python
# -- coding: utf-8 --
# <AUTHOR> gan
# @File : hrunnerToJson.py
import json


def toJson(stra):
    cont = stra.replace("'",'"').replace("False","false").replace("True","true").replace("\n","").replace("None","null")
    print(cont)
    jsonD = json.loads(cont)
    res = json.dumps(jsonD, sort_keys=True, indent=4, separators=(', ', ': '),ensure_ascii=False)
    print(res)
if __name__ == '__main__':
    inr = """ {"attachments": [{"fileId": "71b6bed61abe431da5dc3e5dec6c7e39", "attachmentName": "\\u9644\\u4ef62.pdf"}], "docs": [{"encryption": 0, "fileId": "a5535ac9b07e49029c8cae612fc54cc0", "fileName": "\\u9644\\u4ef61", 
"source": 0}], "copiers": [{"copierAccountId": "3986d7544d9848f186ee9c5dcd342de1", "copierIdentityAccountId": "5e8899b894954a83af3dfd9924a10d32", "copierIdentityAccountType": 1}], "flowInfo": {"initiatorAccountId": "3986d7544d9848f1
86ee9c5dcd342de1", "initiatorAuthorizedAccountId": "09d83d8687b74df292862c0cd8cc2e98", "autoArchive": true, "autoInitiate": true, "businessScene": "********\\u9644\\u4ef6\\u4e0b\\u8f7d", "flowConfigInfo": {"batchDropSeal": true, "no
ticeDeveloperUrl": "http://datafactory.smlk8s.esign.cn/simpleTools/notice/", "noticeType": "1,2,3", "signPlatform": "1,2"}}, "signers": [{"signOrder": 1, "signerAccount": {"signerAccountId": "3986d7544d9848f186ee9c5dcd342de1", "auth
orizedAccountId": "8fbe5926547e40149978fb8c5a448394"}, "signfields": [{"autoExecute": false, "actorIndentityType": 2, "signerRoleType": "2", "certId": "", "fileId": "a5535ac9b07e49029c8cae612fc54cc0", "sealType": "", "posBean": {"po
sPage": "1", "posX": 300, "posY": 300}, "signDateBeanType": "2", "signDateBean": {"fontSize": 50, "posX": 220, "posY": 250, "posPage": "1"}, "signType": 1}, {"signOrder": 1, "signerAccount": {"signerAccountId": "0aa8640685874e65b17c
bc202f91c3c4", "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f"}, "signfields": [{"autoExecute": false, "actorIndentityType": 2, "signerRoleType": "2", "certId": "", "fileId": "a5535ac9b07e49029c8cae612fc54cc0", "sealType":
 "", "posBean": {"posPage": "1", "posX": 600, "posY": 600}, "signDateBeanType": "2", "signDateBean": {"fontSize": 150, "posX": 120, "posY": 150, "posPage": "1"}, "signType": 1}], "thirdOrderNo": "\\u4f01\\u4e1a2"}], "thirdOrderNo": 
"\\u4f01\\u4e1a2"}]}
"""
    toJson(inr)