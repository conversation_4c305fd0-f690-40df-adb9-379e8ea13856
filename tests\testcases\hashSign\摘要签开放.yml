- config:
    name: "摘要签开放api"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - group: ${ENV(envCode)}
      - psn_id_1: ${ENV(account_id1_in_tsign)}
      - psn_id_2: ${ENV(account_id2_in_tsign)}
      - psn_id_4: ${ENV(account_id4_in_tsign)}
      - org_id_1: ${ENV(orgid_dy)}
      - org_id_2: ${ENV(orgid_sx)}

- test:
    name: "获取个人印章列表-1"
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $psn_id_1
    extract:
      - psn_seal: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取个人印章列表-2"
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $psn_id_2
    extract:
      - psn_seal_2: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取个人印章列表-4"
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $psn_id_4
    extract:
      - psn_seal_4: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "获取企业印章列表"
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $org_id_1
    extract:
      - org_seal: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 获取意愿验证码-用户1
    variables:
      - app_id: ${app_id}
      - accountId: $psn_id_1
      - bizId: ""
      - bizType:  "SIGN"
      - sendType: "SMS"
    api: api/realname/realname/willCreateCodeAuth.yml
    extract:
      - bizId1: content.data.bizId
      - willAuthId1: content.data.willAuthId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 校验意愿验证码-用户1
    variables:
      - app_id: ${app_id}
      - accountId: $psn_id_1
      - bizId: $bizId1
      - bizType:  "SIGN"
      - sendType: "SMS"
      - authCode: "123456"
      - willAuthId: ${willAuthId1}
    api: api/realname/realname/checkVerCodeModel.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取意愿验证码-用户2
    variables:
      - app_id: ${app_id}
      - accountId: $psn_id_2
      - bizId: ""
      - bizType:  "SIGN"
      - sendType: "SMS"
    api: api/realname/realname/willCreateCodeAuth.yml
    extract:
      - bizId2: content.data.bizId
      - willAuthId2: content.data.willAuthId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 校验意愿验证码-用户2
    variables:
      - app_id: ${app_id}
      - accountId: $psn_id_2
      - bizId: $bizId2
      - bizType:  "SIGN"
      - sendType: "SMS"
      - authCode: "123456"
      - willAuthId: ${willAuthId2}
    api: api/realname/realname/checkVerCodeModel.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 获取意愿验证码-用户4
    variables:
      - app_id: ${app_id}
      - accountId: $psn_id_4
      - bizId: ""
      - bizType:  "SIGN"
      - sendType: "SMS"
    api: api/realname/realname/willCreateCodeAuth.yml
    extract:
      - bizId4: content.data.bizId
      - willAuthId4: content.data.willAuthId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 校验意愿验证码-用户4
    variables:
      - app_id: ${app_id}
      - accountId: $psn_id_4
      - bizId: $bizId4
      - bizType:  "SIGN"
      - sendType: "SMS"
      - authCode: "123456"
      - willAuthId: ${willAuthId4}
    api: api/realname/realname/checkVerCodeModel.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 摘要签-willingnessBizId为空
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signAlgorithm":1,"signerId":"${psn_id_1}","willingnessBizId":""}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",意愿认证凭证不能为空]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-操作人和签署人为不同个人
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_2}","sealId":"${psn_seal}","signAlgorithm":1,"signerId":"${psn_id_1}","willingnessBizId":"${bizId2}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",印章无权限]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-无操作人
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","sealId":"${psn_seal}","signAlgorithm":1,"signerId":"${psn_id_2}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",操作人ID不能为空]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-无签署人
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signAlgorithm":1,"willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签署方ID不能为空]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-无印章
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","signAlgorithm":1,"signerId":"${psn_id_2}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",印章id不能为空]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-印章非当前操作人
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal_2}","signerId":"${psn_id_1}","signAlgorithm":1,"willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",印章无权限]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-操作人为企业
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${org_id_1}","sealId":"${psn_seal}","signAlgorithm":1,"signerId":"${psn_id_2}","willingnessBizId":"${bizId2}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",operatorId非个人账号]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-印章非当前企业所有
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signAlgorithm":1,"signerId":"${org_id_1}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",印章无权限]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-意愿认证失败
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signAlgorithm":1,"signerId":"${psn_id_1}","willingnessBizId":"1111"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",意愿认证失败]
      - eq: ["content.code",1436202]

- test:
    name: 摘要签-意愿认证成功-算法传3
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signAlgorithm":3,"signerId":"${psn_id_1}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","signAlgorithm只支持1, 2"]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-意愿认证成功-算法没传
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signerId":"${psn_id_1}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",签名算法不能为空]
      - eq: ["content.code",1435002]

- test:
    name: 摘要签-意愿认证成功-RSA算法-个人
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signAlgorithm":1,"signerId":"${psn_id_1}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    extract:
      - signResult: content.data.signature
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 摘要签-意愿认证成功-SM2算法-个人
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${psn_seal}","signAlgorithm":2,"signerId":"${psn_id_1}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    extract:
      - signResult: content.data.signature
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 摘要签-意愿认证成功-RSA算法-企业
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${org_seal}","signAlgorithm":1,"signerId":"${org_id_1}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    extract:
      - signResult: content.data.signature
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 摘要签-意愿认证成功-SM2算法-企业
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_1}","sealId":"${org_seal}","signAlgorithm":2,"signerId":"${org_id_1}","willingnessBizId":"${bizId1}"}
    api: api/v3/file_hash_sign.yml
    extract:
      - signResult: content.data.signature
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["content.code",0]

- test:
    name: 摘要签-未做授权
    variables:
      - json: {"hashBase64":"5oiR5piv5aSn5biF5ZOl","operatorId":"${psn_id_4}","sealId":"${psn_seal_4}","signAlgorithm":2,"signerId":"${psn_id_4}","willingnessBizId":"${bizId4}"}
    api: api/v3/file_hash_sign.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",未授权]
      - eq: ["content.code",1436116]