- config:
    name: 用户自动签校验
    base_url: ${ENV(footstone_api_url)}
    variables:
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_Id1: ${ENV(appId_xuanyuan)}
      - app_Id2: ${ENV(appId_wukong)}
      - path_pdf: 个人借贷合同.pdf
      - mobile: "***********"
      - name: 梁贤红
      - idNo: 331082199211223080
      - thirdPartyUserId1: a${get_randomString()}
      - thirdPartyUserId2: b${get_randomString()}
      - mobile: "***********"
      - name1: esigntest东营伟信建筑安装工程有限公司
      - idNo1: 91370502060407048H
      - fileId1: ${get_file_id($app_Id1,$path_pdf)}
      - fileId2: ${get_file_id($app_Id2,$path_pdf)}

- test:
    name: 创建流程：轩辕套餐
    variables:
      - app_id: $app_Id1
      - autoArchive: False
      - businessScene: 创建流程：轩辕套餐
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: 创建账号，轩辕套餐
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo
      - mobile: $mobile
      - name: $name
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid1: content.data.accountId


- test:
    name: 创建企业账号，轩辕套餐
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id1
      - idNo: $idNo1
      - name: $name1
      - thirdPartyUserId: $thirdPartyUserId2
      - creator: $oid1
    extract:
      - orgId1: content.data.orgId

- test:
    name: 创建个人模板印章，轩辕套餐
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id1
      - accountId: $oid1
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]


- test:
    name: 创建企业模板印章，轩辕套餐
    api: api/seals/seals/create_c_template_seal_appid.yml
    variables:
      - app_id: $app_Id1
      - orgId: $orgId1
      - color: "BLUE"
      - height: 159
      - width: 159
      - alias: "企业章-蓝色"
      - type: "TEMPLATE_OVAL"
      - central: "NONE"
      - hText: "财务"
      - qText: "2019年"

    extract:
      - sealId_org1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]


- test:
    name: 添加流程文档，轩辕套餐
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 用户自动签署，轩辕套餐
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield: {'fileId':$fileId1, 'authorizedAccountId':$oid1, 'order':1,  'posBean':$posBean, 'sealId':$sealId1,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["status_code", 200]
      - eq: ["content.message","参数错误: 不支持自动签署"]

- test:
    name: 对接平台自动签署_轩辕
    variables:
      - app_id: $app_Id1
      - flowId: $flowId1
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield1: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean,'signType':2}
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 创建流程：非轩辕套餐
    variables:
      - app_id: $app_Id2
      - autoArchive: False
      - businessScene: 创建流程：轩辕套餐
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: 创建账号，非轩辕套餐
    api: api/user/account_third/create_account_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: $idNo
      - mobile: $mobile
      - name: $name
      - thirdPartyUserId: $thirdPartyUserId1
    extract:
      - oid2: content.data.accountId


- test:
    name: 创建企业账号，非轩辕套餐
    api: api/user/account_third/create_org_appid.yml
    variables:
      - app_id: $app_Id2
      - idNo: $idNo1
      - name: $name1
      - thirdPartyUserId: $thirdPartyUserId2
      - creator: $oid2
    extract:
      - orgId2: content.data.orgId

- test:
    name: 创建个人模板印章，非轩辕套餐
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - app_id: $app_Id2
      - accountId: $oid2
      - color: "BLUE"
      - height: 136
      - width: 136
      - alias: "蓝色印章"
      - type: "BORDERLESS"
    extract:
      - sealId2: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]


- test:
    name: 创建企业模板印章，非轩辕套餐
    api: api/seals/seals/create_c_template_seal_appid.yml
    variables:
      - app_id: $app_Id2
      - orgId: $orgId2
      - color: "BLUE"
      - height: 159
      - width: 159
      - alias: "企业章-蓝色"
      - type: "TEMPLATE_OVAL"
      - central: "NONE"
      - hText: "财务"
      - qText: "2019年"

    extract:
      - sealId_org2: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]


- test:
    name: 添加流程文档，非轩辕套餐
    variables:
      - flowId: $flowId2
      - app_id: $app_Id2
      - doc1: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]


- test:
    name: 静默授权，非轩辕套餐
    variables:
      - app_id: $app_Id2
      - accountId: $oid2
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 用户自动签署，非轩辕套餐-指定日期字体corpos
    variables:
      - flowId: $flowId2
      - app_id: $app_Id2
      - posBean: {'posPage':'1','posX':300, 'posY':400,"addSignTime":true,"signDateBean": {"fontName":"corpos","fontSize": 120,"format": "YYYY-MM-DD hh-mm-ss"}}
      - signfield: {'fileId':$fileId2, 'authorizedAccountId':$oid2, 'order':1,  'posBean':$posBean, 'sealId':$sealId2,'signType':1}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
      - eq: ["content.message","成功"]

- test:
    name: 对接平台自动签署_悟空-指定日期字体simsun
    variables:
      - flowId: $flowId2
      - posBean: {'posPage':'1','posX':300, 'posY':400,"addSignTime":true,"signDateBean": {"fontName":"simsun","fontSize": 120,"format": "YYYY-MM-DD hh-mm-ss"}}
      - signfield1: { 'fileId':$fileId2, 'order':1,  'posBean':$posBean,'signType':2}
      - signfields: [$signfield1]
      - app_id: $app_Id2
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
      - eq: ["content.message","成功"]