- config:
    name: "悟空轩辕整合 - 验证企业-法人-经办人签署区"    #需要打开链接验证  查看是否有法人签
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "**********"
      - app_secret: "c7bbb2254c0006435be7dcda18940e1d"
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - group: ''
      - fileId: ${ENV(fileId)}
      - oid_linjue_tsign: "34a2789ee9d348128fbfe01bf16f714d"  #linjue

#oid    :  34a2789ee9d348128fbfe01bf16f714d 
#orgid  :  969c37538edc4631a61d45d637affce8

#- test:
#    name: "获取appSecret"
#    variables:
#    api: api/iterate_cases/getAppInfo.yml
#    extract:
#      - app_secret: content.appSecret
#    validate:
#      - eq: ["status_code", 200]
#    output:
#      - $app_secret

#
#
#- test:
#    name: "标准签授权-后续操作报错的话手动打开链接做授权操作"
#    api: api/signflow/v3/oauth2Index.yml
#    variables:
#      - base_url: ${ENV(base_url)}
#      - json: {account: "***********", appId: $app_id, responseType: "code", redirectUrl: "http://www.baidu.com",bizType: combination, scope: "get_user_info,op_seal,op_sign,op_flow_template,op_organ_admin","authConfigParam":{"authType":"","availableAuthTypes":[""],"indivEditableInfo":[],"orgEditableInfo":["name","certNo","organizationType","legalRepName","legalRepCertNo"],"showResultPage":true}}
#    validate:
#      - eq: ["status_code", 200]


- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - json: {
    "docs":  $docs,
    "flowInfo": {
        "autoArchive": true,
        "autoInitiate": true,
        "businessScene": "v3一步发起 多主体经办人签",
        "flowConfigInfo": {
            "notifyConfig": {
                "noticeDeveloperUrl": "",
                "noticeType": ""
            },
            "signConfig": {
                "redirectUrl": "",
                "signPlatform": ""
            }
        },
        "hashSign": false,
        "remark": "印章平台",
        "signValidity": *************
    },
    "signers": [
        {
            "signOrder": 1,
            "signerAccount": {
              "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
              "authorizedAccountId": "969c37538edc4631a61d45d637affce8"
            },
            "signfields": [
                {
                    "autoExecute": false,
                    "actorIndentityType": 4,
                    "certId": "",
                    "fileId": $fileId,
                    "sealType": "",
                    "posBean": {
                        "addSignTime": true,
                        "keyword": "乙方",
                        "posPage": "1",
                        "posX": 100,
                        "posY": 100
                    },
                   "signType": 1
                },
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "certId": "",
                "fileId": $fileId,
                "sealType": "",
                "posBean": {
                  "addSignTime": true,
                  "keyword": "乙方",
                  "posPage": "1",
                  "posX": 200,
                  "posY": 200
                },
                "signType": 1
              }
            ],
            "thirdOrderNo": "xyz"
        }
    ]
}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


-   test:
        name: "获取签署链接"
        variables:
            -   app_secret: $app_secret
            -   flowId: $sign_flowId1
            -   accountId: $oid_linjue_tsign
            -   urlType: 0
            -   multiSubject: true
            -   organizeId:
            -   compressContext: true
        api: api/iterate_cases/executeUrl.yml
        extract:
            -   shortUrl: content.data.shortUrl
            -   code: content.code
            -   message: content.message
        validate:
            -   eq: ["content.code",0]

- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - flowConfigInfo: {noticeDeveloperUrl: '', noticeType: '', redirectUrl: '', signPlatform: ''}
      - json: {
    "docs":  $docs,
    "flowInfo": {
        "autoArchive": true,
        "autoInitiate": true,
        "businessScene": "v3一步发起 多主体经办人签",
        "flowConfigInfo": {
            "notifyConfig": {
                "noticeDeveloperUrl": "",
                "noticeType": ""
            },
            "signConfig": {
                "redirectUrl": "",
                "signPlatform": ""
            }
        },
        "hashSign": false,
        "remark": "印章平台",
        "signValidity": *************
    },
    "signers": [
        {
            "signOrder": 1,
            "signerAccount": {
              "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
              "authorizedAccountId": "969c37538edc4631a61d45d637affce8"
            },
            "signfields": [
                {
                    "autoExecute": false,
                    "actorIndentityType": 4,
                    "certId": "",
                    "fileId": $fileId,
                    "sealType": "",
                    "posBean": {
                        "addSignTime": true,
                        "keyword": "乙方",
                        "posPage": "1",
                        "posX": 100,
                        "posY": 100
                    },
                   "signType": 1
                },
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "certId": "",
                "fileId": $fileId,
                "sealType": "",
                "posBean": {
                  "addSignTime": true,
                  "keyword": "乙方",
                  "posPage": "1",
                  "posX": 200,
                  "posY": 200
                },
                "signType": 1
              }
            ],
            "thirdOrderNo": "xyz"
        }
    ]
}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: v3一步发起 多主体经办人签 - 东营
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - json: {
    "docs":  $docs,
    "flowInfo": {
        "autoArchive": true,
        "autoInitiate": true,
        "businessScene": "v3一步发起 多主体经办人签",
        "flowConfigInfo": {
            "notifyConfig": {
                "noticeDeveloperUrl": "",
                "noticeType": ""
            },
            "signConfig": {
                "redirectUrl": "",
                "signPlatform": ""
            }
        },
        "hashSign": false,
        "remark": "印章平台",
        "signValidity": *************
    },
    "signers": [
        {
            "signOrder": 1,
            "signerAccount": {
              "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
              "authorizedAccountId": "969c37538edc4631a61d45d637affce8"
            },
            "signfields": [
                {
                    "autoExecute": false,
                    "actorIndentityType": 4,
                    "certId": "",
                    "fileId": $fileId,
                    "sealType": "",
                    "posBean": {
                        "addSignTime": true,
                        "keyword": "乙方",
                        "posPage": "1",
                        "posX": 100,
                        "posY": 100
                    },
                   "signType": 1
                },
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "certId": "",
                "fileId": $fileId,
                "sealType": "",
                "posBean": {
                  "addSignTime": true,
                  "keyword": "乙方",
                  "posPage": "1",
                  "posX": 200,
                  "posY": 200
                },
                "signType": 1
              }
            ],
            "thirdOrderNo": "xyz"
        }
    ]
}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId3: content.data.flowId
    validate:
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: v3一步发起 多主体经办人签 - 指定印章类型
    variables:
      - json: {
    "docs": [
        {
            "encryption": 0,
            "fileId": "2ef966f873444a59b7447de4c3823131",
            "fileName": "1111.pdf",
            "source": 0
        }
    ],
    "flowInfo": {
        "autoArchive": true,
        "autoInitiate": true,
        "businessScene": "v3非注册流程=手绘章",
        "flowConfigInfo": {
            "notifyConfig": {
                "noticeDeveloperUrl": "",
                "noticeType": ""
            },
            "signConfig": {
                "redirectUrl": "",
                "signPlatform": "1,2,3,4"
            }
        },

        "hashSign": false,

        "remark": "印章平台",

        "signValidity": *************
    },

    "signers": [
        {
            "signOrder": 1,
            "signerAccount": {
                "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
                "authorizedAccountId": "34a2789ee9d348128fbfe01bf16f714d"
            },
            "signfields":
              [
                {
                  "autoExecute": false ,
                  "actorIndentityType": 0,
                  "sealId": "",
                  "fileId": "2ef966f873444a59b7447de4c3823131",
                  "posBean": {
                    "addSignTime": true,
                    "posPage": "5",
                    "posX": 444,
                    "posY": 270
                  },
                  "assignedPosbean": "true",
                  "sealType": "",
                  "signDateBeanType": "1",
                  "signDateBean": {
                    "format": "yyyy       MM       dd",
                    "posPage": "5",
                    "fontSize": 14,
                    "posX": 343,
                    "posY": 200,
                    "fontName": "simsun"
                  },
                  "signType": 1,
                  "width": 136
                } ,
                {
                  "autoExecute": false ,
                  "actorIndentityType": 0,
                  "sealId": "",
                  "fileId": "2ef966f873444a59b7447de4c3823131",
                  "posBean": {
                    "addSignTime": true,
                    "posPage": "5",
                    "posX": 444,
                    "posY": 370
                  },
                  "assignedPosbean": "true",
                  "sealType": "",
                  "signDateBeanType": "1",
                  "signDateBean": {
                    "format": "yyyy-MM-dd",
                    "posPage": "5",
                    "fontSize": 14,
                    "posX": 343,
                    "posY": 100,
                    "fontName": "simsun"
                  },
                  "signType": 1,
                  "width": 136
                },
                {
                  "autoExecute": false ,
                  "actorIndentityType": 0,
                  "sealId": "",
                  "fileId": "2ef966f873444a59b7447de4c3823131",
                  "posBean": {
                    "addSignTime": true,
                    "posPage": "5",
                    "posX": 444,
                    "posY": 470
                  },
                  "assignedPosbean": "true",
                  "sealType": "",
                  "signDateBeanType": "1",
                  "signDateBean": {
                    "format": "yyyy-MM-dd",
                    "posPage": "5",
                    "fontSize": 14,
                    "posX": 343,
                    "posY": 200000,
                    "fontName": "simsun"
                  },
                  "signType": 1,
                  "width": 136
                }
              ],
            "thirdOrderNo": "企业2"
        }
    ]
}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId4: content.data.flowId
    validate:
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name:  获取批量签署url
    variables:
      - json: {
    "operatorId": $oid_linjue_tsign,
    "redirectUrl": "",
    "signFlowIds": [$sign_flowId1,
        $sign_flowId2,$sign_flowId3,$sign_flowId4,"02d954d32d4543c0914f14d08187d2b3"
    ]
}
    api: api/signflow/batchSign0210/getBatchUrl.yml
    validate:
      - eq: ["status_code", 200]
    teardown_hooks:
      - ${teardown_re_find($response)}
    extract:
      - batchSerialId: batchSerialId

-   test:
        name: "获取签署链接"
        variables:
            -   app_secret: $app_secret
            -   flowId: $sign_flowId4
            -   accountId: $oid_linjue_tsign
            -   urlType: 0
            -   multiSubject: true
            -   organizeId:
            -   compressContext: true
        api: api/iterate_cases/executeUrl.yml
        extract:
            -   shortUrl: content.data.shortUrl
            -   code: content.code
            -   message: content.message
        validate:
            -   eq: ["content.code",0]

#- test:
#    name: 获取签署链接-个人签
#    variables:
#      - accountId: $oid_linjue_tsign
#      - app_secret: $app_secret
#      - flowId: $sign_flowId4
#    api: api/signflow/v3/executeUrl.yml
#    extract:
#      - shortUrl: content.data.shortUrl
#    validate:
#      - eq: ["content.code",0]



#- test:
#    name: 查询签署区
#    variables:
#      - flowId: $sign_flowId
#    api: api/mobile-shield/signflows_search_signfields.yml
#    extract:
#      - signfieldId: content.data.signfields.0.signfieldId
#    validate:
#      - eq: ["status_code", 200]
#      - eq: ["content.code", 0]
#      - eq: ["content.message", 成功]
#
##- test:
##    name: 查询可用印章
##    variables:
##      - flowId: $sign_flowId
##      - loginAccountId: $oid_linjue_tsign
##      - signerAccountId: $oid_linjue_tsign
##    api: api/signflow/signflows/getSignSeals.yml
##    extract:
##      - personalSealId: content.data.personalSeals.0.sealId
##      - personalSealFileKey: content.data.personalSeals.0.sealFileKey
##    validate:
##      - eq: ["status_code", 200]
##      - eq: ["content.code", 0]
##      - eq: ["content.message", 成功]
#
##- test:
##    name: 签署时需意愿认证
##    variables:
##      - flowId: $sign_flowId
##      - json: {async: true,accountId: $oid_linjue_tsign,updateSignfields: [{sealId: $personalSealId,sealFileKey: $personalSealFileKey,signfieldId: $signfieldId,actorIndentityType: 0,assignedItem: false,assignedPosbean: true,assignedSeal: false, authorizedAccountId: $oid_linjue_tsign,autoExecute: false,signDateBeanType: 1,fileId: $fileId,order: 1,posBean: {addSignTime: false, posPage: "1", posX: 500,posY: 500, qrcodeSign: false,width: 500,signDateBean: {posPage: "1", fontName: "simsun", format: "yyyy年MM月dd日",fontSize: 12,posX: 220,posY: 250},sealType: 1,signType: 1,signerAccountId: $oid_linjue_tsign}}],approvalPolicy: 0,dingUser: {}}
##    api: api/signflow/v3/addUpdateExecute.yml
##    validate:
##      - eq: ["content.code",1436201]
##      - contains: ["content.message",用户未做意愿认证]
##      - eq: ["status_code", 200]
#
#
#- test:
#    name: 获取流程列表
#    variables:
#      - json:
#          {
#            "signFlowStatus": [1,2],# 流程状态 必传 array int 1-签署中 2-已完成
#           "signFlowStartTimeFrom": *************,    #签署流程发起时间开始 必传 long
#            "signFlowStartTimeTo": *************, #签署流程发起时间结束 必传 long,
#            "pageNum": 1, #页码 必传 int
#            "pageSize": 10, # 每页数量 必传 int
#              "signerInfos": [ #参与签署账号列表 非必填 array
#                {
#                  "operatorId": "",    #签署账号  非必填
#                  "signerId": "",    # 签署主体账号  非必填
#                }
#              ]
#          }
#    api: api/signflow/batchSign0210/batchSignList.yml
##    validate:
##      - eq: ["content.data.total", 28]
#
#
#- test:
#    name:  获取批量签署url-报错
#    variables:
#      - json: {
#    "operatorId": $oid_linjue_tsign,
#    "redirectUrl": "",
#    "signFlowIds": [
#         $sign_flowId,$sign_flowId2,"c42352176a3f4015a122d020ae2e4d10","02d954d32d4543c0914f14d08187d2b3","71f29af130424867a933aef5e4598514","032bb28cf463450e80e393bf6cbadc07","5aa736bcff4f471e809c5c4d3752d695",
#"4d6355ec119146488ba6cbf0b8aa917a","79ec28aa7c2f4f838624fab2b9b3b685","2f7d7eb08707486589a49308e035c826","6802e05a293d4458864f25de5dd4a179"
#    ]
#}
#    api: api/signflow/batchSign0210/getBatchUrl.yml
#    validate:
#      - eq: ["status_code", 200]
#      - contains: ["content.message","超过10个"]
#
#

#- test:
#    name: 获取批量签署分类列表
#    variables:
#      - batchSerialId: $batchSerialId
#    api: api/signflow/batchSign0210/batchSortFlow.yml
#    extract:
#      - shortUrl: content.data.noSupportSigns
#    validate:
#      - eq: ["content.code",0]
#
#
#- test:
#    name: 获取批量签中文档页数最小的文档信息及主体类型
#    variables:
#      - batchSerialId: $batchSerialId
#    api: api/signflow/batchSign0210/getMinPage.yml
#    extract:
#      - shortUrl: content.data.actorIdentityTypes
#    validate:
#      - eq: ["content.code",0]
#
#
##未完成  设置签名域信息  需要修改参数 抓包修改
#- test:
#    name: 设置签名域信息
#    variables:
#      - batchSerialId: $batchSerialId
#      - json:  {"signFieldBeans":[{"actorIdentityType":0,"posBean":{"addSignTime":false,"posPage":1,"posX":403.46713346593515,"posY":478.01298317628755,"qrcodeSign":false,"signDateBean":null,"signDateBeanType":0},"signType":1}]}
#    api: api/signflow/batchSign0210/batchSignInfo.yml
#    validate:
#      - eq: ["status_code", 200]
##    extract:
##      - batchSerialId: content.data
#

- test:
    name: 获取印章列表
    variables:
      - batchSerialId: $batchSerialId
    api: api/signflow/batchSign0210/batchGetSeals.yml
    extract:
      - shortUrl: content.data
    validate:
      - eq: ["content.code",0]
#
#
#- test:
#    name: 提交签署
#    variables:
#      - batchSerialId: $batchSerialId
#      - json: {"accountId":"ebda3541171c44d4900447e857fae75a","batchSerialId":$batchSerialId,"accountSealId":"b6c362fc-cc80-4783-b18a-6debfcc2273a","subjectSealBeans":[]}
#    api: api/signflow/batchSign0210/batchSign.yml
#    validate:
#      - eq: ["content.message", "成功"]
#
#
#- test:
#    name: 获取签署结果
#    variables:
#      - batchSerialId: $batchSerialId
#    api: api/signflow/batchSign0210/batchGetResult.yml
##    extract:
##      - shortUrl: content.data.shortUrl
#    validate:
#      - eq: ["content.code",0]





