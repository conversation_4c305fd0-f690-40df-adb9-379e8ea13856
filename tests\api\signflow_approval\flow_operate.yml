# 添加或更新签署区并执行签署
- api:
    def: addUpdateExecute_approval_sign($flowId,$addSignfields,$updateSignfields,$signfieldIds,$approvalPolicy,$accountId,$needSignWilling,$dingUser,$securityCode)
    request:
      url: ${ENV(item_url)}/v3/signflows/$flowId/signfields/add-Update-Execute
      method: PUT
      headers:
        X-Tsign-Client-Id: "PC_SIMPLE"
        X-Tsign-Open-App-Id: ${ENV(X-Tsign-Open-App-Id)
      json:
        async: true
        accountId: $accountId
        addSignfields: $addSignfields
        updateSignfields: $updateSignfields
        signfieldIds: $signfieldIds
        approvalPolicy: $approvalPolicy
        needSignWilling: $needSignWilling
        dingUser: $dingUser
        securityCode: $securityCode

