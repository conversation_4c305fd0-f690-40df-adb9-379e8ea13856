- config:
    name: 反哺case
    base_url: ${ENV(footstone_api_url)}
    "request":
      "base_url":
      "headers":
        "Content-Type": "application/json"
    variables:
      - path_pdf: ****************.pdf
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/模板文件.pdf
      - fileId: ${get_file_id($app_id,$path_pdf)}

- test:
    name: ONLINEBUG-1927：创建账号
    variables:
      - json:
          {
            "name": 王瑶济,
            "thirdPartyUserId": wyj-320721199305232634,
            "idNumber":"320721199305232634",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********"
          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - p_oId: content.data.accountId

- test:
    name: 创建账号2
    variables:
      - json:
          {
            "name":"梁贤红",
            "idNumber":"331082199211223080",
            "idType":"CRED_PSN_CH_IDCARD",
            "mobile":"***********",
            "thirdPartyUserId": lxh-331082199211223080

          }
    api: api/user/account_third/create_third_user_id.yml
    extract:
      - p_oId2: content.data.accountId
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: ONLINEBUG-1927：静默授权
    variables:
      - accountId: $p_oId
      - deadline: null
    api: api/signflow/signAuth/signAuth_appid.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-1927：一步发起用户自动签+用户手动签
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "www.baidu.com", signPlatform: "1,2" }, hashSign: false }
      - signers: [{signOrder: 1,signerAccount: {  signerAccountId: $p_oId,authorizedAccountId: $p_oId}, signfields: [ { autoExecute: true, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 } ,
                                                                                                                      { autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",成功]
      - eq: ["status_code", 200]


- test:
    name: ONLINEBUG-1927：查询流程详情：签署中
    api: api/signflow/signflows/flowsDetailV2.yml
    variables:
      - flowId: $flowId
      - queryAccountId: $p_oId
      - operatorid: $p_oId
    extract:
      - content_data: content.data
      - content_data_configInfo: content.data.configInfo
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.data.flowStatus",1]


- test:
    name: ONLINEBUG-2539：创建流程
    variables:
      - autoArchive: true
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $p_oId
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-2539：获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: ****************.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $p_oId
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: ONLINEBUG-2539：添加流程文档：未上传成功的文档添加失败
    variables:
      - flowId: $flowId
      - doc1: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437516]
      - eq: ["status_code", 200]
      - eq: ["content.message", "文档未上传完成或上传失败, 请重新上传"]



- test:
    name: ONLINEBUG-2794：查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $p_oId2
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-2794：一步发起用户手动签:指定印章，印章不匹配
    variables:
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2" }, hashSign: false }
      - signers: [{                                                                                                                                                     signOrder: 1,signerAccount: {  signerAccountId: $p_oId,authorizedAccountId: $p_oId}, signfields: [
      { autoExecute: false, actorIndentityType: 0, fileId: $fileId,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,  sealId: $sealId, width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message","参数错误: 印章和签署主体不匹配"]
      - eq: ["content.code", 1435002]



- test:
    name: ONLINEBUG-3548：创建流程
    variables:
      - autoArchive: false
      - businessScene: 单人单文档
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - initiatorAccountId: $p_oId
      - extend: {}
      - contractValidity: null
      - payerAccountId: null
      - signValidity: null
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-3548：添加流程文档
    variables:
      - flowId: $flowId
      - doc1: {'fileHash': '', 'fileId': $fileId, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
      - eq: ["content.message", "成功"]

- test:
    name: ONLINEBUG-3548：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: ONLINEBUG-3548：添加签署区拆分场景：归档
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1437170]
      - eq: ["content.message", "没有签署，无需归档"]
      - eq: ["status_code", 200]
