import requests
import json

url = 'http://in-test-openapi.tsign.cn/v3/sign-flow/create-by-file'

headers = {
    'X-Tsign-Open-App-Id': '7876722740',
    'Accept': '*/*',
    'X-Tsign-Open-Auth-Mode': 'simple',

    'Content-Type': 'application/json; charset=UTF-8'
}

headers = {
    'X-Tsign-Open-App-Id': '7876722740',
    'X-Tsign-Open-Auth-Mode': 'simple',
    'Content-Type': 'application/json;charset=UTF-8',

}

data = {
    "docs": [
        {
            "fileEditPwd": "",
            "fileId": "63a913cb97ad4ef9b8834947b48e8862",
            "fileName": "个人借贷合同.pdf",
            "neededPwd": False
        }
    ],
    "signFlowInitiator": {
        "orgInitiator": {
            "orgId": "90fc72de45174e66b70653c7ec1f7aae",
            "transactor": {
                "psnId": "24410509f77448fcb8cab2951ea853b6"
            }
        }
    },

    "signFlowConfig": {

        "authConfig": {
            "audioVideoTemplateId": "",
            "orgAvailableAuthModes": [],
            "orgEditableFields": [],
            "psnAvailableAuthModes": [],
            "psnEditableFields": [],
            "willingnessAuthModes": []
        },
        "autoFinish": True,
        "autoStart": True,
        "chargeConfig": {
            "chargeMode": 1
        },
        "noticeConfig": {
            "noticeTypes": "1"
        },
        "redirectConfig": {
            "redirectDelayTime": None,
            "redirectUrl": ""
        },

        "signFlowTitle": "ca2期create-by-file",
        "signFlowExpireTime": ""
    },
    "signers": [
        {
            "noticeConfig": {
                "noticeTypes": ""
            },
            "orgSignerInfo": None,
            "psnSignerInfo": {
                "psnId": "3986d7544d9848f186ee9c5dcd342de1",
                "psnAccount": ""
            },
            "signConfig": {
                "signOrder": 1,
"agreeSkipWillingness": True
            },
            "signFields": [
                {
                    "customBizNum": "",
                    "fileId": "63a913cb97ad4ef9b8834947b48e8862",
                    "normalSignFieldConfig": {
                        "assignedSealId": "",
                        "autoSign": False,
                        "availableSealIds": [],
                        "freeMode": True,
                        "movableSignField": True,
                        "orgSealBizTypes": "",
                        "psnSealStyles": "",
                        "signFieldPosition": {
                            "acrossPageMode": "",
                            "positionPage": "1",
                            "positionX": 100,
                            "positionY": 100
                        },
                        "signFieldSize": 200,
                        "signFieldStyle": 1
                    },
                    "remarkSignFieldConfig": None,
                    "signDateConfig": {
                        "dateFormat": "",
                        "fontSize": 0,
                        "showSignDate": 1,
                        "signDatePositionPage": 2,
                        "signDatePositionX": 100,
                        "signDatePositionY": 100
                    },
                    "signFieldType": 0
                }
            ],
            "signerType": 0
        }
    ]
}

print(json.dumps(data))
response = requests.post(url, headers=headers, data=json.dumps(data))

print(response.status_code)
print(response.json())
signFlowId = response.json()['data']['signFlowId']
# 获取签署链接
url = 'http://in-test-openapi.tsign.cn/v3/sign-flow/{}/sign-url'.format(signFlowId)
data = {
    "operator": {
        "psnId": "",
        "psnAccount": "***********"
    },
    "organization": {
        "orgId": "",
        "orgName": ""
    },
    "clientType": "",
    "appScheme": "",
    "needLogin": False,
    "redirectConfig": {
        "redirectDelayTime": 3,
        "redirectUrl": "https://www.baidu.com"
    }
}
response = requests.post(url, headers=headers, data=json.dumps(data))
res = response.json()
print(res)
print(f"***********获取签署链接：{res.get('data').get('shortUrl')}")

