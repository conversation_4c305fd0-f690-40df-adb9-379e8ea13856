config:
  name: 相对方管理二期
#    variables:
testcases:

  相对方管理二期:
    testcase: testcases/opponent_check/opponent_black.yml
  #    异步签署下载权限校验:
  #      testcase: testcases/signflow_file/pack_file_async.yml
  0820:
    testcase: testcases/iterate_cases/0820Sign.yml

  0917钉签权限:
    testcase: testcases/iterate_cases/0917DingPermission.yml

  0928原文文本签:
    testcase: testcases/iterate_cases/0928dataSignOriginal.yml

  20201015迭代用例:
    testcase: testcases/iterate_cases/20201015Sign.yml

  20201119迭代用例:
    testcase: testcases/iterate_cases/20201119Sign.yml

  20210722迭代用例:
    testcase: testcases/iterate_cases/20210722Sign.yml