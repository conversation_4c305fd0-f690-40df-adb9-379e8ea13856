- config:
    name: "指定意愿分步/一步发起，该appId意愿方式为支付宝刷脸+短信+密码，报错请看是否被修改"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_Id1: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - path_pdf: data/个人借贷合同.pdf
      - fileId: ${ENV(fileId)}
      - group: ''
      - xuanyuan_sixian: ${ENV(xuanyuan_sixian)}
      - language: zh-cn
- test:
    name: "创建王瑶济账号"
    variables:
      - thirdPartyUserId: **************
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_wang: content.data.accountId

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，仅传了微信刷脸
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "FACE_TECENT_CLOUD_H5" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，未传意愿方式
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，意愿方式传空
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域1，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 添加相同签署人+签署主体的手动签名域2，仅传了短信
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "CODE_SMS" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域1，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 添加相同签署人+不同签署主体的手动签名域2，仅传了短信
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "2","authorizedAccountId": "$xuanyuan_sixian","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "CODE_SMS" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加有序签手动签名域1，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 1,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 添加有序签手动签名域2，仅传了短信验证码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，传支付宝刷脸
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "FACE_ZHIMA_XY" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有支付宝刷脸
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]
      - contains: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_ZHIMAAUTH_TYPE ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为微信刷脸+自主刷脸
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，仅传了微信刷脸
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "FACE_TECENT_CLOUD_H5" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用意愿方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为微信刷脸+自主刷脸
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为微信刷脸+自主刷脸
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，签名域传空
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为微信刷脸+自主刷脸
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，签名域未传
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为微信刷脸+自主刷脸
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动无序签名域1，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 1,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 添加手动无序签名域2，仅传了短信
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "CODE_SMS" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 1,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为短信+密码+微信刷脸
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "FACE_TECENT_CLOUD_H5","CODE_SMS","SIGN_PWD" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，未传签名域
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为空
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为空
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，仅传了微信刷脸
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "FACE_TECENT_CLOUD_H5" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为空
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域，传空
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含支付宝刷脸，密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 3 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为空
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动签名域1，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 添加手动签名域2，仅传了短信
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "CODE_SMS" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 分步发起签署流程，流程维度意愿方式为空
    variables:
      - json: { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": "false","businessScene": "创建流程","configInfo": { "noticeType": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false,"willTypes": [ "" ] },"extend": { },"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 添加文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf","filePassword": "","encryption": 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 添加手动有序签名域1，仅传了密码
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "SIGN_PWD" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 1,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 添加手动有序签名域2，仅传了短信
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "$fileId","signerAccountId": "$accountId_wang","actorIndentityType": "0","authorizedAccountId": "$accountId_wang","assignedPosbean": false,"signDateBeanType": "1","willTypes": [ "CODE_SMS" ],"signDateBean": { "posPage": "1","posX": 118,"posY": 199 },"order": 2,"posBean": { "posPage": "1","posX": 314,"posY": 225 },"sealType": "","sealId": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.signfieldBeans.0.signfieldId, 0 ]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 一步发起，流程为短信+密码，签名域传密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 一步发起，流程为短信+密码，签名域传微信刷脸
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "FACE_TECENT_CLOUD_H5" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程为短信+密码，签名域传空
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程为短信+密码，无序签，签名域分别传短信和密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "CODE_SMS" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" },{ "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程为短信+密码，有序签，签名域分别传短信和密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "CODE_SMS" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" },{ "platformSign": false,"signOrder": 2,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程为短信+密码，签名域传支付宝刷脸
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "FACE_ZHIMA_XY" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有支付宝刷脸
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_ZHIMAAUTH_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程为短信+密码，签名域传支付宝刷脸
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "FACE_ZHIMA_XY" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有支付宝刷脸
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_ZHIMAAUTH_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程为微信刷脸+自主刷脸，签名域传微信刷脸
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "FACE_TECENT_CLOUD_H5" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程为微信刷脸+自主刷脸，签名域传密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 一步发起，流程为微信刷脸+自主刷脸，签名域传空
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "" },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程为微信刷脸+自主刷脸，无序签名域分别传短信和密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" },{ "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "CODE_SMS" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程为短信+密码+自主刷脸，签名域传空
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": [ "SIGN_PWD","CODE_SMS","FACE_FACE_LIVENESS_RECOGNITION" ],"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程传空，签名域传密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": null,"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅包含密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_PASSWORD_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程传空，签名域传微信刷脸
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": null,"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "FACE_TECENT_CLOUD_H5" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程传空，签名域传空
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": null,"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "null" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 3 ]

- test:
    name: 一步发起，流程传空，无序签名域分别传短信+密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": null,"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" },{ "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "CODE_SMS" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程传空，有序签名域分别传短信+密码
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": null,"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "SIGN_PWD" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" },{ "platformSign": false,"signOrder": 2,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "CODE_SMS" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]

- test:
    name: 一步发起，流程传空，签名域传支付宝刷脸
    variables:
      - json: { "docs": [ { "fileId": "$fileId","fileName": "不重要的文件名.pdf" } ],"flowInfo": { "flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"autoArchive": true,"autoInitiate": true,"businessScene": "不重要的任务名","contractRemind": 65,"initiatorAccountId": "$accountId_wang","initiatorAuthorizedAccountId": "$accountId_wang","flowConfigInfo": { "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType": "","redirectUrl": "","signPlatform": "","willTypes": null,"countdown": 0,"redirectDelayTime": 3 } },"signers": [ { "platformSign": false,"signOrder": 1,"signerAccount": { "signerAccountId": "$accountId_wang","authorizedAccountId": "$accountId_wang","noticetype": "","willTypes": [ "FACE_ZHIMA_XY" ] },"signfields": [ { "assignedPosbean": false,"autoExecute": false,"actorIndentityType": 0,"sealId": "","fileId": "$fileId","posBean": { "posPage": "1","posX": 500,"posY": 500 },"sealType": "","signDateBeanType": "1","signDateBean": { "fontSize": 20,"posX": 220,"posY": 250 },"signType": 1,"width": 150 } ],"thirdOrderNo": "11111" } ] }
    api: api/iterate_cases/createFlowOneStep.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowId, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅包含支付宝刷脸
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_ZHIMAAUTH_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程传短信+密码，签名域传密码
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "SIGN_PWD","CODE_SMS" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "SIGN_PWD" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅包含密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_PASSWORD_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程传短信+密码，签名域传微信刷脸
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "SIGN_PWD","CODE_SMS" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "FACE_TECENT_CLOUD_H5" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程传短信+密码，签名域传空
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "SIGN_PWD","CODE_SMS" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程传短信+密码，无序签名域传短信+密码
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "SIGN_PWD","CODE_SMS" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "SIGN_PWD" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] },{ "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "CODE_SMS" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程传短信+密码，有序签名域传短信+密码
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "SIGN_PWD","CODE_SMS" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "SIGN_PWD" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] },{ "seperateSigner": false,"signOrder": 2,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "CODE_SMS" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode",SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 一步发起，流程传短信+密码，有序签名域传支付宝刷脸
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "SIGN_PWD","CODE_SMS" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "FACE_ZHIMA_XY" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-仅有支付宝刷脸
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_ZHIMAAUTH_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程传微信刷脸+自主刷脸，签名域传微信刷脸
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "FACE_TECENT_CLOUD_H5" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程传微信刷脸+自主刷脸，签名域传密码
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "SIGN_PWD" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_PASSWORD_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程传微信刷脸+自主刷脸，签名域传空
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "111" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程传微信刷脸+自主刷脸，无序签名域传短信+密码
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "FACE_TECENT_CLOUD_H5","FACE_FACE_LIVENESS_RECOGNITION" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "SIGN_PWD" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] },{ "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "CODE_SMS" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程传短信+密码+自主刷脸，签名域传空
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "SIGN_PWD","CODE_SMS","FACE_FACE_LIVENESS_RECOGNITION" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "111","222" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authGroupCode", SMS_CODE ]
      - eq: [ "content.data.officialWillAuthTypes.1.authGroupCode", SIGN_PWD ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 2 ]

- test:
    name: 一步发起，流程传空，签名域传密码
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "SIGN_PWD" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-密码
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.data.officialWillAuthTypes.0.authModes.0.authCode", PSN_PASSWORD_TYPE ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 1 ]

- test:
    name: 一步发起，流程传空，签名域传微信刷脸
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ "" ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ "FACE_TECENT_CLOUD_H5" ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-无可用认证方式
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.officialWillAuthAvailable", false ]

- test:
    name: 一步发起，流程传空，签名域传空
    variables:
      - json: { "accountId": "$accountId_wang","createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "不重要的任务名","configInfo": { "noticeType": "","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","willTypes": [ ] },"flowConfigItemBean": { "buttonConfig": { "propButton": "1,2,15" } },"docs": [ { "fileId": "$fileId" } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": "$accountId_wang","signerAccountName": "","signerAuthorizerId": "$accountId_wang","signerAuthorizerName": "","signerNickname": "","willTypes": [ ] } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": "$fileId","autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "2","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    extract:
      - sign_flowId: content.data.flowIds.0
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.message",成功 ]
      - len_gt: [ content.data.flowIds.0, 0 ]

- test:
    name: 获取意愿链接
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
    api: api/signflow/signflows/identifyUrl.yml
    extract:
      - willAuthParam: content.data.url
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 获取意愿登录态
    variables:
      - index1: 'willAuthParam='
      - index2: '&lang'
      - param: ${substring($willAuthParam, $index1, $index2)}
    api: api/signflow/will/getSessionId.yml
    extract:
      - sessionId: content.data.sessionId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 查看意愿方式-同时包含密码和短信
    variables:
      - sessionId: $sessionId
    api: api/signflow/will/willingness.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]
      - len_eq: [ "content.data.officialWillAuthTypes", 3 ]