- config:
    name: "e签盾流程创建-更新-轩辕"
    base_url: ${ENV(footstone_mobile_shield_url)}
    variables:
      - app_id1: ${ENV(mobile_shield_xuanyuan_appid)}
      - app_id: ${ENV(mobile_shield_xuanyuan_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(wukong_no_forcewill)}
      - path_pdf1: data/个人借贷合同.pdf
      - path_pdf2: data/有签名无篡改2.pdf
      - path_pdf3: data/被篡改的文档1.pdf
      - path_pdf4: data/附件2.pdf

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 - 泗县深泉纯净水有限公司"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", "成功" ]


- test:
    name: "文件上传 - 个人借贷合同ckl.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同ckl.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", "成功" ]

#上传附件
- test:
    name: "上传附件 - 附件2.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf4)}
      - contentType: application/pdf
      - fileName: 附件2.pdf
      - fileSize: ${get_file_size($path_pdf4)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId4: content.data.fileId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf4)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf4)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.errCode", 0 ]
      - eq: [ "content.msg", "成功" ]






#创建e签盾流程 - createAllAtOnce
- test:
    name: "创建e签盾签署流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "e签盾签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 2 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId1: content.data.processFlows.0.flowId



#创建现有流程 - createAllAtOnce
- test:
    name: "创建现有签署流程"
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "现有签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 1 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId2: content.data.processFlows.0.flowId


#查询e签盾流程1 - /v1/signflows/{flowId}
- test:
    name: "查询e签署流程"
    variables:
      - flowId: $flowId1
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "e签盾签署流程" ]
      - eq: [ "content.data.initiatorAccountId", $accountId_ckl ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 2 ]
      - eq: [ "content.data.autoArchive", true ]


#查询e签盾流程2 - /v1/signflows/{flowId}/detail
- test:
    name: "查询e签署流程 - /v1/signflows/{flowId}/detail"
    variables:
      - flowId: $flowId1
      - queryAccountId: $caogu_accountId
      - operator_id: $caogu_accountId
    api: api/mobile-shield/signflows_search_V1.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "e签盾签署流程" ]
      - eq: [ "content.data.initiatorAccountId", $accountId_ckl ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 2 ]
      - eq: [ "content.data.autoArchive", true ]

#查询e签盾流程3 - /v2/signflows/{flowId}/detail
- test:
    name: "查询e签署流程 - /v2/signflows/{flowId}/detail"
    variables:
      - flowId: $flowId1
      - queryAccountId: $caogu_accountId
      - operator_id: $caogu_accountId
    api: api/mobile-shield/signflows_search_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "e签盾签署流程" ]
      - eq: [ "content.data.initiatorAccountId", $accountId_ckl ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 2 ]
      - eq: [ "content.data.autoArchive", true ]



#更新e签盾流程1 - /v1/signflows/{flowId}
- test:
    name: "更新e签盾签署流程 - /v1/signflows/{flowId}"
    variables:
      - flowId: $flowId1
      - autoArchive: false
      - businessScene: "e签盾签署流程-更新1"
      - configInfo: { "mobileShieldWay": 1 }
      - initiatorAccountId: $caogu_accountId
    api: api/mobile-shield/signflows_update.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询e签署流程"
    variables:
      - flowId: $flowId1
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "e签盾签署流程-更新1" ]
      - eq: [ "content.data.initiatorAccountId", $caogu_accountId ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]


#更新e签盾流程2 - /v1/signflows/{flowId}/
- test:
    name: "更新e签盾签署流程 - /v1/signflows/{flowId}/updateWithScratch"
    variables:
      - flowId: $flowId1
      - autoArchive: true
      - businessScene: "e签盾签署流程-更新2"
      - configInfo: { "mobileShieldWay": 2 }
      - initiatorAccountId2: $accountId_ckl
      - operatorId: $accountId_ckl
    api: api/mobile-shield/signflows_updateWithScratch.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询e签署流程"
    variables:
      - flowId: $flowId1
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "e签盾签署流程-更新2" ]
      #      - eq: ["content.data.initiatorAccountId", $accountId_ckl]
      - eq: [ "content.data.configInfo.mobileShieldWay", 2 ]
      - eq: [ "content.data.autoArchive", true ]


#更新e签盾流程3 - /v2/signflows/$flowId/updateAllAtOnce
- test:
    name: "更新e签盾签署流程 - /v2/signflows/$flowId/updateAllAtOnce"
    variables:
      - flowId: $flowId1
      - autoArchive: false
      - businessScene: "e签盾签署流程-更新3"
      - configInfo: { "mobileShieldWay": 1 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: null
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询e签署流程"
    variables:
      - flowId: $flowId1
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "e签盾签署流程-更新3" ]
      - eq: [ "content.data.initiatorAccountId", $caogu_accountId ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]





#更新现有流程1 - /v1/signflows/{flowId}
- test:
    name: "更新现有签署流程 - /v1/signflows/{flowId}"
    variables:
      - flowId: $flowId2
      - autoArchive: false
      - businessScene: "现有签署流程-更新1"
      - configInfo: { "mobileShieldWay": 2 }
      - initiatorAccountId: $caogu_accountId
    api: api/mobile-shield/signflows_update.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询现有签署流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId2
      - queryAccountId: $caogu_accountId
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "现有签署流程-更新1" ]
      - eq: [ "content.data.initiatorAccountId", $caogu_accountId ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 2 ]
      - eq: [ "content.data.autoArchive", false ]


#更新现有流程2 - /v1/signflows/{flowId}/updateWithScratch
- test:
    name: "更新现有签署流程 - /v1/signflows/{flowId}/updateWithScratch"
    variables:
      - flowId: $flowId2
      - autoArchive: true
      - businessScene: "现有签署流程-更新2"
      - configInfo: { "mobileShieldWay": 1 }
      - initiatorAccountId2: $accountId_ckl
      - operatorId: $accountId_ckl
    api: api/mobile-shield/signflows_updateWithScratch.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询现有签署流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId2
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "现有签署流程-更新2" ]
      #      - eq: ["content.data.initiatorAccountId", $accountId_ckl]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", true ]


#更新现有流程3 - /v2/signflows/$flowId/updateAllAtOnce
- test:
    name: "更新现有签署流程 - /v2/signflows/$flowId/updateAllAtOnce"
    variables:
      - flowId: $flowId2
      - autoArchive: false
      - businessScene: "现有签署流程-更新3"
      - configInfo: { "mobileShieldWay": 2 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: null
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询现有签署流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId2
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "现有签署流程-更新3" ]
      - eq: [ "content.data.initiatorAccountId", $caogu_accountId ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 2 ]
      - eq: [ "content.data.autoArchive", false ]



#异常更新
- test:
    name: "创建e签盾签署流程"
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "e签盾签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 2 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId3: content.data.processFlows.0.flowId


- test:
    name: "更新e签盾签署流程 - 更新为个人签名域"
    variables:
      - flowId: $flowId3
      - autoArchive: false
      - businessScene: "更新为个人签名域"
      - configInfo: { "mobileShieldWay": 2 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $caogu_accountId, "signerAuthorizerType": 0,"signerSignRoles": "0" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 目前e签盾仅支持机构公章手动签署" ]

- test:
    name: "更新e签盾签署流程 - 更新为法人签名域"
    variables:
      - flowId: $flowId3
      - autoArchive: false
      - businessScene: "更新为法人签名域"
      - configInfo: { "mobileShieldWay": 2 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "2" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 目前e签盾仅支持机构公章手动签署" ]

- test:
    name: "更新e签盾签署流程 - 更新为经办人签名域"
    variables:
      - flowId: $flowId3
      - autoArchive: false
      - businessScene: "更新为经办人签名域"
      - configInfo: { "mobileShieldWay": 2 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "3" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 目前e签盾仅支持机构公章手动签署" ]

- test:
    name: "更新e签盾签署流程 - 更新为现有流程、个人签名域"
    variables:
      - flowId: $flowId3
      - autoArchive: false
      - businessScene: "更新为现有流程 - 个人签名域"
      - configInfo: { "mobileShieldWay": 1 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $caogu_accountId, "signerAuthorizerType": 0,"signerSignRoles": "0" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId3
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "更新为现有流程 - 个人签名域" ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]




- test:
    name: "创建e签盾签署流程"
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "e签盾签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 2 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId4: content.data.processFlows.0.flowId



- test:
    name: "更新e签盾签署流程 - 更新为现有流程、法人签名域"
    variables:
      - flowId: $flowId4
      - autoArchive: false
      - businessScene: "更新为现有流程 - 法人签名域"
      - configInfo: { "mobileShieldWay": 1 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "2" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId4
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "更新为现有流程 - 法人签名域" ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]




- test:
    name: "创建e签盾签署流程"
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "e签盾签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 2 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId5: content.data.processFlows.0.flowId



- test:
    name: "更新e签盾签署流程 - 更新为现有流程、经办人签名域"
    variables:
      - flowId: $flowId5
      - autoArchive: false
      - businessScene: "更新为现有流程 - 经办人签名域"
      - configInfo: { "mobileShieldWay": 1 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "3" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId5
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "更新为现有流程 - 经办人签名域" ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]




- test:
    name: "创建现有签署流程"
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "现有签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 1 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId6: content.data.processFlows.0.flowId

- test:
    name: "更新现有签署流程 - 更新为e签盾流程、个人签名域"
    variables:
      - flowId: $flowId6
      - autoArchive: false
      - businessScene: "更新为e签盾流程、个人签名域"
      - configInfo: { "mobileShieldWay": 2 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $caogu_accountId, "signerAuthorizerType": 0,"signerSignRoles": "0" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 目前e签盾仅支持机构公章手动签署" ]

- test:
    name: "更新现有签署流程 - 更新为个人签名域"
    variables:
      - flowId: $flowId6
      - autoArchive: false
      - businessScene: "更新为个人签名域"
      - configInfo: { "mobileShieldWay": 1 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $caogu_accountId, "signerAuthorizerType": 0,"signerSignRoles": "0" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId6
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "更新为个人签名域" ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]


- test:
    name: "创建现有签署流程"
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "现有签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 1 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId7: content.data.processFlows.0.flowId

- test:
    name: "更新现有签署流程 - 更新为e签盾流程、法人签名域"
    variables:
      - flowId: $flowId7
      - autoArchive: false
      - businessScene: "更新为e签盾流程、法人签名域"
      - configInfo: { "mobileShieldWay": 2 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "2" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 目前e签盾仅支持机构公章手动签署" ]

- test:
    name: "更新现有签署流程 - 更新为法人签名域"
    variables:
      - flowId: $flowId7
      - autoArchive: false
      - businessScene: "更新为法人签名域"
      - configInfo: { "mobileShieldWay": 1 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "2" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId7
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "更新为法人签名域" ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]


- test:
    name: "创建现有签署流程"
    variables:
      - json: { "accountId": $accountId_ckl,"initiatorAccountId": $accountId_ckl,"createWay": "normal","autoInitiate": false,"autoArchive": true,"businessScene": "现有签署流程","configInfo": { "noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","mobileShieldWay": 1 },"docs": [ { "fileId": $fileId1 } ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "1","signerAccount": "","signerAccountId": $accountId_ckl,"signerAccountName": "","signerAuthorizerId": $sixian_organize_id,"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "certId": "","fileId": $fileId1,"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 1,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - flowId8: content.data.processFlows.0.flowId

- test:
    name: "更新现有签署流程 - 更新为e签盾流程、经办人签名域"
    variables:
      - flowId: $flowId8
      - autoArchive: false
      - businessScene: "更新为e签盾流程、经办人签名域"
      - configInfo: { "mobileShieldWay": 2 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "3" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1435002 ]
      - eq: [ "content.message", "参数错误: 目前e签盾仅支持机构公章手动签署" ]

- test:
    name: "更新现有签署流程 - 更新为经办人签名域"
    variables:
      - flowId: $flowId8
      - autoArchive: false
      - businessScene: "更新为经办人签名域"
      - configInfo: { "mobileShieldWay": 1 }
      - accountId: $caogu_accountId
      - docs: [ { "fileId": $fileId1 } ]
      - signers: [ { "signerAccounts": [ { "signerAccountId": $caogu_accountId, "signerAuthorizerId": $sixian_organize_id, "signerAuthorizerType": 1,"signerSignRoles": "3" } ] } ]
    api: api/mobile-shield/signflows_updateAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]

- test:
    name: "查询流程"
    setup_hooks:
      - ${sleep_N_secs(5)}
    variables:
      - flowId: $flowId8
      - filter-result: false
    api: api/mobile-shield/signflows_search.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - eq: [ "content.data.businessScene", "更新为经办人签名域" ]
      - eq: [ "content.data.configInfo.mobileShieldWay", 1 ]
      - eq: [ "content.data.autoArchive", false ]