- config:
    name: 文件上传操作
    variables:
      - footstone_api_url: ${ENV(footstone_api_url)}
    ###def: file_action($contentMd5, $contentType, $fileName, $fileSize,$local_path)
    request:
      base_url:
      headers:
      "Content-Type": "application/json"

- test:
    name: upload_filekeys上传文件
    api: api/file_template/upload_filekeys/upload_filekeys.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileKey: content.data.fileKey
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - len_gt: ["content.data.fileKey",0]
      - contains: ["content.data.uploadUrl","esignoss.oss-cn-hangzhou.aliyuncs.com"]
    output:
      - $uploadUrl

- test:
    name: 上传文件到oss
    variable_binds:
      - binary: ${open_file($local_path)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validators:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]