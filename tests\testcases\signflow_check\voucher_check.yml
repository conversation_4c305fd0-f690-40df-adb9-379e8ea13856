- config:
    name: 签署凭证查询校验
    base_url: ${ENV(base_url)}
    variables:

      - perIdType: CRED_PSN_CH_IDCARD

      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - client_id: pc
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:



- test:
    name: "创建新账号1"
    api: api/user/accounts/create_accounts.yml   #无email/mobile
    variables:
      - idNo: "350725198204117350"
      - idType: "19"
      - name: "孙明睿"
      - mobile: ***********
      - email: <EMAIL>
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]



- test:
    name: 获取上传文件url
    variables:
      - app_id: $app_Id1
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]




- test:
    name: 创建流程和文档
    variables:
      - autoArchive: false
      - configInfo:
      - contractValidity:
      - extend:
      - payerAccountId:
      - signValidity:
      - initiatorAccountId: $personOid1
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 查询流程详情
    api: api/signflow/aggregate_sign/select_detail.yml
    variables:
      - queryAccountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",0]

- test:
    name: "创建个人模版印章"
    api: api/seals/seals/create_p_template_seal.yml
    variables:
      - accountId: $personOid1
      - color: "RED"
      - height: 2
      - alias: "某某的印章"
      - type: "SQUARE"
      - width: 2
    extract:
      - sealId_p1: content.data.sealId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]

- test:
    name: 查询personOid1的印章
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - sealId_p1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]



- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 批量添加流程签署区
    variables:
      - flowId: $flowId
      - posBean: {}
      - autoExecute: 0
      - signfield1: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$personOid1,$personOid1,$sealId_p1,1)} #个人签

      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldIds: content.data.signfieldIds
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]


- test:
    name: 添加_更新_执行签署区
    variables:
      - accountId: $personOid1
      - flowId: flowId
      - posBean: {addSignTime: True, key: '', posPage: '1', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - addSignfield_3: ${gen_signfield_data_V2(0,0,$fileId1,$flowId,$posBean,1,$personOid1,$personOid1,$sealId_p1,1)}
      - addSignfields: [$addSignfield_3]
      - updateSignfields: []
      - signfieldIds: $signfieldIds
      - approvalPolicy:
      - dingUser:
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    extract:
      - docSignResult_fileId: content.data.docSignResult.0.fileId
      - signfieldSignResult_signfieldId: content.data.signfieldSignResult.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.docSignResult.0.optResult",0]
      - eq: ["content.data.docSignResult.0.failedReason",null]

- test:
    name: 查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - accountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]


- test:
    name: 查询签署凭证-流程未归档
    api: api/signflow/signflows/voucher.yml
    variables:
      - flow_Id: $flowId
      - operatorid: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",当前流程未全部签署完成，不支持获取签署凭证]

- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - queryAccountId: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 查询签署凭证-已登录
    api: api/signflow/signflows/voucher.yml
    variables:
      - flow_Id: $flowId
      - operatorid: $personOid1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.logined",True]

- test:
    name: 查询签署凭证-未登录
    api: api/signflow/signflows/voucher.yml
    variables:
      - flow_Id: $flowId
      - operatorid: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.logined",False]

- test:
    name: 查询签署凭证-流程不存在
    api: api/signflow/signflows/voucher.yml
    variables:
      - flow_Id: '****************'
      - operatorid: ''
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",流程不存在:****************]