#def: createWithSignDocs($autoArchive, $businessScene, $configInfo, $contractValidity, $createWay,$docs, $extend, $initiatorAccountId, $payerAccountId, $signValidity)
request:
  url: /v1/signflows/createWithSignDocs
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    createWay: $createWay
    docs: $docs
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity

# 创建流程并添加文档, 模板创建流程情况下支持自动发起