- config:
    name: "********迭代"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(opponent_appid)}
      - account_id: ${ENV(account_id3_in_tsign)}
      - account_id1_in_tsign: ${ENV(account_id1_in_tsign)}
      - account_id2_in_tsign: ${ENV(account_id2_in_tsign)}
      - fileId: ${ENV(fileId)}
      - orgid_dy: ${ENV(orgid_dy)}
      - group:  ${ENV(envCode)}

- test:
    name: 分布发起流程
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id
      - initiatorAuthorizedAccountId: $account_id
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-不传sealBizTypes
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "2",signerAccountId: $account_id1_in_tsign,"authorizedAccountId": $orgid_dy,"assignedPosbean": false,"signDateBeanType":"1","signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加个人签名域-sealBizTypes传空
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "2",signerAccountId: $account_id,"authorizedAccountId": $orgid_dy,"assignedPosbean": false,"signDateBeanType":"1","sealBizTypes": "", "signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加个人签名域-sealBizTypes传ALL
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "2",signerAccountId: $account_id2_in_tsign,"authorizedAccountId": $orgid_dy,"assignedPosbean": false,"signDateBeanType":"1","sealBizTypes": "ALL", "signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 获取流程印章/不传——全量
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $account_id1_in_tsign
      - signerAccountId: $account_id1_in_tsign
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - response: content.data
    validate:
      - eq: [status_code, 200]
      - contains: ["${toString($response)}", COMMON]
      - contains: ["${toString($response)}", PUBLIC]
      - contains: ["${toString($response)}", CONTRACT]

- test:
    name: 获取流程印章/传空——全量
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $account_id
      - signerAccountId: $account_id
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - response: content.data
    validate:
      - eq: [status_code, 200]
      - contains: ["${toString($response)}", COMMON]
      - contains: ["${toString($response)}", PUBLIC]
      - contains: ["${toString($response)}", CONTRACT]

- test:
    name: 获取流程印章/传ALL——全量
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $account_id2_in_tsign
      - signerAccountId: $account_id2_in_tsign
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - response: content.data
    validate:
      - eq: [status_code, 200]
      - contains: ["${toString($response)}", COMMON]
      - contains: ["${toString($response)}", PUBLIC]
      - contains: ["${toString($response)}", CONTRACT]

- test:
    name: 分布发起流程
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id
      - initiatorAuthorizedAccountId: $account_id
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-sealBizTypes传CONTRACT
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "2",signerAccountId: $account_id,"authorizedAccountId": $orgid_dy,"assignedPosbean": false,"signDateBeanType":"1","sealBizTypes": "CONTRACT", "signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 获取流程印章/CONTRACT
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $account_id
      - signerAccountId: $account_id
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - response: content.data
    validate:
      - eq: [status_code, 200]
      - contains: ["${toString($response)}", CONTRACT]
      - eq: ["${contain_keys($response,PUBLIC)}", False]
      - eq: ["${contain_keys($response,LEGAL_PERSON)}", False]
      - eq: ["${contain_keys($response,FINANCE)}", False]
      - eq: ["${contain_keys($response,INVOICE)}", False]
      - eq: ["${contain_keys($response,PERSONNEL)}", False]

- test:
    name: 分布发起流程
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $account_id
      - initiatorAuthorizedAccountId: $account_id
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [{fileId: $fileId, fileName: 文档, filePassword: "",encryption: 0}]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加个人签名域-sealBizTypes传CONTRACT,PUBLIC
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "2",signerAccountId: $account_id,"authorizedAccountId": $orgid_dy,"assignedPosbean": false,"signDateBeanType":"1","sealBizTypes": "CONTRACT,PUBLIC", "signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldBeans.0.signfieldId",0]

- test:
    name: 添加个人签名域-actorIndentityType 为0
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "0",signerAccountId: $account_id,"authorizedAccountId": $account_id,"assignedPosbean": false,"signDateBeanType":"1","sealBizTypes": "CONTRACT,PUBLIC", "signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 企业]

- test:
    name: 添加个人签名域-actorIndentityType 为3
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "3",signerAccountId: $account_id,"authorizedAccountId": $account_id,"assignedPosbean": false,"signDateBeanType":"1","sealBizTypes": "CONTRACT,PUBLIC", "signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 企业]

- test:
    name: 添加个人签名域-actorIndentityType 为4
    variables:
      - flowId: $sign_flowId
      - signfields: [{fileId: $fileId,"actorIndentityType": "4",signerAccountId: $account_id,"authorizedAccountId": $account_id,"assignedPosbean": false,"signDateBeanType":"1","sealBizTypes": "CONTRACT,PUBLIC", "signDateBean":{posPage: "1", posX: 200,posY: 200},order: 1,posBean: {posPage: "1", posX: 200,posY: 200},sealType: "",sealId: "", signType: 1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message", 企业]

- test:
    name: 流程开启
    variables:
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 获取流程印章/CONTRACT,PUBLIC
    variables:
      - flowId: $sign_flowId
      - loginAccountId: $account_id
      - signerAccountId: $account_id
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - response: content.data
    validate:
      - eq: [status_code, 200]
      - contains: ["${toString($response)}", CONTRACT]
      - contains: ["${toString($response)}", PUBLIC]
      - eq: ["${isContainKey($response,LEGAL_PERSON)}", False]
      - eq: ["${isContainKey($response,FINANCE)}", False]
      - eq: ["${isContainKey($response,INVOICE)}", False]
      - eq: ["${isContainKey($response,PERSONNEL)}", False]