- config:
    name: "0528接口测试用例"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(xuanyuan_realname)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: "xuanyuan_realname下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建机构1"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - thirdPartyUserId: sixian111120133911141132
      - creator: $caogu_accountId
      - idNumber: 92341324MA2NX3TR55
      - idType: CRED_ORG_USCC
      - name: 泗县梁贤红乐福超市
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixianchaoshi_organize_id: content.data.orgId


- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $standard_sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - org_sealId4: org_sealId4
      - org_all: org_all
      - list1: list1
      - list2: list2
      - list3: list3
      - leg_org: leg_org
      - legal_sealId1: legal_sealId1
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "获取泗县超市对应的实名组织详情"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $sixianchaoshi_organize_id
    extract:
      - standard_sixianchaoshi_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals_chaoshi($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $standard_sixianchaoshi_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - list11: list11
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业印章"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_open_sealId1: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "根据登陆凭证获取accountId"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - idcard: ***********
      - type: MOBILE
    api: api/iterate_cases/getByIdcard.yml
    extract:
      - stu_accountId: content.data.items.0.accountId

- test:
    name: "创建签署流程-一个签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个企业签署区-无sealIds和sealId"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表-返回所有包含企业和法人的印章"
    teardown_hooks:
      - ${teardown_seals_total($response,$org_all)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
#      - eq: ["isPass",True]

- test:
    name: "创建签署流程-一个签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个企业签署区-有sealIds"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds":$list1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表"
    teardown_hooks:
      - ${teardown_seals_list($response,$list1)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId1
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["isPass",True]

- test:
    name: "创建签署流程-一个签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId2
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "一个企业签署区-有sealId"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId2
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"signType":1,"sealId":$org_sealId1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message",'参数错误: 实名签署不允许指定印章id']

###############两个签署区-同一顺序#####################################
- test:
    name: "创建签署流程-两个签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId3
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "两个企业签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId3
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1},{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId3
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表-返回所有包含企业和法人的印章"
    teardown_hooks:
      - ${teardown_seals_total($response,$org_all)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId3
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
#      - eq: ["isPass",True]

- test:
    name: "创建签署流程-两个签署区-签署区A：-签署区B：seals[a,b]"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId4
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "两个个企业签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId4
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1},{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1,sealIds: $list1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId4
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表-返回所有包含企业和法人的印章"
    teardown_hooks:
      - ${teardown_seals_total($response,$org_all)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId4
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
#      - eq: ["isPass",True]

- test:
    name: "创建签署流程-两个签署区-签署区A：[b,c]-签署区B：seals[a,b]"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId5: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId5
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "两个个企业签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId5
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds": $list2},{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1,sealIds: $list1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId5
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表-返回a,b,c"
    teardown_hooks:
      - ${teardown_seals_list($response,$list3)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId5
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["isPass",True]

###############两个签署区-不同顺序#####################################
- test:
    name: "创建签署流程-两个签署区-签署区A：[b,c]-签署区B：seals[a,b]，不同顺序"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId6: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId6
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "两个个企业签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId6
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":2,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds": $list2},{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1,sealIds: $list1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId6
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表"
    teardown_hooks:
      - ${teardown_seals_list($response,$list1)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId6
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["isPass",True]

###############两个签署区-一个法人加一个企业签署区#####################################
- test:
    name: "创建签署流程-两个签署区-签署区A：-签署区B：seals[a,b]，同一顺序"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId7: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId7
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "两个个企业签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId7
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"3","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1},{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1,sealIds: $list1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId7
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表"
    teardown_hooks:
      - ${teardown_seals_list_leg($response,$leg_org)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId7
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["isPass",True]

###############两个签署区-多主体#####################################
- test:
    name: "创建签署流程-企业A list1,企业B list11，同一顺序"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - autoArchive: true
      - businessScene: 轩辕实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId8: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId8
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "两个个企业签署区"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId8
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"sealId":"","signType":1,"sealIds": $list1},{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixianchaoshi_organize_id,"assignedPosbean":true,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":300,"posY":400},"sealType":null,"sealId":"","signType":1,sealIds: $list11}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId8
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询可用的印章列表"
    teardown_hooks:
      - ${teardown_seals_list_two($response,$list1,$list11)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
      - flowId: $sign_flowId8
      - accountId: $stu_accountId
      - authorizerId: $standard_sixian_organize_id
      - signerAccountId: $stu_accountId
    api: api/iterate_cases/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["isPass",True]






