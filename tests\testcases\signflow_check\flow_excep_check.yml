- config:
    name: 拒签、撤销、过期场景，以及不同流程状态下文档、签署区、附件的操作校验
    base_url: ${ENV(base_url)}
    variables:
      - orgName: 东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: 13344445555
      - path_pdf: data/个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - personOid1: 4468fe9323bc4bfeaf5f659af14b6754
      - personOid2: 824708b5195546eab28565ebb0203a1e
      - personOid3: 310913bf68594f8bb7c457215e0b4831
      - orgOid1: 4d4352cd0de849ab9daa507397086bf1
      - fileId1: 37492a3be0c64282b834a48f77db3c89
      - m1: "19922222222"
      - m2: "19800000000"
      - e1: <EMAIL>
      - e2: <EMAIL>
      - pOid1: ${getOid($m1)}
      - pOid2: ${getOid($m2)}
      - pOid3: ${getOid($e1)}
      - tPUserId: autotest${get_randomNo()}
      - contractValidity:
      - signValidity:
      - m3: "13260881366"
      - pOid_m: ${getOid($m3)}
      - tPUserId1: autotest1${get_randomNo()}
      - appid_xuanyuan: ${ENV(appId_xuanyuan)}
      - m4: "18767104153"
      - pOid_m1: ${getOid($m4)}
      - client_id: pc
- test:
    name: 查询企业
    variables:
      - name: esigntest东营伟信建筑安装工程有限公司
    api: api/user/organizations/get_organizations_by_name.yml
    extract:
      - orgOid2: content.data.items.0.ouid

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 获取上传文件url
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
      - accountId: $personOid1
    api: api/signflow/flowSigners/get_uploadUrl1.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 上传文件到oss
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: 过期场景——创建流程
    variables:
      - businessScene: "创建流程"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs_2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1,$docs_2]
      - signValidity: ${get_timestamp_13(0)}
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_expire: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 过期场景——添加流程签署区
    variables:
      - flowId: $flowId_expire
      - posBean1: {addSignTime: True, key: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - posBean2: {}
      - posBean3: {addSignTime: False, key: '', posPage: '1,2', posX: 0, posY: 600, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_data_V2(1,0,$fileId1,$flowId_expire,$posBean1,1,$pOid_m,$orgOid2,,)}
      - signfield2: ${gen_signfield_data_V2(2,0,$fileId2,$flowId_expire,$posBean2,0,$pOid_m,$orgOid2,,)}
      - signfield3: ${gen_signfield_data_V2(1,0,$fileId1,$flowId_expire,$posBean3,2,$pOid_m1,$orgOid2,,)}
      - signfields: [$signfield1,$signfield2,$signfield3]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId_expire_1: content.data.signfieldIds.0
      - signfieldId_expire_2: content.data.signfieldIds.1
      - signfieldId_expire_3: content.data.signfieldIds.2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 过期场景——开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId_expire
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 数据准备-流程草稿状态：创建流程和文档
    variables:
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - autoArchive: False
      - docs_2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_2]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_draft: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 流程状态=草稿，允许添加文档
    variables:
      - flowId: $flowId_draft
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=草稿，允许更新文档
    variables:
      - flowId: $flowId_draft
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne改个名字', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/signflow/flowDocuments/documentsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=草稿，允许删除文档
    variables:
      - flowId: $flowId_draft
      - fileIds: $fileId1
    api: api/signflow/flowDocuments/documentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=草稿，允许全量替换文档
    variables:
      - flowId: $flowId_draft
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne替换', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/signflow/flowDocuments/documentsReplace.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询流程文档-替换后的文档
    api: api/signflow/flowDocuments/documentsSelect.yml
    variables:
      - flowId: $flowId_draft
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.docs",1]
      - eq: ["content.data.docs.0.fileId",$fileId1]

- test:
    name: 数据准备-流程草稿状态：添加流程签署区
    variables:
      - flowId: $flowId_draft
      - posBean: {}
      - autoExecute: 0
      - signfield1: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$pOid_m,,,)} #个人签
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: 流程状态=草稿，允许添加签署区
    variables:
      - flowId: $flowId_draft
      - posBean: {}
      - autoExecute: 0
      - signfield2: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$pOid_m,,,)} #个人签
      - signfields: [$signfield2]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId2: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: 流程状态=草稿，允许更新签署区
    variables:
      - flowId: $flowId_draft
      - posBean: {addSignTime: True, key: '', posPage: 1, posX: 200, posY: 400, qrcodeSign: True, width: 0}
      - autoExecute: 0
      - signfield2: ${gen_signfield_update_data($pOid_m, , $posBean, ,,$signfieldId2)}
      - signfields: [$signfield2]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 流程状态=草稿，允许删除签署区
    variables:
      - flowId: $flowId_draft
      - signfieldId: $signfieldId2
    api: api/signflow/signfields/signfieldsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.deleteResults.0.optResult",0]

- test:
    name: 流程状态=草稿，允许添加附件
    variables:
      - flowId: $flowId_draft
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=草稿，允许更新附件
    variables:
      - flowId: $flowId_draft
      - attachments: [{"attachmentName": "附件1改个名字.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=草稿，允许全量替换附件，允许为空
    variables:
      - flowId: $flowId_draft
      - attachments: []
    api: api/signflow/flowAttachments/attachmentsReplace.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=草稿，查询附件列表
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - flowId: $flowId_draft
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",0]

- test:
    name: 流程状态=草稿，允许全量替换附件
    variables:
      - flowId: $flowId_draft
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1},{"attachmentName": "附件2.pdf", "fileId": $fileId2}]
    api: api/signflow/flowAttachments/attachmentsReplace.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=草稿，查询附件列表
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - flowId: $flowId_draft
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",2]

- test:
    name: 流程状态=草稿，允许删除附件
    variables:
      - flowId: $flowId_draft
      - fileIds: $fileId1
    api: api/signflow/flowAttachments/attachmentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId_draft
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=签署中，不允许添加文档
    variables:
      - flowId: $flowId_draft
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程非草稿状态，不允许添加文档']

- test:
    name: 流程状态=签署中，不允许更新文档
    variables:
      - flowId: $flowId_draft
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo改个名字', 'filePassword': '', 'encryption': 0}
      - docs: [$doc2]
    api: api/signflow/flowDocuments/documentsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程非草稿状态，不允许更新文档']

- test:
    name: 流程状态=签署中，不允许删除文档
    variables:
      - flowId: $flowId_draft
      - fileIds: $fileId1
    api: api/signflow/flowDocuments/documentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程非草稿状态，不允许删除文档']

- test:
    name: 流程状态=签署中，不允许全量替换文档
    variables:
      - flowId: $flowId_draft
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc2]
    api: api/signflow/flowDocuments/documentsReplace.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程非草稿状态，不允许全量替换签署文档']

- test:
    name: 流程状态=签署中，允许添加签署区
    variables:
      - flowId: $flowId_draft
      - posBean: {}
      - autoExecute: 0
      - signfield3: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$pOid_m,,,)} #个人签
      - signfields: [$signfield3]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId3: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: 流程状态=签署中，允许更新签署区
    variables:
      - flowId: $flowId_draft
      - posBean: {addSignTime: True, key: '', posPage: 1, posX: 200, posY: 400, qrcodeSign: True, width: 0}
      - autoExecute: 0
      - signfield4: ${gen_signfield_update_data($pOid_m, , $posBean, ,,$signfieldId3)}
      - signfields: [$signfield4]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.updateResults.0.failedReason",null]

- test:
    name: 流程状态=签署中，允许删除签署区
    variables:
      - flowId: $flowId_draft
      - signfieldId: $signfieldId3
    api: api/signflow/signfields/signfieldsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.deleteResults.0.optResult",0]

- test:
    name: 流程状态=签署中，允许添加附件
    variables:
      - flowId: $flowId_draft
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=签署中，允许更新附件
    variables:
      - flowId: $flowId_draft
      - attachments: [{"attachmentName": "附件1改个名字.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=签署中，不允许全量替换附件
    variables:
      - flowId: $flowId_draft
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsReplace.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'流程非草稿状态，不允许全量替换附件']

- test:
    name: 流程状态=签署中，查询附件列表
    api: api/signflow/flowAttachments/attachmentsSelect.yml
    variables:
      - flowId: $flowId_draft
    validate:
      - eq: [status_code, 200]
      - len_eq: ["content.data.attachments",2]

- test:
    name: 流程状态=签署中，允许删除附件
    variables:
      - fileIds: $fileId1
      - flowId: $flowId_draft
    api: api/signflow/flowAttachments/attachmentsDelete.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'流程非草稿状态，不允许删除附件']

- test:
    name: 执行流程签署区
    variables:
      - flowId: $flowId_draft
      - signfieldIds: [$signfieldId1]
      - accountId: $pOid_m
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]

- test:
    name: 流程状态=签署中，签署区=签署完成，不允许删除签署区
    variables:
      - flowId: $flowId_draft
      - signfieldId: $signfieldId1
    api: api/signflow/signfields/signfieldsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.deleteResults.0.optResult",1]
      - eq: ["content.data.deleteResults.0.failedReason",'该签署区不允许删除']

- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId_draft
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=签署完成，不允许添加文档
    variables:
      - flowId: $flowId_draft
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'流程非草稿状态，不允许添加文档']

- test:
    name: 流程状态=签署完成，不允许添加签署区
    variables:
      - flowId: $flowId_draft
      - posBean: {}
      - autoExecute: 0
      - signfield3: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$pOid_m,,,)} #个人签
      - signfields: [$signfield3]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'流程已完结']

- test:
    name: 流程状态=签署完成，不允许添加附件
    variables:
      - flowId: $flowId_draft
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'流程已完结，不允许添加附件']

- test:
    name: 数据准备-流程草稿状态：创建流程和文档
    variables:
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_draft2: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId_draft2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 撤销流程
    api: api/signflow/signflows/signflowsRevoke.yml
    variables:
      - flowId: $flowId_draft2
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=撤销，不允许添加文档
    variables:
      - flowId: $flowId_draft2
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程非草稿状态，不允许添加文档']

- test:
    name: 流程状态=撤销，不允许添加签署区
    variables:
      - flowId: $flowId_draft2
      - posBean: {}
      - autoExecute: 0
      - signfield3: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$pOid_m,,,)} #个人签
      - signfields: [$signfield3]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程已完结']

- test:
    name: 流程状态=撤销，不允许添加附件
    variables:
      - flowId: $flowId_draft2
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程已完结，不允许添加附件']

- test:
    name: 数据准备-流程草稿状态：创建流程和文档
    variables:
      - businessScene: "创建流程并添加文档"
      - createWay: "normal"
      - autoArchive: False
      - docs_1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$docs_1]
      - contractValidity:
      - initiatorAccountId:
      - payerAccountId:
      - signValidity:
    api: api/signflow/aggregate_sign/createWithSignDocs.yml
    extract:
      - flowId_draft3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 数据准备-流程草稿状态：添加流程签署区
    variables:
      - flowId: $flowId_draft3
      - posBean: {}
      - autoExecute: 0
      - signfield1: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$pOid_m,,,)} #个人签
      - signfields: [$signfield1]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId6: content.data.signfieldIds.0
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.signfieldIds",0]

- test:
    name: 开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId_draft3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 拒签流程
    variables:
      - flowId: $flowId_draft3
      - refuseReason: '拒签不需要理由No Reason'
      - signfieldId: $signfieldId6
    api: api/signflow/signfields/signfieldsRefuse.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 流程状态=拒签，不允许添加文档
    variables:
      - flowId: $flowId_draft3
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",'流程非草稿状态，不允许添加文档']

- test:
    name: 流程状态=拒签，不允许添加签署区
    variables:
      - flowId: $flowId_draft3
      - posBean: {}
      - autoExecute: 0
      - signfield3: ${gen_signfield_data_V2(0,$autoExecute,$fileId1,$flowId,$posBean,0,$pOid_m,,,)} #个人签
      - signfields: [$signfield3]
    api: api/signflow/signfields/signfieldsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'流程已完结']

- test:
    name: 流程状态=拒签，不允许添加附件
    variables:
      - flowId: $flowId_draft3
      - attachments: [{"attachmentName": "附件1.pdf", "fileId": $fileId1}]
    api: api/signflow/flowAttachments/attachmentsCreate.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message",'流程已完结，不允许添加附件']

#由于过期定期任务可能最长要10分钟，这个先注释掉
- test:
    name: 过期场景——查询流程详情：已过期
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId_expire
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
#      - eq: ["content.data.flowStatus",5]