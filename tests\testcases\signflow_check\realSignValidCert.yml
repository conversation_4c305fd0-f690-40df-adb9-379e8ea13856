- config:
    name: 署过程校验签署主体是否有有效证书
    ###def: one_businessSign()
    base_url: ${ENV(base_url)}
    variables:
      - flowId: c5276a59cc9547ca8650c0929ecce9b8
      - app_id: ${ENV(xuanyuan_realname)}
    "request":
      "base_url": ""
      "headers":
        "Content-Type": "application/json"

- test:
    name: 签署主体存在有效证书
    variables:
      - json: {
        "accountId": "116a184fb65646309c0fc5ed1d9eb4b6",
        "hasHandWrite": false,
        "operatorId": "",
        "sealIds": []
      }
    api: api/sign-flow/realSignValidCert.yml

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.showTip", false]

