- api:
    def: create_organizations($accountId,$name,$orgType,$orgCode,$legalName,$legalIdNo)         #创建组织
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/createOverall
      method: POST
      headers: ${get_headers($app_id)}
      json:
        creater: $accountId
        credentials:
          orgCode: null
          regCode: null
          usccCode: null
        idcards:
          organ:
            type: $orgType
            value: $orgCode
          projThirdparty:
            thirdpartyUserId: ''
            thirdpartyUserType: ''
          thirdparty:
            thirdpartyKey: ''
            thirdpartyUserId: ''
            thirdpartyUserType: ''
        properties:
          agentIdno: ''
          agentName: ''
          legalIdno: $legalIdNo
          legalName: $legalName
          name: $name
        source: {}


- api:
    def: delete_organizations($organId)            #注销组织账号
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/$organId/deleteOrg
      method: POST
      headers: ${get_headers($app_id)}


- api:
    def: get_organizations($organId)               #查询组织
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/$organId/getOrg
      method: GET
      headers: ${get_headers($app_id)}


- api:
    def: add_members_into_organizations($orgId,$organCode,$organType,$applierAccountId)            #加入成员
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/join
      method: PUT
      headers: ${get_headers($app_id)}
      json:
        {
          "accountId": $orgId, #组织oid
          "applierAccountId": $applierAccountId, #申请人oid
          "isAcceptApplyJoin": false, #是否是管理员同意用户申请
          "organCode": $organCode,
          "organType": $organType
        }

#- api:
#    def: delete_members_from_organizations($accountId,$idNos)            #删除成员
#    request:
#        url: /api/v1/organizations/$accountId/members
#        method: DELETE
#        headers: ${get_headers($app_id)}
#        json:
#        idNos: idNos  #demo  [idNo1,idNo2]
#
- api:
    def: update_organizations($accountId, $email,$legalName,$name,$mobile)            #更新组织
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/$accountId/organizations
      method: PUT
      headers: ${get_headers($app_id)}
      json:
        contacts: {"email": $email, "mobile": $mobile}
        properties: {"name": $name, "legalName": $legalName}
