- config:
    name: 简化版多文档场景：对接平台自动签署-用户自动签署-用户手动签署
    variables:
      - client_id: pc
- test:
    name: 简化版多文档场景：创建流程
    variables:
      - autoArchive: False
      - businessScene: 添加签署区拆分场景
      - client_id: pc
      - configInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId4: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 简化版多文档场景：添加流程文档
    variables:
      - flowId: $flowId4
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1,$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：查询平台印章
    api: api/seals/seals/select_plat_seals.yml
    extract:
      - plat_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版多文档场景：对接平台自动签署
    variables:
      - flowId: $flowId4
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield1: { 'fileId':$fileId1, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':2}
      - signfield2: { 'fileId':$fileId2, 'order':1,  'posBean':$posBean, 'sealId':$plat_sealId,'signType':2}
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsCreate_platAuto.yml
    extract:
      - signfieldId1: content.data.signfieldBeans.0.signfieldId
      - signfieldId2: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：对企业账号静默授权
    variables:
      - accountId: $orgOid1
      - type: silent
      - deadline:
    api: api/signflow/signAuth/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询个人印章列表-多文档：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $orgOid1
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版多文档场景：用户自动签署,骑缝签署
    variables:
      - flowId: $flowId4
      - posBean: {'posPage':'1','posX':300, 'posY':400}
      - signfield3: {'fileId':$fileId1,'authorizedAccountId':$orgOid1, 'order':1,  'posBean':$posBean, 'sealId':$o_sealId,'signType':2}
      - signfield4: {'fileId':$fileId2,'authorizedAccountId':$orgOid1, 'order':1,  'posBean':$posBean, 'sealId':$o_sealId,'signType':2}
      - signfields: [$signfield3,$signfield4]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    extract:
      - signfieldId3: content.data.signfieldBeans.0.signfieldId
      - signfieldId4: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：添加用户手动签署
    variables:
      - flowId: $flowId4
      - posBean: {'posPage':'1','posX':300, 'posY':600}
      - signfield5: {'fileId':$fileId1, 'signerAccountId':$personOid2,'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':2}
      - signfield6: {'fileId':$fileId2, 'signerAccountId':$personOid2, 'actorIndentityType':'0', 'authorizedAccountId':$personOid2,'assignedPosbean':True, 'order':1,  'posBean':$posBean, 'sealType':'','signType':2}
      - signfields: [$signfield5,$signfield6]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId5: content.data.signfieldBeans.0.signfieldId
      - signfieldId6: content.data.signfieldBeans.1.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版多文档场景：更新签署区
    variables:
      - flowId: $flowId4
      - posBean4: {'posPage':'1','posX':300, 'posY':600}
      - signfield5: ${gen_signfield_update_data($personOid2, , $posBean4, ,$p2_sealId,$signfieldId5)}
      - signfield6: ${gen_signfield_update_data($personOid2, , $posBean4, ,$p2_sealId,$signfieldId6)}
      - signfields: [$signfield5,$signfield6]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 简化版多文档场景：执行流程签署区
    variables:
      - flowId: $flowId4
      - signfieldIds: [$signfieldId5,$signfieldId6]
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]
      - eq: ["content.data.executeResults.1.optResult",0]

- test:
    name: 简化版多文档场景：查询签署区-签署完成
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
      - accountId:
      - signfieldIds: ""
      - flowId: $flowId4
    extract:
      - s_signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId3, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId4, $expectedKey,4)}",True]

- test:
    name: 简化版多文档场景：归档
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 简化版多文档场景：查询流程-已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]

- test:
    name: 简化版多文档场景：获取签署文档
    variables:
      - flowId: $flowId4
      - accountId: $personOid1
      - organizeId:
      - urlType: 0
    api: api/signflow/signflows/getSignUrl.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 取消签署授权:企业账户
    variables:
      - accountId: $orgOid1
      - type: silent
    api: api/signflow/signAuth/signAuth_delete.yml
    validate:
      - eq: [status_code, 200]
