- config:
    name: "签署计费改造"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: "**********"
      - fileId1: ${get_file_id($app_id,$path_pdf1)}
      - imagfileId: "5d134d1a5033458293a122bded604950"

- test:
    name: "创建签署人账号 - gan"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: z111zzzzzzzzzzzzzzzz
      - name: 甘舰戈
      - idNumber: ******************
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_gan: content.data.accountId
      - yuzan_accountId: content.data.accountId
- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "使用caogu_accountId创建机构"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId



- test:
    name: "获取对应的实名组织详情"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - orgId: $sixian_organize_id
    extract:
      - standard_sixian_organize_id: content.data.base.ouid
    api: api/iterate_cases/getFinalRealnameOrgInfo.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    teardown_hooks:
      - ${teardown_seals($response)}
    name: "查询实名组织下的企业印章"
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - orgId: "8fbe5926547e40149978fb8c5a448394"
    api: api/seals/seals/select_c_seals.yml
    extract:
      - org_sealId1: org_sealId1
      - org_sealId2: org_sealId2
      - org_sealId3: org_sealId3
      - legal_sealId1: legal_sealId1
      - cancellation_sealId: cancellation
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]



- test:
    name: 查询个人印章列表：使用查询到的印章进行签署
    api: api/seals/seals/select_p_seals.yml
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - accountId: $yuzan_accountId
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]


- test:
    name: 一步发起指定证书id用户手动签-成功
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - doc: {encryption: 0,fileId: $fileId1, fileName: '文件1', source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: "autotest",flowConfigInfo: { batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", signPlatform: "1,2" } }
      - signers:  [
      {
        "signOrder": 1,
        "signerAccount":
          {
            "signerAccountId": $yuzan_accountId,
            "authorizedAccountId": $sixian_organize_id
          },
        "signfields":
          [
          {
            "autoExecute": false,
            "actorIndentityType": 2,
            "signerRoleType": "2",
            "certId": "",
            "fileId": $fileId1,
            "sealType": "",
            "posBean":
              {
                "posPage": "1",
                "posX": 300,
                "posY": 300
              },
            "signDateBeanType": "2",
            "signDateBean":{"fontSize":50,"posX":220,"posY":250,"posPage": "1"},
            "signType": 1

          }
          ],
        "thirdOrderNo": "企业2"
      }
      ]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - sign_flow_one: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",'成功']

- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flow_one
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 查询签署区
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - flowId: $sign_flow_one
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200] #content.data.signfields.0.signerAccountId
    extract:
      - signfieldId0: content.data.signfields.0.signfieldId

- test:
    name: 查询流程配置
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - flowId: $sign_flow_one
    api: api/signflow/signflows/signflowsConfig.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code",200]
      - eq: ["content.data.signBillingWay",1]





- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: ${ENV(gan_appid_xuanyuan)}
      - flowId: $sign_flow_one
      - accountId: "3986d7544d9848f186ee9c5dcd342de1"
      - sealId: $p1_sealId
      - addSignfield:  {signfieldId: $signfieldId0,"fileId":$fileId1,"signerAccountId":"3986d7544d9848f186ee9c5dcd342de1","actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "第一个用户手动签，第一个用户手动签"},"sealType":"","sealId":$org_sealId1,"signType":1,signerOperatorId: "3986d7544d9848f186ee9c5dcd342de1",signerOperatorAuthorizerId: "8fbe5926547e40149978fb8c5a448394"}
      - addSignfields: []
      - updateSignfields: [$addSignfield]
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: []
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.message","成功"]

- test:
    name: "创建签署流程-分步流程"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - autoArchive: false
      - businessScene: 悟空非实名签校验add-update字段
      - configInfo: {batchDropSeal: true, noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', signPlatform: '',redirectUrl: "http://www.baidu.com"}
      # redirectUrl: "http://www.baidu.com",
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $accountId_gan
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flow_one: content.data.flowId
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


- test:
    name: "添加1份流程文档-分步流程"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]



- test:
    name: "个人-添加成功个人签名域-指定签名域 可以添加位置信息-分步流程"
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flowId
      - signfields: [
      { "signDateBeanType": "2","signDateBean": { "fontSize": 50,"posX": 220,"posY": 150 },"posBean":{"addSignTime":false,"posPage":"1","posX":200,"posY":500,"location": "创建第一个手动签署区创建第一个手动签署区"},"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"sealType":null,"sealId":"","signType":2}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]


- test:
    name: "开启签署流程-分步流程"
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 添加或更新签署区并执行签署-分步流程
    variables:
      - app_id: ${ENV(gan_appid_download)}
      - flowId: $sign_flow_one
      - accountId: "3986d7544d9848f186ee9c5dcd342de1"
      - sealId: $p1_sealId
      - addSignfield:  {signfieldId: $signfieldId0,"fileId":$fileId1,"signerAccountId":"3986d7544d9848f186ee9c5dcd342de1","actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":100,"location": "第一个用户手动签，第一个用户手动签"},"sealType":"","sealId":$org_sealId1,"signType":1,signerOperatorId: "3986d7544d9848f186ee9c5dcd342de1",signerOperatorAuthorizerId: "8fbe5926547e40149978fb8c5a448394"}
      - addSignfields: []
      - updateSignfields: [$addSignfield]
      - dingUser: {}
      - approvalPolicy: 0
      - signfieldIds: []
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - eq: ["content.message","成功"]