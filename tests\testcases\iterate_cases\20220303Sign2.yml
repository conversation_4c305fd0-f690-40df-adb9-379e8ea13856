- config:
    name: "更新流程参与人"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ${ENV(envCode)}
      - app_id: ${ENV(xuanyuan_realname)}
      - fileId1: ${get_file_id($app_id,$path_pdf)}
      - mobile_fengjiu: ***********
      - mobile_caogu: ***********
      - sealId1: 8fa71acc-425d-4270-9fa7-b0e91f57fbef

- test:
    name: "创建钟灵账号"
    variables:
      - thirdPartyUserId: ckl201911141994
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId


- test:
    name: "创建草谷账号"
    variables:
      - app_id: $write_app_Id
      - group: $w_group
      - thirdPartyUserId: caogu201911141132
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId


- test:
    name: "创建甘宁账号"
    variables:
      - thirdPartyUserId: yre00152224212341
      - name: 甘舰戈
      - idNumber: ******************
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ganning: content.data.accountId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司1"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying2012112
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying1_organize_id: content.data.orgId



- test:
    name: createFlowOneStep - "多个签署人"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: [{ copierAccountId: $accountId_ckl, copierIdentityAccountId: $dongying1_organize_id, copierIdentityAccountType: 2},{ copierAccountId: $caogu_accountId, copierIdentityAccountId: $caogu_accountId, copierIdentityAccountType: 0}]
      - flowInfo: { autoArchive: true, autoInitiate: true, initiatorAccountId: $accountId_ckl, initiatorAuthorizedAccountId: $dongying1_organize_id,businessScene: " createFlowOneStep - 多个签署人",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $caogu_accountId,authorizedAccountId: $dongying1_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 },{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $dongying1_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]




#更新参与人信息-异常case
- test:
    name: 更新参与人信息-异常1-updateAccountId不传
    variables:
      - json: {
        "flowId": $flowId1,
        "originalAccountId": $accountId_ckl,
        "updateAccountGid": "",
        "updateAccountId": "",
        "updateAccountUid": "1234"
      }
    api: api/iterate_cases/updateFlowAccount.yml
    validate:
      - eq: [status_code, 400]
      - eq: ["content.success",False]
      - contains: ["content.message", "updateAccountId不能为空"]

- test:
    name: 更新参与人信息-异常2-originalAccountId不传
    variables:
      - json: {
        "flowId": $flowId1,
        "originalAccountId": "",
        "updateAccountGid": "",
        "updateAccountId": "2334efd",
        "updateAccountUid": "1234"
      }
    api: api/iterate_cases/updateFlowAccount.yml
    validate:
      - eq: [status_code, 400]
      - eq: ["content.success",False]
      - contains: ["content.message", "原始账号id不能为空"]

- test:
    name: 更新参与人信息-异常3-originalAccountId不存在
    variables:
      - json: {
        "flowId": $flowId1,
        "originalAccountId": $accountId_ganning,
        "updateAccountGid": "",
        "updateAccountId": "2334efd",
        "updateAccountUid": "1234"
      }
    api: api/iterate_cases/updateFlowAccount.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "执行成功"]

- test:
    name: 查询流程参与人信息
    variables:
      - flowId: $flowId1
      - queryAccountId: $accountId_ckl
    api: api/signflow/aggregate_sign/select_detail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.code", 0]
      - eq: ["content.data.initiatorAccountId", $accountId_ckl]
      - eq: ["content.data.initiatorAuthorizedAccountId", $dongying1_organize_id]

      - contained_by: ["content.data.signers.0.signerAccountId", [$caogu_accountId,$accountId_ckl]]
      - contained_by: ["content.data.signers.0.signerAuthorizedAccountId", [$dongying1_organize_id]]
      - contained_by: ["content.data.signers.1.signerAccountId", [$caogu_accountId,$accountId_ckl]]
      - contained_by: ["content.data.signers.1.signerAuthorizedAccountId", [$dongying1_organize_id]]

      - contained_by: ["content.data.recipients.0.recipientAccountId", [$caogu_accountId,$accountId_ckl]]
      - contained_by: ["content.data.recipients.0.recipientIdentityAccountId", [$caogu_accountId,$dongying1_organize_id]]
      - contained_by: ["content.data.recipients.1.recipientAccountId", [$caogu_accountId,$accountId_ckl]]
      - contained_by: ["content.data.recipients.1.recipientIdentityAccountId", [$caogu_accountId,$dongying1_organize_id]]


#更新参与人信息 正向场景
- test:
    name: 更新参与人信息
    variables:
      - json: {
        "flowId": $flowId1,
        "originalAccountId": $accountId_ckl,
        "updateAccountGid": "",
        "updateAccountId": "2334efd",
        "updateAccountUid": "1234"
      }
    api: api/iterate_cases/updateFlowAccount.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "执行成功"]


- test:
    name: 查询流程参与人信息
    variables:
      - flowId: $flowId1
      - queryAccountId: $accountId_ckl
    api: api/signflow/aggregate_sign/select_detail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.code", 0]
      - eq: ["content.data.initiatorAccountId", 2334efd]
      - eq: ["content.data.initiatorAuthorizedAccountId", $dongying1_organize_id]

      - contained_by: ["content.data.signers.0.signerAccountId", [$caogu_accountId,2334efd]]
      - contained_by: ["content.data.signers.0.signerAuthorizedAccountId", [$dongying1_organize_id]]
      - contained_by: ["content.data.signers.1.signerAccountId", [$caogu_accountId,2334efd]]
      - contained_by: ["content.data.signers.1.signerAuthorizedAccountId", [$dongying1_organize_id]]

      - contained_by: ["content.data.recipients.0.recipientAccountId", [$caogu_accountId,2334efd]]
      - contained_by: ["content.data.recipients.0.recipientIdentityAccountId", [$caogu_accountId,$dongying1_organize_id]]
      - contained_by: ["content.data.recipients.1.recipientAccountId", [$caogu_accountId,2334efd]]
      - contained_by: ["content.data.recipients.1.recipientIdentityAccountId", [$caogu_accountId,$dongying1_organize_id]]


#开启流程
- test:
    name: 开启流程
    variables:
      - flowId: $flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 更新参与人信息
    variables:
      - json: {
        "flowId": $flowId1,
        "originalAccountId": $caogu_accountId,
        "updateAccountGid": "",
        "updateAccountId": $accountId_ganning,
        "updateAccountUid": "23fghjhjhgf"
      }
    api: api/iterate_cases/updateFlowAccount.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "执行成功"]


- test:
    name: 查询流程参与人信息
    variables:
      - flowId: $flowId1
      - queryAccountId: $accountId_ckl
    api: api/signflow/aggregate_sign/select_detail.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.code", 0]
      - eq: ["content.data.initiatorAccountId", 2334efd]
      - eq: ["content.data.initiatorAuthorizedAccountId", $dongying1_organize_id]

      - contained_by: ["content.data.signers.0.signerAccountId", [$accountId_ganning,2334efd]]
      - contained_by: ["content.data.signers.0.signerAuthorizedAccountId", [$dongying1_organize_id]]
      - contained_by: ["content.data.signers.1.signerAccountId", [$accountId_ganning,2334efd]]
      - contained_by: ["content.data.signers.1.signerAuthorizedAccountId", [$dongying1_organize_id]]

      - contained_by: ["content.data.recipients.0.recipientAccountId", [$accountId_ganning,2334efd]]
      - contained_by: ["content.data.recipients.0.recipientIdentityAccountId", [$accountId_ganning,$dongying1_organize_id]]
      - contained_by: ["content.data.recipients.1.recipientAccountId", [$accountId_ganning,2334efd]]
      - contained_by: ["content.data.recipients.1.recipientIdentityAccountId", [$accountId_ganning,$dongying1_organize_id]]

