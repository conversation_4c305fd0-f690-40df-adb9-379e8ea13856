- config:
    name: 区块链无证书签
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS)} #app_id未配，无发起权限
      - account_id: ${ENV(tengqing_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_id: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}

- test:
    name: app_id 未配置，无发起权限，返回错误
    variables:
      - json: {"signers":[{"signOrder":0,"signerAccount":{"noticeType":"","signerAccountId":"$account_id"},"signfields":[{"assignedPosbean":true,"fileId":"$file_id","posBean":{"posPage":"1","posX":306,"posY":48},"signType":0,"width":200}],"thirdOrderNo":"1111"}],"docs":[{"bizFileId":"","encryption":0,"fileHash":"","fileId":"$file_id","fileName":"签署文件.pdf","filePassword":"","source":0}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签签署署${get_randomNo()}","contractRemind":0,"flowConfigInfo":{},"flowConfigItemBean":{"buttonConfig":{"propButton":""}},"hashSign":false,"initiatorAccountId":"$account_id","remark":""}}
    api: api/signflow/signflows/ecloudflowsCreateFlow.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.code, 1437196]
      - contains: [content.message, 当前应用配置不允许发起无意愿无证书协议云流程]
