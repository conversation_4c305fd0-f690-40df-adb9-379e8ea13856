import json
from datetime import datetime
from untils import 吊销证书
import requests
def 批量签链接(主体个数=1,是否注销证书=False,是否项目环境=1,appid版本=1):
    if appid版本 == 1:
        appid = "7876722740"
    elif appid版本 == 2:
        appid = "7876722860"
    elif appid版本 == 3:
        appid = "7876722899"
    else:
        appid = "7876722915"

    headers = {
        'X-Tsign-Open-App-Id': appid,
        'X-Tsign-Open-Auth-Mode': 'simple',
        'Content-Type': 'application/json;charset=UTF-8'
    }
    if 是否项目环境 == 1:
        headers['X-Tsign-Service-Group'] = 'eSignCA'
    else:
        pass
    flowIdList = []
    #个人签署




    oidList = ["3986d7544d9848f186ee9c5dcd342de1",'a97780e49e1f48f8bd3a1e87af0cd2fe', '90fc72de45174e66b70653c7ec1f7aae']
    #获取签署的flowid

    for oid in oidList:
        if 是否注销证书:
            吊销证书(oid)
        else:
            pass

        url = 'http://in-test-openapi.tsign.cn/api/v3/signflows/createFlowOneStep'
        data = {
            "docs": [
                {
                    "encryption": 0,
                    "fileId": "63a913cb97ad4ef9b8834947b48e8862",
                    "fileName": "测试文档.pdf",
                    "source": 0
                }
            ],
            "flowInfo": {
                "autoArchive": True,
                "autoInitiate": True,
                "businessScene": "一步创建流程-一个人签50个企业",
                "flowConfigInfo": {
                    "notifyConfig": {
                        "noticeDeveloperUrl": "http://www.94rg.com",
                        "noticeType": "1,2,4"
                    },
                    "signConfig": {
                        "redirectUrl": "",
                        "signPlatform": "1,2,3,5"
                    }
                },
                "hashSign": False,
                "remark": "ca2期",
                "signValidity": *************
            },
            "signers": [
                {
                    "signOrder": 1,
                    "signerAccount": {
                        "signerAccountId": "3986d7544d9848f186ee9c5dcd342de1",
                        "authorizedAccountId": oid
                    },
                    "signfields": [
                        {
                            "autoExecute": False,
                            "actorIndentityType": 2,
                            "certId": "",
                            "fileId": "63a913cb97ad4ef9b8834947b48e8862",
                            "sealType": "",
                            "posBean": {
                                "posX": 100,
                                "posY": 500,
                                "posPage": "1"  # 注意：这里有两个 posPage，建议检查是否正确
                            },
                            "signType": 1
                        }
                    ],
                    "thirdOrderNo": "xyz"
                }
            ]
        }
        if oid == '3986d7544d9848f186ee9c5dcd342de1': #添加模板章
            # data["signers"][0]["signerAccount"]["authorizedAccountId"] = ""
            # data["signers"][0]["signfields"][0]["posBean"]["posPage"] = "1"
            data["signers"][0]["signfields"][0]["actorIndentityType"] = 0
        response = requests.post(url, headers=headers, data=json.dumps(data))

        # 打印响应
        res = response.json()
        print(f"这是获取的flowid是{res}")
        flowId = res.get("data").get("flowId")

        flowIdList.append(flowId)
        print(flowId)


        if oid == '3986d7544d9848f186ee9c5dcd342de1':   #添加备注签署区
            data["signers"][0]["signfields"][0]["fieldType"] = 2
            data["signers"][0]["signfields"][0]["remarkFieldConfig"] = {"aiCheck": 0, "inputType": 1, "remarkContent": "一二",
                                                                        "remarkFieldHeight": 200, "remarkFieldWidth": 200}
            response = requests.post(url, headers=headers, data=json.dumps(data))

            # 打印响应
            res = response.json()
            print(f"这是获取的flowid是{res}")
            flowId = res.get("data").get("flowId")

            flowIdList.append(flowId)
            print(flowId)

        #控制批量签的个数
        if len(flowIdList) >= 主体个数:
            break
    #获取批量签链接


    print("获取批量签的个数是{}，flowid是{}".format(len(flowIdList),flowIdList))
    url = 'http://in-test-openapi.tsign.cn/v3/sign-flow/batch-sign-url'
    data = {
        "signFlowIds":flowIdList,
        "operatorId": "3986d7544d9848f186ee9c5dcd342de1",
        "clientType": "ALL"
    }

    response = requests.post(url, headers=headers, data=json.dumps(data))

    # 打印响应
    print(response.status_code)
    print(f"这是获取的链接是{response.json()}")
    print(response.json().get("data").get("batchSignShortUrlWithoutLogin"))


if __name__ == '__main__':
    """
        1全灰          7876722740  走意愿弹框  领e签宝ca  0219 1528 改成了无需意愿
        2全白          7876722860  走签署弹框  领天津ca
        3弹框灰 证书不灰 7876722899  走意愿弹框  领天津ca
        4弹框不灰 证书灰 7876722915  走老版签署弹框 领天津ca
    """
    批量签链接(主体个数=5,是否注销证书=True,是否项目环境=0,appid版本=2)