request:
  url: /v1/signflows
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity
    copierAccountId: $copierAccountId
    copierIdentityAccountId: $copierIdentityAccountId
validate:
  - eq: [status_code, 200]
