- config:
    name: "SaaS指定意愿发起"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_Id1: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - path_pdf: data/个人借贷合同.pdf
      - xuanyuan_sixian: ${ENV(xuanyuan_sixian)}

- test:
    name: "创建王瑶济账号"
    variables:
      - thirdPartyUserId: **************
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_wang: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_liang: content.data.accountId

- test:
    name: "创建一个文件"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

- test:
    name: SaaS指定意愿发起，单主体，willTypes为空
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 0,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: []}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，返回app_id 中配置的全部意愿
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
      - language: zh-cn
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
        #仅通过接口无法判断，需要登录查看
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，单主体，willTypes仅传了CODE_SMS
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 0,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['CODE_SMS']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，willTypes为CODE_SMS
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
      - language: zh-cn
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，单主体个人，willTypes传了CODE_SMS及SIGN_PWD
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 0,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['CODE_SMS','SIGN_PWD']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name: 获取意愿链接，willTypes为CODE_SMS、SIGN_PWD
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
      - language: zh-cn
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，单机构主体，willTypes传了FACE_FACE_LIVENESS_RECOGNITION及FACE_ZHIMA_XY
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $xuanyuan_sixian,"signerAuthorizedAccountType": 0,"signerSignRoles": 1,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['FACE_FACE_LIVENESS_RECOGNITION','FACE_ZHIMA_XY']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，返回FACE_FACE_LIVENESS_RECOGNITION及FACE_ZHIMA_XY
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
      - language: zh-cn
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，主体为个人+机构，willTypes分别传FACE_FACE_LIVENESS_RECOGNITION及SIGN_PWD
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $xuanyuan_sixian,"signerAuthorizedAccountType": 0,"signerSignRoles": 1,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['FACE_FACE_LIVENESS_RECOGNITION']}, {"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 0,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['SIGN_PWD']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，willTypes分别传FACE_FACE_LIVENESS_RECOGNITION及SIGN_PWD
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - scene: 1
      - saleSchemaId: 18
      - language: zh-cn
    api: api/signflow/signflows/identifyUrl.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，主体为不同操作人
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 1,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['FACE_FACE_LIVENESS_RECOGNITION']}, {"allowedUploadAttachment": true, "signOrder": 0, "signerAccountId": $accountId_liang,  "signerAuthorizedAccountId": $accountId_liang,"signerAuthorizedAccountType": 0,"signerSignRoles": 0,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['SIGN_PWD']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，主体为王瑶济，意愿为FACE_FACE_LIVENESS_RECOGNITION
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - json: {"accountId":$accountId_wang,"app_id":$app_id,"appScheme":"","authType":"","billParams":{"payerAccountGid":"","saleSchemaId":18,"scope":""},"bizContext":{},"bizCtxIds":[$sign_flowId],"bizDecs":"","bizType":1,"configParams":{"authType":"","indivUneditableInfo":[]},"contextInfo":{"contextId":"","showResultPage":true},"indivInfo":{"bankCardNo":"","certNo":"","certType":"","mobileNo":"","name":"","nationality":""},"notifyUrl":"","origin":"", "redirectUrl": "http://www.baiu.com?tsignDes=签署成功&tsignTypeSIGN&tsignCode=0","repeatIdentity":true,"wukongOid":"$accountId_wang","scene":1}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name:  获取意愿链接，主体为梁贤红，意愿为SIGN_PWD
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_liang
      - json: {"accountId":$accountId_liang,"app_id": $app_id,"appScheme": "","authType":"","billParams":{"payerAccountGid":"","saleSchemaId":18,"scope":""},"bizContext":{},"bizCtxIds":[$sign_flowId],"bizDecs":"","bizType":1,"configParams":{"authType":"","indivUneditableInfo":[]},"contextInfo":{"contextId":"","showResultPage":true},"indivInfo":{"bankCardNo":"","certNo":"","certType":"","mobileNo":"","name":"","nationality":""},"notifyUrl":"","origin":"","redirectUrl": "http://www.baiu.com?tsignDes=签署成功&tsignTypeSIGN&tsignCode=0","repeatIdentity":true,"wukongOid":"$accountId_liang","scene":1}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，willtype同时包含一个合法一个非法
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 1, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 1,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['111111','CODE_SMS']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，主体为王瑶济，意愿仅保存合法CODE_SMS
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - json: {"accountId":$accountId_wang,"app_id":$app_id,"appScheme":"","authType":"","billParams":{"payerAccountGid":"","saleSchemaId":18,"scope":""},"bizContext":{},"bizCtxIds":[$sign_flowId],"bizDecs":"","bizType":1,"configParams":{"authType":"","indivUneditableInfo":[]},"contextInfo":{"contextId":"","showResultPage":true},"indivInfo":{"bankCardNo":"","certNo":"","certType":"","mobileNo":"","name":"","nationality":""},"notifyUrl":"","origin":"", "redirectUrl": "http://www.baiu.com?tsignDes=签署成功&tsignTypeSIGN&tsignCode=0","repeatIdentity":true,"wukongOid":"$accountId_wang","scene":1}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，willtype仅传一个非法
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 1, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 1,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['111111','111111']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，主体为王瑶济，意愿为app_id 维度
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - json: {"accountId":$accountId_wang,"app_id":$app_id,"appScheme":"","authType":"","billParams":{"payerAccountGid":"","saleSchemaId":18,"scope":""},"bizContext":{},"bizCtxIds":[$sign_flowId],"bizDecs":"","bizType":1,"configParams":{"authType":"","indivUneditableInfo":[]},"contextInfo":{"contextId":"","showResultPage":true},"indivInfo":{"bankCardNo":"","certNo":"","certType":"","mobileNo":"","name":"","nationality":""},"notifyUrl":"","origin":"", "redirectUrl": "http://www.baiu.com?tsignDes=签署成功&tsignTypeSIGN&tsignCode=0","repeatIdentity":true,"wukongOid":"$accountId_wang","scene":1}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: SaaS指定意愿发起，willtype传了重复字段
    variables:
      - flowId: ${get_randomNo_32()}
      - doc: {encryption: 0,fileId: $fileId, fileName: "个人借贷合同.pdf", source: 0}
      - attachment: {}
      - recipients: []
      - accountId: $accountId_wang
      - authorizerId: $accountId_wang
      - autoArchive: true
      - autoInitiate: true
      - businessScene: "SaaS指定意愿签署$flowId"
      - configInfo:
      - signers: [{"allowedUploadAttachment": true, "signOrder": 1, "signerAccountId": $accountId_wang,  "signerAuthorizedAccountId": $accountId_wang,"signerAuthorizedAccountType": 0,"signerSignRoles": 1,"signfields": [{"autoExecute": false,"fileId": $fileId,"posBean": {},"signType": 0,"signerRoleType": 0}], willTypes: ['CODE_SMS', 'CODE_SMS']}]
      - attachments: []
      - signValidity:
      - docs: [$doc]
    api: api/signflow/signflows/createFlowInput.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message",执行成功]

- test:
    name:  获取意愿链接，主体为王瑶济，意愿为CODE_SMS
    api: api/signflow/signflows/identifyUrlWithJson.yml
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - json: {"accountId":$accountId_wang,"app_id":$app_id,"appScheme":"","authType":"","billParams":{"payerAccountGid":"","saleSchemaId":18,"scope":""},"bizContext":{},"bizCtxIds":[$sign_flowId],"bizDecs":"","bizType":1,"configParams":{"authType":"","indivUneditableInfo":[]},"contextInfo":{"contextId":"","showResultPage":true},"indivInfo":{"bankCardNo":"","certNo":"","certType":"","mobileNo":"","name":"","nationality":""},"notifyUrl":"","origin":"", "redirectUrl": "http://www.baiu.com?tsignDes=签署成功&tsignTypeSIGN&tsignCode=0","repeatIdentity":true,"wukongOid":"$accountId_wang","scene":1}
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 校验签署权限-入参校验-登录账号与指定不一致
    variables:
      - flowId: $sign_flowId
      - accountId: $accountId_wang
      - operatorId: $accountId_liang
      - redirectUrl:
    api: api/signflow/flowSigners/checkSignAccess.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - contains: ["content.data.cardNo", "*"]