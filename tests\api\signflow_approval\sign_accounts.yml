#创建个人账号
- api:
    def: create_sign_accounts($thirdPartyUserId,$name,$idNumber,$idType,$email,$mobile)
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/createByThirdPartyUserId
      method: POST
      headers: ${get_headers($app_id)}
      json:
        thirdPartyUserId: $thirdPartyUserId
        name: $name
        idNumber: $idNumber
        idType: $idType
        email: $email
        mobile: $mobile
#创建企业
- api:
    def: create_sign_org_accounts($thirdPartyUserId,$creator,$idNumber,$name,$idType)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/createByThirdPartyUserId
      method: POST
      headers: ${get_headers($app_id)}
      json:
        thirdPartyUserId: $thirdPartyUserId
        name: $name
        idNumber: $idNumber
        idType: $idType
        creator: $creator
#获取实名组织详情
- api:
    def: getFinalRealnameOrgInfo($organize_id)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/$organize_id/getFinalRealnameOrgInfo
      method: GET
      headers: ${get_headers($app_id)}
#查询企业印章
- api:
    def: getOrgSeals($organize_id)
    request:
      url: ${ENV(footstone_user_url)}/v1/organizations/$organize_id/seals?scope=1
      method: GET
      headers: ${get_headers($app_id)}
#获取账号属性信息
- api:
    def: getProperties($accountId)
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/$accountId/getProperties
      method: GET
      headers: ${get_headers($app_id)}
#根据登录凭证获取账号信息(手机号)
- api:
    def: getByIdcard($mobile)
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/getByIdcard?idcard=$mobile&type=MOBILE
      method: GET
      headers: ${get_headers($app_id)}