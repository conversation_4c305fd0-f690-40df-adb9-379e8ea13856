- config:
    name: "*********迭代"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - group: ${ENV(envCode)}
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - fileId: ${ENV(file_id_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id1_in_tsign: ${ENV(account_id1_in_tsign)}

- test:
    name: "v3一步发起流程，指定两个个人签署区，其中一个sealType=1，一个sealType为空"
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id1_in_tsign","initiatorAuthorizedAccountId":"$account_id1_in_tsign","remark":"","signValidity":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id1_in_tsign","signerAccountId":"$account_id1_in_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一.二-三","remarkFieldHeight":1000,"remarkFieldWidth":1000},"fileId":"$fileId","handDrawnWay":"1","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"1","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"},{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id1_in_tsign","signerAccountId":"$account_id1_in_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":0,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三","remarkFieldHeight":1000,"remarkFieldWidth":1000},"fileId":"$fileId","handDrawnWay":"0","posBean":{"posPage":"2","posX":400,"posY":400},"sealType":"","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]

- test:
    name: "获取印章列表，可查得印章"
    variables:
      - flowId: $flowId
      - loginAccountId: $account_id1_in_tsign
      - signerAccountId: $account_id1_in_tsign
    api: api/signflow/signflows/getSignSeals.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: [content.data.personalSeals, 1]

- test:
    name: "v3一步发起流程，指定抄送人为已存在的accountId"
    variables:
      - json: {"docs":[{"fileId":"$fileId","fileName":"2页.pdf"}],"flowInfo":{"autoArchive":true,"autoInitiate":true,"businessScene":"签署","flowConfigInfo":{"buttonConfig":{"propButton":"1,2,15"},"notifyConfig":{"noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}","noticeType":"","sendNotice":false},"signConfig":{"archiveLock":false,"batchDropSeal":false,"countdown":0,"esignCertSign":false,"esignQrPos":"","mobileShieldWay":0,"redirectDelayTime":0,"redirectUrl":"","showQrCode":false,"signPlatform":"","signQrSwitch":false}},"initiatorAccountId":"$account_id1_in_tsign","initiatorAuthorizedAccountId":"$account_id1_in_tsign","remark":"","signValidity":""},"signers":[{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id1_in_tsign","signerAccountId":"$account_id1_in_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":2,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一.二-三","remarkFieldHeight":1000,"remarkFieldWidth":1000},"fileId":"$fileId","handDrawnWay":"1","posBean":{"posPage":"1","posX":200,"posY":200},"sealType":"1","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"},{"forceReadTime":0,"platformSign":false,"signOrder":1,"signerAccount":{"authorizedAccountId":"$account_id1_in_tsign","signerAccountId":"$account_id1_in_tsign"},"signfields":[{"actorIndentityType":"0","assignedPosbean":false,"autoExecute":false,"fieldType":0,"remarkFieldConfig":{"aiCheck":2,"inputType":1,"remarkContent":"一二三","remarkFieldHeight":1000,"remarkFieldWidth":1000},"fileId":"$fileId","handDrawnWay":"0","posBean":{"posPage":"2","posX":400,"posY":400},"sealType":"","signDateBeanType":1,"signTimeFormat":"","signType":1,"width":200}],"thirdOrderNo":"11111"}]}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code, 200]