#def: signflowsCreateForOpenApi($autoArchive, $businessScene, $configInfo, $contractValidity, $initiatorAccountId, $payerAccountId, $signValidity)
request:
  url: /v1/signflows
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity
validate:
  - eq: [status_code, 200]

#撤销流程-不传revokeReason，请求不传空对象
