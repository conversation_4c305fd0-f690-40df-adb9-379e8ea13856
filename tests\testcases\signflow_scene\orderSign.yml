- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(base_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: ***********
      - path_pdf: 个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - idNo1: 130202199104016781
      - name1: esigntest-测试用户1
      - thirdPartyUserId1: esigntest-130202199104016781
      - idNo2: 230124200712193556
      - name2: esigntest-测试用户2
      - thirdPartyUserId2: esigntest-230124200712193556
      - idNo3: 230803201510286771
      - name3: esigntest-测试用户3
      - thirdPartyUserId3: esigntest-230803201510286771
      - client_id: pc
- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: 130202199104016781
      - name: esigntest-测试用户1
      - thirdPartyUserId: esigntest-130202199dfg104016781
    api: api/user/account_third/create_account_appid.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230124200712193556
      - name: esigntest-测试用户2
      - thirdPartyUserId: esigntest-23012443234c22vbnn1219
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户3，只输入必填项（用户名、证件号），转签用"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230803201510286771
      - name: esigntest-测试用户3
      - thirdPartyUserId: esigntest-230803201512345qwertsdf71
    extract:
      - personOid3: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建组织：个人账户2创建组织"
    variables:
      - accountId: $personOid2
      - orgType: CRED_ORG_CODE
      - orgCode: ********-X
      - legalIdNo:
      - legalName:
      - name: $orgName
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]


- test:
    name: "用户1创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章1
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户2创建个人模版印章"
    variables:
      - accountId: $personOid2
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章2
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户3创建个人模版印章"
    variables:
      - accountId: $personOid3
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章3
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: "创建企业模板印章"
    variables:
      - orgId: $orgOid1
      - color: RED
      - height: 150
      - width: 150
      - alias: 企业模板印章
      - type: TEMPLATE_ROUND
      - central: STAR
      - hText: 上弦文
      - qText: 下弦文
    api: api/seals/seals/create_c_template_seal_appid.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 获取文件信息
    variables:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 顺序签场景-用户创建流程
    variables:
      - autoArchive: False
      - businessScene: 顺序签署
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: , signPlatform: '1,2,3'}
      - extend: {}
      - initiatorAuthorizedAccountId: $orgOid1
      - initiatorAccountId: $personOid1
    api: api/signflow/signflows/signflowsCreateForOrganize.yml
    extract:
      - flowId6: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 顺序签场景-添加流程文档：单文档
    variables:
      - flowId: $flowId6
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序签场景-添加流程签署区
    variables:
      - flowId: $flowId6
      - posBean1: {addSignTime: True, key: '', posPage: '1', posX: 100, posY: 600, qrcodeSign: False, width: 159}
      - posBean2: {addSignTime: True, key: '', posPage: '2', posX: null, posY: 400, qrcodeSign: False, width: 159}
      - posBean3: {addSignTime: True, key: '', posPage: '2', posX: 100, posY: 200, qrcodeSign: False, width: 95}
      - posBean4: {addSignTime: True, key: '', posPage: '1', posX: 100, posY: 400, qrcodeSign: False, width: 95}
      - posBean5: {addSignTime: True, key: '', posPage: '2', posX: 100, posY: 400, qrcodeSign: False, width: 95}
      - posBean6: {addSignTime: True, key: '', posPage: '2', posX: 300, posY: 600, qrcodeSign: False, width: 159}
      - signfield1: ${gen_signfield_data_V3(1,0,$fileId1,$flowId6,$posBean1,1,$personOid1,$orgOid1,,,1)}
      - signfield2: ${gen_signfield_data_V3(1,0,$fileId1,$flowId6,$posBean2,2,$personOid2,$orgOid1,,,2)}
      - signfield3: ${gen_signfield_data_V3(0,0,$fileId1,$flowId6,$posBean3,1,$personOid3,,,,3)}
      - signfield4: ${gen_signfield_data_V3(0,0,$fileId1,$flowId6,$posBean3,1,$personOid1,,,,1)}
      - signfield5: ${gen_signfield_data_V3(0,0,$fileId1,$flowId6,$posBean4,1,$personOid2,,,,2)}
      - signfield6: ${gen_signfield_data_V3(1,0,$fileId1,$flowId6,$posBean5,1,$personOid3,$orgOid1,,,3)}
      - signfields: [$signfield1,$signfield2,$signfield3,$signfield4,$signfield5,$signfield6]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
      - signfieldId2: content.data.signfieldIds.1
      - signfieldId3: content.data.signfieldIds.2
      - signfieldId4: content.data.signfieldIds.3
      - signfieldId5: content.data.signfieldIds.4
      - signfieldId6: content.data.signfieldIds.5
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序签场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId6
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序签场景-查询流程状态
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId6
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",1]

- test:
    name: 顺序签场景-查询企业印章列表
    api: api/seals/seals/select_c_seals.yml
    variables:
      - orgId: $orgOid1
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 顺序签场景-查询第一个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 顺序签场景-查询第二个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid2
    extract:
      - p2_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 顺序签场景-查询第三个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid3
    extract:
      - p3_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]

- test:
    name: 顺序签场景-更新第一个用户签署域
    variables:
      - flowId: $flowId6
      - posBean1: {addSignTime: True, key: '', posPage: '1', posX: 100, posY: 600, qrcodeSign: False, width: 159}
      - signfield1: ${gen_signfield_update_data($orgOid1, , $posBean1, ,$o_sealId,$signfieldId1)}
      - posBean2: {addSignTime: True, key: '', posPage: '1', posX: 100, posY: 400, qrcodeSign: False, width: 95}
      - signfield2: ${gen_signfield_update_data($personOid1, , $posBean2, ,$p1_sealId,$signfieldId4)}
      - signfields: [$signfield1,$signfield2]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 顺序签场景-执行第一个用户签署区
    variables:
      - signfieldIds: [$signfieldId1,$signfieldId4]
      - accountId: $personOid1
      - flowId: $flowId6
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]

- test:
    name: 顺序签场景-查询签署区，第二个人签署中，第三个人等待签署
    variables:
      - flowId: $flowId6
      - conditionKey: "signfieldId"
      - expectedKey: "status"
      - signfieldIds: ""
      - accountId: ""
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - s_signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId2, $expectedKey,1)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId5, $expectedKey,1)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId3, $expectedKey,0)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId6, $expectedKey,0)}",True]

- test:
    name: 顺序签场景-更新第二个用户签署域
    variables:
      - flowId: $flowId6
      - posBean2: {addSignTime: True, key: '', posPage: '2', posX: null, posY: 400, qrcodeSign: False, width: 159}
      - signfield2: ${gen_signfield_update_data($orgOid1, , $posBean2, ,$o_sealId,$signfieldId2)}
      - posBean3:  {addSignTime: True, key: '', posPage: '2', posX: 100, posY: 400, qrcodeSign: False, width: 95}
      - signfield3: ${gen_signfield_update_data($personOid2, , $posBean3, ,$p2_sealId,$signfieldId5)}
      - signfields: [$signfield2,$signfield3]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]

- test:
    name: 顺序签场景-执行第二个用户签署区
    variables:
      - flowId: $flowId6
      - signfieldIds: [$signfieldId2,$signfieldId5]
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]

- test:
    name: 顺序签场景-更新第三个用户签署域
    variables:
      - flowId: $flowId6
      - posBean3: {addSignTime: True, key: '', posPage: '2', posX: 100, posY: 200, qrcodeSign: False, width: 95}
      - signfield3: ${gen_signfield_update_data($personOid3, , $posBean3, ,$p3_sealId,$signfieldId3)}
      - posBean4: {addSignTime: True, key: '', posPage: '2', posX: 300, posY: 600, qrcodeSign: False, width: 159}
      - signfield4: ${gen_signfield_update_data($orgOid1, , $posBean4, ,$o_sealId,$signfieldId6)}
      - signfields: [$signfield3,$signfield4]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序签场景-查询签署区
    variables:
      - flowId: $flowId6
      - signfieldIds: [$signfieldId3,$signfieldId6]
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsSelect.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序签场景-执行第三个用户签署区
    variables:
      - signfieldIds: [$signfieldId3,$signfieldId6]
      - accountId: $personOid3
      - flowId: $flowId6
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]

- test:
    name: 顺序签场景-查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId6
      - signfieldIds:
      - accountId: $personOid3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序签场景-归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId6
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 顺序签场景-查询流程：已归档
    setup_hooks:
      - ${sleep_N_secs(5)}
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId6
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]


