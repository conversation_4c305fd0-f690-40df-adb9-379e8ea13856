- config:
    name: 通过流程模板创建签署流程接口测试
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id)}
      - sign_template_id: ${ENV(sign_template_id)}
      - template_file_id: ${ENV(template_file_id)}
      - notify_url: ${ENV(notify_url)}
      - participant_id_org: ${ENV(participant_id_org)}
      - participant_id_person: ${ENV(participant_id_person)}
      - org_id: ${ENV(org_id)}
      - org_name: ${ENV(org_name)}
      - transactor_id: ${ENV(transactor_id)}
      - transactor_phone: ${ENV(transactor_phone)}
      - transactor_name: ${ENV(transactor_name)}
      - person_id: ${ENV(person_id)}
      - person_phone: ${ENV(person_phone)}
      - person_name: ${ENV(person_name)}
      - component_id: ${ENV(component_id)}

# 必填参数测试
- test:
    name: "场景1: 必填参数校验-signTemplateId缺失"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        }
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "流程模板ID不能为空" ]

- test:
    name: "场景2: 必填参数校验-signFlowTitle缺失"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {}
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "签署任务主题不能为空" ]

- test:
    name: "场景3: 必填参数校验-participantId缺失"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        },
        "participants": [
          {
            "orgParticipant": {
              "orgName": "$org_name"
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "参与方ID不能为空" ]

# 正向场景测试
- test:
    name: "场景4: 完整参数-企业经办人ID方式"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowInitiator": {
          "orgId": "$org_id",
          "transactor": {
            "psnId": "$transactor_id"
          }
        },
        "signFlowConfig": {
          "signFlowTitle": "完整测试${get_timestamp()}",
          "autoStart": true,
          "autoFinish": true,
          "notifyUrl": "$notify_url",
          "noticeConfig": {
            "noticeTypes": "1,2"
          },
          "signConfig": {
            "availableSignClientTypes": "1",
            "autoFillAndSubmit": true
          },
          "authConfig": {
            "psnAvailableAuthModes": ["PSN_MOBILE3", "PSN_FACE"],
            "orgAvailableAuthModes": ["ORG_BANK_TRANSFER", "ORG_LEGALREP"]
          }
        },
        "participants": [
          {
            "participantId": "$participant_id_org",
            "orgParticipant": {
              "orgId": "$org_id",
              "transactor": {
                "transactorPsnId": "$transactor_id"
              }
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]
      - contains: [ content.message, "成功" ]
    extract:
      - flowId: content.data.signFlowId

- test:
    name: "场景5: 完整参数-个人账号方式"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "个人测试${get_timestamp()}"
        },
        "participants": [
          {
            "participantId": "$participant_id_person",
            "psnParticipant": {
              "psnAccount": "$person_phone",
              "psnName": "$person_name"
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]

# 参数规则校验
- test:
    name: "场景6: 企业参与方信息校验-orgId与orgName不能同时为空"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        },
        "participants": [
          {
            "participantId": "$participant_id_org",
            "orgParticipant": {
              "transactor": {
                "transactorPsnAccount": "$transactor_phone",
                "transactorName": "$transactor_name"
              }
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "企业ID与企业名称不能同时为空" ]

- test:
    name: "场景7: 控件值填充测试"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "控件测试${get_timestamp()}"
        },
        "components": [
          {
            "fileId": "$template_file_id",
            "componentId": "$component_id",
            "componentValue": "测试填充值"
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]

- test:
    name: "场景8: 特殊字符校验-签署任务主题"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "测试/\\:*\"<>|?签署"
        }
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "主题名称不可含有特殊字符" ]

- test:
    name: "场景9: 签署截止时间校验"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "时间测试${get_timestamp()}",
          "signFlowExpireTime": "${get_timestamp(-1)}"
        }
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "签署截止时间不能早于当前时间" ]

# 参与方必填参数完整性校验
- test:
    name: "场景10: 参与方为空数组"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        },
        "participants": []
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "参与方信息不能为空" ]

- test:
    name: "场景11: 参与方信息不完整-企业参与方缺少经办人"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        },
        "participants": [
          {
            "participantId": "$participant_id_org",
            "orgParticipant": {
              "orgName": "$org_name"
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "企业经办人信息不能为空" ]

- test:
    name: "场景12: 参与方信息不完整-个人参与方缺少必填信息"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        },
        "participants": [
          {
            "participantId": "$participant_id_person",
            "psnParticipant": {
              "psnAccount": "$person_phone"
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "个人姓名不能为空" ]

# 企业经办人信息完整性校验
- test:
    name: "场景13: 企业经办人信息校验-使用orgName时必填项校验"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        },
        "participants": [
          {
            "participantId": "$participant_id_org",
            "orgParticipant": {
              "orgName": "$org_name",
              "transactor": {}
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "使用企业名称时经办人账号和姓名不能为空" ]

# 混合参与方场景
- test:
    name: "场景14: 多参与方完整性校验"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowConfig": {
          "signFlowTitle": "多参与方测试${get_timestamp()}"
        },
        "participants": [
          {
            "participantId": "$participant_id_org",
            "orgParticipant": {
              "orgId": "$org_id",
              "transactor": {
                "transactorPsnId": "$transactor_id"
              }
            }
          },
          {
            "participantId": "$participant_id_person",
            "psnParticipant": {
              "psnAccount": "$person_phone",
              "psnName": "$person_name"
            }
          }
        ]
      }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ content.code, 0 ]
      - contains: [ content.message, "成功" ]

# 发起方信息完整性校验
- test:
    name: "场景15: 发起方信息校验-企业发起缺少经办人"
    api: api/v3/sign-flow/create-by-sign-template.yml
    variables:
      json: {
        "signTemplateId": "$sign_template_id",
        "signFlowInitiator": {
          "orgId": "$org_id"
        },
        "signFlowConfig": {
          "signFlowTitle": "测试${get_timestamp()}"
        }
      }
    validate:
      - eq: [ status_code, 200 ]
      - ne: [ content.code, 0 ]
      - contains: [ content.message, "企业发起方必须指定经办人" ] 