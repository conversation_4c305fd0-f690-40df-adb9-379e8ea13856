- config:
    name: "数据准备：创建签署流程需要的账号、文档"
    base_url: ${ENV(base_url)}
    variables:
      - orgName: esigntest东乡族自治县梅里美商贸有限公司测试
      - legalName: 沙苏飞也
      - perIdType: CRED_PSN_CH_IDCARD
      - orgIdType: CRED_ORG_CODE
      - email: <EMAIL>
      - mobile: ***********
      - path_pdf: 个人借贷合同.pdf
      - extend: {}
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
      - path_pdf2: data/劳动合同书.pdf
      - path_pdf3: data/模板劳动合同0001.pdf
      - path_pdf4: data/销售合同006.pdf
      - path_pdf5: data/three_page.pdf
      - contractValidity:
      - payerAccountId:
      - signValidity:
      - idNo1: 130202199104016781
      - name1: esigntest-测试用户1
      - thirdPartyUserId1: esigntest-130202199104016781
      - idNo2: 230124200712193556
      - name2: esigntest-测试用户2
      - thirdPartyUserId2: esigntest-230124200712193556
      - idNo3: 230803201510286771
      - name3: esigntest-测试用户3
      - thirdPartyUserId3: esigntest-230803201510286771
      - client_id: pc
- test:
    name: "创建账户：个人账户1，只输入必填项（用户名、证件号）"
    variables:
      - idNo: 130202199104016781
      - name: esigntest-测试用户1
      - thirdPartyUserId: esigntest-130202199iutfa781
    api: api/user/account_third/create_account_appid.yml
    extract:
      - personOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户2，传所有参数（email、mobile、name）"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230124200712193556
      - name: esigntest-测试用户2
      - thirdPartyUserId: esigntest-23012443234c22vbnn1219
    extract:
      - personOid2: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建账户：个人账户3，只输入必填项（用户名、证件号），转签用"
    api: api/user/account_third/create_account_appid.yml
    variables:
      - idNo: 230803201510286771
      - name: esigntest-测试用户3
      - thirdPartyUserId: esigntest-232sfgh76542340286771
    extract:
      - personOid3: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]

- test:
    name: "创建组织：个人账户2创建组织"
    variables:
      - accountId: $personOid2
      - orgType: CRED_ORG_CODE
      - orgCode: ********-X
      - legalIdNo:
      - legalName:
      - name: $orgName
    api: api/user/organizations/create_organizations.yml
    extract:
      - orgOid1: content.data.accountId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.accountId",0]
#    output:
#        -  $orgId

- test:
    name: "用户1创建个人模版印章"
    variables:
      - accountId: $personOid1
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章1
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户2创建个人模版印章"
    variables:
      - accountId: $personOid2
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章2
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "用户3创建个人模版印章"
    variables:
      - accountId: $personOid3
      - color: RED
      - height: 100
      - width: 100
      - alias: esigntest测试印章3
      - type: SQUARE
    api: api/seals/seals/create_p_template_seal.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业模板印章"
    variables:
      - orgId: $orgOid1
      - color: RED
      - height: 150
      - width: 150
      - alias: 企业模板印章
      - type: TEMPLATE_ROUND
      - central: STAR
      - hText: 上弦文
      - qText: 下弦文
    api: api/seals/seals/create_c_template_seal_appid.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.sealId",0]


- test:
    name: 获取文件信息
    variables:
      - fileId: ${get_file_id($app_id,$path_pdf)}
    api: api/file_template/files/get_fileId.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
    extract:
      - fileKey: content.data.fileKey

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: 根据本地文件创建文件
    variables:
      - accountId: $personOid1
      - fileName: 个人借贷合同.pdf
      - fileKey: $fileKey
    api: api/file_template/files/createbyfilekey.yml
    extract:
      - fileId2: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]


- test:
    name: 多人多文档场景-创建流程
    variables:
      - autoArchive: False
      - businessScene: 多人多文档
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '', redirectUrl: '', signPlatform: ''}
      - extend: {}
      - initiatorAccountId: $personOid1
    api: api/signflow/signflows/signflowsCreate.yml
    extract:
      - flowId3: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-添加流程文档：文档1，文档2
    variables:
      - flowId: $flowId3
      - doc1: {'fileHash': '', 'fileId': $fileId1, 'fileName': 'fileOne', 'filePassword': '', 'encryption': 0}
      - doc2: {'fileHash': '', 'fileId': $fileId2, 'fileName': 'fileTwo', 'filePassword': '', 'encryption': 0}
      - docs: [$doc1,$doc2]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

      #- test:
      #    name: 多人多文档场景-查询文档关键字
      #    variables:
      #      - keywords: 出借方
      #    api: api/signflow/flowDocuments/documentsKeywordSelect.yml
      #    extract:
      #      - posPage: content.data.0.positionList.0.pageIndex
      #      - posX: content.data.0.positionList.0.coordinateList.0.posx
      #      - posY: content.data.0.positionList.0.coordinateList.0.posy
      #    validate:
      - eq: ["content.code",0]

- test:
    name: 多人多文档场景-查询企业印章列表
    api: api/seals/seals/select_c_seals.yml
    variables:
      - orgId: $orgOid1
    extract:
      - o_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-查询个人印章列表
    api: api/seals/seals/select_p_seals.yml
    variables:
      - accountId: $personOid1
    extract:
      - p1_sealId: content.data.seals.0.sealId
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-添加流程签署区：1-个人1关键字签署，2-个人1指定位置签署，3-个人1自由落章，4-个人2骑缝签
    variables:
      - flowId: $flowId3
      - posBean1: {addSignTime: True, keyword: '出借方', posPage: 1, posX: 0, posY: 0, qrcodeSign: True, width: 0}
      - posBean2: {addSignTime: True, keyword: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - posBean3: {}
      - posBean4: {addSignTime: False, keyword: '', posPage: '1,2', posX: 0, posY: 600, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_data_V2(0,0,$fileId1,$flowId3,$posBean1,4,$personOid1,,,1)}
      - signfield2: ${gen_signfield_data_V2(1,0,$fileId1,$flowId3,$posBean2,1,$personOid1,$orgOid1,,1)}
      - signfield3: ${gen_signfield_data_V2(0,0,$fileId2,$flowId3,$posBean3,0,$personOid1,,,1)}
      - signfield4: ${gen_signfield_data_V2(1,0,$fileId1,$flowId3,$posBean4,2,$personOid2,$orgOid1,,1)}
      - signfields: [$signfield1,$signfield2,$signfield3,$signfield4]
    api: api/signflow/signfields/signfieldsCreate.yml
    extract:
      - signfieldId1: content.data.signfieldIds.0
      - signfieldId2: content.data.signfieldIds.1
      - signfieldId3: content.data.signfieldIds.2
      - signfieldId4: content.data.signfieldIds.3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-开启流程
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-发起人账号1-催签-签署人账号2
    variables:
      - noticeTypes: 1,2,3
      - flowId: $flowId3
      - accountId: $personOid1
      - rushsignAccountId: $personOid2
    api: api/signflow/flowSigners/rushsign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-更新流程签署区4：补充印章数据
    variables:
      - flowId: $flowId3
      - posBean4: {addSignTime: False, key: '', posPage: '1,2', posX: 0, posY: 600, qrcodeSign: True, width: 0}
      - signfield: ${gen_signfield_update_data($orgOid1, , $posBean4, ,$o_sealId,$signfieldId4)}
      - signfields: [$signfield]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId3
      - signfieldIds: ""
      - accountId: ""
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-删除流程签署区3：个人1自由落章
    variables:
      - signfieldId: $signfieldId3
      - flowId: $flowId3
    api: api/signflow/signfields/signfieldsDelete.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.deleteResults.0.optResult",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-执行流程签署区1/2-缺少印章数据-预期失败
    variables:
      - flowId: $flowId3
      - signfieldIds: [$signfieldId1,$signfieldId2]
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    extract:
      - executeResults: content.data.executeResults
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",1]
      - contains: ["content.data.executeResults.0.failedReason","没有印章"]
      - eq: ["content.data.executeResults.1.optResult",1]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-执行流程签署区4-预期成功
    variables:
      - signfieldIds: [$signfieldId4]
      - flowId: $flowId3
      - accountId: $personOid2
    api: api/signflow/signfields/signfieldsExecute.yml
    extract:
      - executeResults: content.data.executeResults
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.optResult",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-更新流程签署区1/2：补充印章数据
    variables:
      - flowId: $flowId3
      - posBean1: {addSignTime: True, keyword: '出借方', posPage: 1, posX: 0, posY: 0, qrcodeSign: True, width: 0}
      - signfield1: ${gen_signfield_update_data($personOid1, , $posBean1, ,$p1_sealId,$signfieldId1)}
      - posBean2: {addSignTime: True, keyword: '', posPage: '2', posX: 400, posY: 200, qrcodeSign: True, width: 0}
      - signfield2: ${gen_signfield_update_data($orgOid1, , $posBean2, , $o_sealId, $signfieldId2)}
      - signfields: [$signfield1, $signfield2]
    api: api/signflow/signfields/signfieldsUpdate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-查询签署区
    api: api/signflow/signfields/signfieldsSelect.yml
    variables:
      - flowId: $flowId3
      - accountId: ""
      - signfieldIds: ""
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-执行流程签署区1/2
    variables:
      - signfieldIds: [$signfieldId1,$signfieldId2]
      - flowId: $flowId3
      - accountId: $personOid1
    api: api/signflow/signfields/signfieldsExecute.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.executeResults.0.failedReason",null]
      - eq: ["content.data.executeResults.1.failedReason",null]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-查询签署区
    variables:
      - conditionKey: "signfieldId"
      - expectedKey: "status"
      - flowId: $flowId3
      - signfieldIds: ""
      - accountId: ""
    api: api/signflow/signfields/signfieldsSelect.yml
    extract:
      - s_signfields: content.data.signfields
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId1, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId2, $expectedKey,4)}",True]
      - eq: ["${results_isExpected($s_signfields, $conditionKey,$signfieldId4, $expectedKey,4)}",True]

- test:
    name: 多人多文档场景-归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["status_code", 200]

- test:
    name: 多人多文档场景-查询流程：已归档
    api: api/signflow/signflows/signflowsSelect.yml
    variables:
      - flowId: $flowId3
    validate:
      - eq: [status_code, 200]
      - eq: ["content.data.flowStatus",2]
      - eq: ["status_code", 200]
