- config:
    name: "原文文本签"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/关键字文档.pdf
      - group: ''
      - data: "test"

###############验证RSA##############################################
- test:
    name: "RSA_appId下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - RSA_yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - thirdPartyUserId: sixian201911141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber:
      - orgLegalName:
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "SM2_appId下创建签署人王瑶济账号"
    variables:
      - app_id: ${ENV(SM2_appId)}
      - thirdPartyUserId: yuzan455651212312321
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - SM2_yuzan_accountId: content.data.accountId

- test:
    name: "验证原文文本签接口参数data是否必填"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: data不能为空"]

- test:
    name: "验证原文文本签接口参数data为空字符串能否发起"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":"","accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: data不能为空"]

- test:
    name: "验证原文文本签接口参数data为正常字符串"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "验证原文文本签接口参数data为中文字符串"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":"代签文档","accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 代签文档不是base64格式"]

- test:
    name: "验证原文文本签接口参数data为是base64格式"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":"5Luj562+5paH5qGj","accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "验证原文文本签接口参数当type=PLATFORM_USER时，accountId是否必填"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 文本用户签, accountId不能为空"]

- test:
    name: "验证原文文本签接口参数当type=PLATFORM时，accountId正常个人账号"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":"PLATFORM"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "验证原文文本签接口参数当type=PLATFORM_USER时，accountId正常个人账号"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message","成功"]

- test:
    name: "验证原文文本签接口参数当type=PLATFORM_USER时，accountId是空字符串"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":"","type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 文本用户签, accountId不能为空"]

- test:
    name: "验证原文文本签接口参数当type=PLATFORM_USER时，accountId是一个不存在的账号"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":"123456","type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 用户不存在或用户类型错误"]

- test:
    name: "验证原文文本签接口参数当type=PLATFORM_USER时，accountId是一个企业id"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":sixian_organize_id,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: 用户不存在或用户类型错误"]

- test:
    name: "验证原文文本签接口参数appid和accountId不匹配"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"accountId":$SM2_yuzan_accountId,"data":$data,"type":"PLATFORM_USER"}
      - code: ${strToInt()}
      - message: ${ENV(message3)}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",$code]
      - contains: ["content.message",$message]

- test:
    name: "验证原文文本签接口参数当type是否必填"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: type不能为空"]

- test:
    name: "验证原文文本签接口参数当type是一个空字符串"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":""}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]
      - eq: ["content.message","参数错误: type值错误"]

- test:
    name: "验证原文文本签接口参数当type是一个不存在的字符串"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":"TEST"}
    api: api/iterate_cases/dataSignOriginal.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",1435002]

- test:
    name: "RSA-验证原文文本签接口参数当type是PLATFORM"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"type":"PLATFORM"}
    api: api/iterate_cases/dataSignOriginal.yml
    extract:
      - signResult1: content.data.signResult
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "RSA-验证文本平台签的验签-正常"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"signResult":$signResult1}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]

- test:
    name: "验证原文文本平台签的验签-data是否必填"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"signResult":$signResult1}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证原文文本平台签的验签-data是空字符串"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":"","signResult":$signResult1}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证原文文本平台签的验签-signResult是否必填"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]

- test:
    name: "验证原文文本平台签的验签-data和signResult不匹配"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":"testqw","signResult":$signResult1}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438401]
      - eq: ["content.message", "验签失败"]

- test:
    name: "验证原文文本平台签的验签-signResult不存在"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"signResult":"11111111111"}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1438401]
      - eq: ["content.message", "验签失败"]

- test:
    name: "验证原文文本平台签的验签-signResult为空字符串"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"signResult":""}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - eq: ["content.message", "参数错误: signResult不能为空"]

- test:
    name: "RSA-验证原文文本签接口参数当type是PLATFORM_USER"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
    api: api/iterate_cases/dataSignOriginal.yml
    extract:
      - signResult1: content.data.signResult
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: "RSA-验证原文文本平台用户签的验签-正常"
    variables:
      - app_id: ${ENV(RSA_appId)}
      - json: {"data":$data,"signResult":$signResult1}
    api: api/iterate_cases/originalVerify.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]

      #######################RSA证书验证#########################################

      #- test:
      #    name: "RSA-验证原文文本签接口参数当type是PLATFORM"
      #    variables:
      #        - app_id: ${ENV(RSA_appId)}
      #        #        - json: {"data":$data,"type":"PLATFORM"}
      #    api: api/iterate_cases/dataSignOriginal.yml
      #    extract:
      #        - signResult1: content.data.signResult
      #    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      #
      #- test:
      #    name: "RSA-验证原文文本平台签的验签-正常"
      #    variables:
      #        - app_id: ${ENV(RSA_appId)}
      #        #        - json: {"data":$data,"signResult":$signResult1}
      #    api: api/iterate_cases/originalVerify.yml
      #    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]
      #
      #- test:
      #    name: "RSA-验证原文文本签接口参数当type是PLATFORM_USER"
      #    variables:
      #        - app_id: ${ENV(RSA_appId)}
      #        #        - json: {"data":$data,"accountId":$RSA_yuzan_accountId,"type":"PLATFORM_USER"}
      #    api: api/iterate_cases/dataSignOriginal.yml
      #    extract:
      #        - signResult1: content.data.signResult
      #    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      #
      #- test:
      #    name: "RSA-验证原文文本平台用户签的验签-正常"
      #    variables:
      #        - app_id: ${ENV(RSA_appId)}
      #        #        - json: {"data":$data,"signResult":$signResult1}
      #    api: api/iterate_cases/originalVerify.yml
      #    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data.signInfo.signature.modify", False]