- config:
    name: 查询审批进度信息
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(xuanyuan_realname)}
    "request":
      "base_url": ""
      "headers":
        "Content-Type": "application/json"

- test:
    name: 查询审批进度信息
    api: api/signflow/approvalflows/approvalflowsProgress.yml
    variables:
      - approvalId: "AF-7c0ae2b27b3f4e299a7e03214e051e4e"
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - contains: ["content.message",成功]
