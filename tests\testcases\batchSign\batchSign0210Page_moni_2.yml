- config:   #TODO
    name: "悟空轩辕整合 - 验证企业-法人-经办人签署区"    #需要打开链接验证  查看是否有法人签
    base_url: ${ENV(base_url)}
    variables:
      - app_id: "**********"
      - app_secret: "c7bbb2254c0006435be7dcda18940e1d"
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - group: ''
      - path_pdf1: data/16M.pdf
      - fileId: ${get_file_id($app_id,$path_pdf1)}
      - oid_linjue_tsign: "34a2789ee9d348128fbfe01bf16f714d"  #linjue
      - sleep: ${hook_sleep_n_secs(5)}

#oid    :  34a2789ee9d348128fbfe01bf16f714d 
#orgid  :  6561d4137ee547f0832abc0e07bbaefb


#
#
- test:
    name: v3一步发起 多主体经办人签
    variables:
      - attachments: []
      - docs: [{"fileId": $fileId, fileName:  签署文件.pdf}]
      - "contractValidity": ${get_timestamp_13(3600000)}
      - flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: ''}
      - json: {
    "docs":  $docs,
    "flowInfo": {
        "autoArchive": true,
        "autoInitiate": true,
        "contractValidity":$contractValidity,
        "businessScene": "v3一步发起 多主体经办人签",
        "flowConfigInfo": {
            "notifyConfig": {
                "noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}",
                "noticeType": '1,2,3,4'
            },
            "signConfig": {
                "redirectUrl": "",
                "signPlatform": ""
            }
        },
        "hashSign": false,
        "remark": "印章平台",
        "signValidity": *************
    },
    "signers": [
        {
            "signOrder": 1,
            "signerAccount": {
              "signerAccountId": "34a2789ee9d348128fbfe01bf16f714d",
              "authorizedAccountId": "969c37538edc4631a61d45d637affce8"
            },
            "signfields": [
                {
                    "autoExecute": false,
                    "actorIndentityType": 4,
                    "certId": "",
                    "fileId": $fileId,
                    "sealType": "",
                    "posBean": {
                        "addSignTime": true,
                        "keyword": "乙方",
                        "posPage": "1",
                        "posX": 100,
                        "posY": 100
                    },
                   "signType": 1
                },
              {
                "autoExecute": false,
                "actorIndentityType": 2,
                "certId": "",
                "fileId": $fileId,
                "sealType": "",
                "posBean": {
                  "addSignTime": true,
                  "keyword": "乙方",
                  "posPage": "1",
                  "posX": 200,
                  "posY": 200
                },
                "signType": 1
              }
            ],
            "thirdOrderNo": "xyz"
        }
    ]
}
    api: api/signflow/v3/createFlowOneStep.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]


-   test:
        name: "获取签署链接"
        variables:
            -   app_secret: $app_secret
            -   flowId: $sign_flowId1
            -   accountId: $oid_linjue_tsign
            -   urlType: 0
            -   multiSubject: true
            -   organizeId:
            -   compressContext: true
        api: api/iterate_cases/executeUrl.yml
        extract:
            -   shortUrl: content.data.shortUrl
            -   code: content.code
            -   message: content.message
        validate:
            -   eq: ["content.code",0]


