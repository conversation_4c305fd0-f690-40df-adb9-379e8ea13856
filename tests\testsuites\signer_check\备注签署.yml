config:
  name: 备注签署
testcases:
  备注签署:
    testcase: testcases/remark_sign/备注签署.yml

  校验当前登录人是否有审批操作权限:
    testcase: testcases/queryApprovalQueryPermission/queryApprovalQueryPermission.yml

  分步发起备注签署:
    testcase: testcases/iterate_cases/20220120Sign1.yml

  福昕20220106用例:
    testcase: testcases/iterate_cases/20220104_福昕pdf.yml

  批量签署0210:
    testcase: testcases/batchSign/batchSign0210.yml

  批量签署0210Page:
    testcase: testcases/batchSign/batchSign0210Page.yml

  批量签署20220901:
    testcase: testcases/batchSign/batchSign20220901.yml


  批量签署0210-2:
    testcase: testcases/batchSign/batchSign_getFlowList.yml


  核心接口优化-悟空:
    testcase: testcases/核心接口改造/追加手动签署区-悟空.yml

  核心接口优化-轩辕:
    testcase: testcases/核心接口改造/追加手动签署区-轩辕.yml

  0630增加合同推送给乙方能力:
    testcase: testcases/iterate_cases/0630增加合同推送给乙方能力.yml

  发起签署页面开放:
    testcase: testcases/发起签署页面开放.yml

  摘要签开放api:
    testcase: testcases/hashSign/摘要签开放.yml

  指定签署方附件:
    testcase: testcases/指定签署方附件.yml

  批量签升级V3:
    testcase: testcases/batchSign/batchSignV3.yml
  静默授权:
    testcase: testcases/静默授权.yml
  多印章授权模板自动签:
    testcase: testcases/模板发起-多印章自动签.yml