#def: create_accounts_v2($email,$mobile,$email2,$mobile2,$thirdPartyUserId)         #创建账号
request:
  url: ${ENV(footstone_user_url)}/v1/accounts/createOverall
  method: POST
  headers: ${get_headers($app_id)}
  json:
    contacts: {"mobile": $mobile,"email":$email}
    idcards: {"mobile": $mobile2,"email":$email2,
              "thirdparty": {
                "thirdpartyKey": "ALI_PAY",
                "thirdpartyUserId": $thirdPartyUserId,
                "thirdpartyUserType": ""
              }}



