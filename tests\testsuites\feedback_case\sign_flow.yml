config:
  name: 反哺case_signflow
  variables:
    - path_pdf: data/2019103000880493.pdf
    - app_id: ${ENV(X-Tsign-Open-App-Id-NO-REALNAME)}
    - tPUserId: autotest${get_randomNo()}
    - tPUserId1: autotest1${get_randomNo()}
    - tPUserId2: autotest2${get_randomNo()}
    - m1: "13260881366"
    - m2: "18767104153"
    - e1: <EMAIL>
    - e2: <EMAIL>
    - pOid1: ${getOid($m1)}
    - pOid1: ${getOid($m2)}
    - path_pdf2: data/模板文件.pdf
testcases:

  #    反哺case签署场景测试:
  #      testcase: testcases/feedback_case/sign_flow.yml

  反哺case搜索关键字场景测试:
    testcase: testcases/feedback_case/searchWordsPosition.yml

