#创建流程
request:
  url: v1/signflows
  method: POST
  headers: ${get_headers($app_id)}
  json:
    autoArchive: $autoArchive
    businessScene: $businessScene
    configInfo: $configInfo
    contractValidity: $contractValidity
    extend: $extend
    initiatorAccountId: $initiatorAccountId
    payerAccountId: $payerAccountId
    signValidity: $signValidity
    initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId
validate:
  - eq: [status_code, 200]