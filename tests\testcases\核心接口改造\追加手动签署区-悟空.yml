- config:
    name: 追加手动签署区-悟空
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(tengqing_PaaS_id_without_real_with_willingness)}
      - file_id: ${ENV(file_id_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id_sx: ${ENV(org_id2_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id_dy: ${ENV(org_id1_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id_1: ${ENV(account_id1_in_tengqing_PaaS_id_without_real_with_willingness)}
      - account_id_2: ${ENV(account_id2_in_tengqing_PaaS_id_without_real_with_willingness)}
      - notifyUrl: ${ENV(noticeDeveloperUrl)}
      - group: ${ENV(envCode)}
      - signFlowExpireTime: ${get_timestamp(10)}
      - sealId_sx_acrossEnterprise: ${ENV(sealId_sx_acrossEnterprise)}
      - sealId_sx_acrossEnterprise_old: ${ENV(sealId_sx_acrossEnterprise_old)}
      - dedicated_cloud_id: ${ENV(dedicated_cloud_id)}
      - file_id_in_dedicated_cloud_id: ${ENV(file_id_in_dedicated_cloud_id)}

- test:
    name: 分布发起流程
    variables:
      - json: { "autoArchive": "false","businessScene": "签署${get_timestamp()}","configInfo": { "noticeType": "1","noticeDeveloperUrl": "${notifyUrl}","signPlatform": "1,2,3","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false },"extend": { },"initiatorAccountId": "$account_id_1","initiatorAuthorizedAccountId": "$account_id_1","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 添加合同文档
    variables:
      - flowId: $sign_flowId
      - docs: [ { fileId: $file_id, fileName: 文档, filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 悟空appId 指定不存在的印章，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": $file_id,"signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","sealId": "1","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message", 无效的印章 ]

- test:
    name: 查询个人印章
    variables:
      - accountId: $account_id_1
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]

- test:
    name: 悟空appId 个人签指定印章，追加成功
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": $file_id,"signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","sealId": "$sealId","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]

- test:
    name: 悟空appId 经办人签指定印章，追加成功
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": $file_id,"signerAccountId": $account_id_1,"actorIndentityType": "4","authorizedAccountId": $account_id_sx,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","sealId": "$sealId","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]


- test:
    name: 查询企业印章
    variables:
      - accountId: $account_id_sx
    api: api/seals/seals/select_p_seals.yml
    extract:
      - sealId: content.data.seals.0.sealId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]

- test:
    name: 非实名悟空appId 企业签指定印章，追加成功
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": $file_id,"signerAccountId": $account_id_1,"actorIndentityType": "2","authorizedAccountId": $account_id_sx,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","sealId": "$sealId","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]

- test:
    name: 非实名悟空appId 企业签指定老的跨企业印章，追加失败
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": $file_id,"signerAccountId": $account_id_1,"actorIndentityType": "2","authorizedAccountId": $account_id_sx,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","sealId": $sealId_sx_acrossEnterprise_old,"signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message", 印章和签署主体不匹配 ]

- test:
    name: 非实名悟空appId 企业签指定新的跨企业印章，追加失败
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": $file_id,"signerAccountId": $account_id_1,"actorIndentityType": "2","authorizedAccountId": $account_id_sx,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","sealId": $sealId_sx_acrossEnterprise,"signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message", 印章和签署主体不匹配 ]


- test:
    name: 非实名悟空appId 企业签指定其他企业的印章，无法追加
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": $file_id,"signerAccountId": $account_id_1,"actorIndentityType": "2","authorizedAccountId": $account_id_dy,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","sealId": "$sealId","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message", 印章和签署主体不匹配 ]

- test:
    name: 分布发起流程-专属云
    variables:
      - json: { "autoArchive": "false","businessScene": "签署${get_timestamp()}","dedicatedCloudId": "${dedicated_cloud_id}","configInfo": { "noticeType": "1","noticeDeveloperUrl": "${notifyUrl}","signPlatform": "1,2,3","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false },"extend": { },"initiatorAccountId": "$account_id_1","initiatorAuthorizedAccountId": "$account_id_1","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 已开启仅支持一份合同，无法追加多份合同
    variables:
      - flowId: $sign_flowId
      - docs: [ { fileId: $file_id, fileName: "文档.pdf", filePassword: "",encryption: 0 },{ fileId: $file_id, fileName: 文档, filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1437526 ]

- test:
    name: 专属云流程无法追加非专属云文件
    variables:
      - flowId: $sign_flowId
      - docs: [ { fileId: $file_id, fileName: "文档.pdf", filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 1437523 ]

- test:
    name: 追加文件到流程
    variables:
      - flowId: $sign_flowId
      - docs: [ { fileId: "${file_id_in_dedicated_cloud_id}", fileName: "文档.pdf", filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: 追加个人手动签署区成功
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "${file_id_in_dedicated_cloud_id}","signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]
      - len_eq: [ content.data.signfieldBeans, 1 ]
    extract:
      - old_sign_field_id: content.data.signfieldBeans.0.signfieldId

- test:
    name: 查询是否需要重定向
    variables:
      - flowId: $sign_flowId
      - params: nodeFieldIds=$old_sign_field_id
    api: api/v1/signflows/flowId/jumpToRedirectUrl.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.jumpToRedirectUrl", True ]

- test:
    name: "查询流程详情，返回一个签署区"
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id_1
      - operator_id: $account_id_1
    api: api/mobile-shield/signflows_search_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - len_eq: [ content.data.signDocs.0.signfields, 1 ]
      - eq: [ content.data.signDocs.0.signfields.0.signfieldId, $old_sign_field_id ]

- test:
    name: 相同坐标追加个人手动签署区，替换原签署区
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "${file_id_in_dedicated_cloud_id}","signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]
      - not_equals: [ "content.data.signfieldBeans.0.signfieldId",$old_sign_field_id ]
    extract:
      - new_sign_field_id: content.data.signfieldBeans.0.signfieldId


- test:
    name: 签名域被替换，无需重定向
    variables:
      - flowId: $sign_flowId
      - params: nodeFieldIds=$old_sign_field_id
    api: api/v1/signflows/flowId/jumpToRedirectUrl.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.jumpToRedirectUrl", False ]

- test:
    name: 新的签名域，需要重定向
    variables:
      - flowId: $sign_flowId
      - params: nodeFieldIds=$new_sign_field_id
    api: api/v1/signflows/flowId/jumpToRedirectUrl.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.jumpToRedirectUrl", True ]

- test:
    name: "查询流程详情，返回一个签署区，id为替换后的id"
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id_1
      - operator_id: $account_id_1
    api: api/mobile-shield/signflows_search_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - len_eq: [ content.data.signDocs.0.signfields, 1 ]
      - eq: [ content.data.signDocs.0.signfields.0.signfieldId, $new_sign_field_id ]

- test:
    name: 再次添加一个手动签署区-成功
    variables:
      - flowId: $sign_flowId
      - signfields: [ { "fileId": "${file_id_in_dedicated_cloud_id}","signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 188,"posY": 188 },"sealType": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]
      - len_eq: [ content.data.signfieldBeans, 1 ]
    extract:
      - new_sign_field_id_188: content.data.signfieldBeans.0.signfieldId
- test:
    name: "查询流程详情，查询签署区为一个，且signFieldId已更新"
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id_1
      - operator_id: $account_id_1
    api: api/mobile-shield/signflows_search_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - len_eq: [ content.data.signDocs.0.signfields, 1 ]
      - eq: [ content.data.signDocs.0.signfields.0.signfieldId, $new_sign_field_id_188 ]

- test:
    name: 流程开启成功
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: "添加自动签"
    variables:
      - flowId: $sign_flowId
      - posBean: { 'posPage': '1','posX': 110, 'posY': 100 }
      - signfield: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean, 'signType': 1 }
      - signfields: [ $signfield ]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - auto_sign_field_id: content.data.signfieldBeans.0.signfieldId

- test:
    name: "查询签署文件-签署前"
    variables:
      - json: { "flowId": "$sign_flowId" }
    api: api/v1/signflows/signDocs.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.signDocs.0.fileId","${file_id_in_dedicated_cloud_id}" ]
    extract:
      - file_key: content.data.signDocs.0.fileKey

- test:
    name: "查询流程详情，返回一个签署区，手动签的id替换为自动签的id"
    variables:
      - flowId: $sign_flowId
      - queryAccountId: $account_id_1
      - operator_id: $account_id_1
    api: api/mobile-shield/signflows_search_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - len_eq: [ content.data.signDocs.0.signfields, 1 ]
      - eq: [ content.data.signDocs.0.signfields.0.signfieldId, $auto_sign_field_id ]

- test:
    name: 创建批量签任务
    api: api/v1/signflows/batchSign.yml
    variables:
      - json: { "flowBatch": [ { "flowId": "$sign_flowId", "order": 0 } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
    extract:
      - batchId: content.data.batchId

- test:
    name: 获取批量签印章列表
    api: api/v1/signflows/flowId/getMultiSignSeals.yml
    variables:
      - batchId: $batchId
      - params: accountId=$account_id_1
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_gt: [ content.data.personalSeals, 0 ]

- test:
    name: 查询批量签详情
    api: api/RPC/queryMultiSignTask.yml
    variables:
      - json: { "batchId": "${batchId}" }
    validate:
      - eq: [ status_code, 200 ]
      - len_eq: [ content.multiSignTaskInfoBeans, 1 ]
      - eq: [ content.multiSignTaskInfoBeans.0.flowId, "$sign_flowId" ]

- test:
    name: 分布发起流程-专属云
    variables:
      - json: { "autoArchive": "false","businessScene": "签署${get_timestamp()}","dedicatedCloudId": "${dedicated_cloud_id}","configInfo": { "noticeType": "1","noticeDeveloperUrl": "${notifyUrl}","signPlatform": "1,2,3","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false },"extend": { },"initiatorAccountId": "$account_id_1","initiatorAuthorizedAccountId": "$account_id_1","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId_2: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 无任务的流程可以创建批量签任务
    api: api/v1/signflows/batchSign.yml
    variables:
      - json: { "flowBatch": [ { "flowId": "${sign_flowId_2}" } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: 无任务可以追加到批量签任务
    api: api/v1/signflows/batchId/addFlow.yml
    variables:
      - batchId: ${batchId}
      - json: { "flowBatch": [ { "flowId": "${sign_flowId_2}" } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message","成功" ]

- test:
    name: 追加文件到流程
    variables:
      - flowId: $sign_flowId_2
      - docs: [ { fileId: "${file_id_in_dedicated_cloud_id}", fileName: "文档.pdf", filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: 流程开启成功
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId_2
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: "添加自动签"
    variables:
      - flowId: $sign_flowId_2
      - posBean: { 'posPage': '1','posX': 100, 'posY': 100 }
      - signfield: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean, 'signType': 1 }
      - signfields: [ $signfield ]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: "再次添加一个自动签签署区"
    variables:
      - flowId: $sign_flowId_2
      - posBean: { 'posPage': '1','posX': 150, 'posY': 150 }
      - signfield: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean, 'signType': 1 }
      - signfields: [ $signfield ]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]


- test:
    name: 不允许重复追加签署流程
    api: api/v1/signflows/batchId/addFlow.yml
    variables:
      - batchId: ${batchId}
      - json: { "flowBatch": [ { "flowId": "${sign_flowId_2}" } ] }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435002 ]
      - contains: [ "content.message",不允许重复追加签署流程 ]

- test:
    name: 查询批量签详情
    api: api/RPC/queryMultiSignTask.yml
    variables:
      - json: { "batchId": "${batchId}" }
    validate:
      - eq: [ status_code, 200 ]
      - len_eq: [ content.multiSignTaskInfoBeans, 2 ]
      - eq: [ content.multiSignTaskInfoBeans.0.flowId, "$sign_flowId" ]
      - eq: [ content.multiSignTaskInfoBeans.1.flowId, "$sign_flowId_2" ]

- test:
    name: "向批量签任务追加手动签署区"
    variables:
      - batchId: ${batchId}
      - json: { "signfields": [ { "flowId": "$sign_flowId_2", "fileId": "${file_id_in_dedicated_cloud_id}","signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 2, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","signType": 1 } ] }
    api: api/v1/signflows/batchId/add-signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.signfieldBeans.0.flowId",$sign_flowId_2 ]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 归档流程
    api: api/signflow/signflows/signflowsArchive.yml
    variables:
      - flowId: $sign_flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]

- test:
    name: "查询签署文件-签署后"
    variables:
      - json: { "flowId": "$sign_flowId" }
    api: api/v1/signflows/signDocs.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.data.signDocs.0.fileId","${file_id_in_dedicated_cloud_id}" ]
      - not_equals: [ "content.data.signDocs.0.fileKey","${file_key}" ]

- test:
    name: 获取批量签链接
    api: api/v1/signflows/batchId/getBatchSignUrl.yml
    variables:
      - batchId: ${batchId}
      - json: { "accountId": "$account_id_1" }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.data.batchSignUrl","http" ]
#贝壳定制非标-追加签署区逻辑调整

- test:
    name: 分步发起流程-专属云-贝壳定制非标
    variables:
      - json: { "autoArchive": "false","businessScene": "签署${get_timestamp()}","dedicatedCloudId": "${dedicated_cloud_id}","configInfo": { "noticeType": "1","noticeDeveloperUrl": "${notifyUrl}","signPlatform": "1,2,3","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false },"extend": { },"initiatorAccountId": "$account_id_1","initiatorAuthorizedAccountId": "$account_id_1","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId_3: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 追加文件到流程
    variables:
      - flowId: $sign_flowId_3
      - docs: [ { fileId: "${file_id_in_dedicated_cloud_id}", fileName: "文档.pdf", filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: 添加两个签署区-手动签
    variables:
      - flowId: $sign_flowId_3
      - signfields: [ { "fileId": "${file_id_in_dedicated_cloud_id}","signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 100,"posY": 200 },"sealType": "","signType": 1 },{ "fileId": "${file_id_in_dedicated_cloud_id}","signerAccountId": $account_id_1,"actorIndentityType": "0","authorizedAccountId": $account_id_1,"assignedPosbean": true,"order": 1, "posBean": { "addSignTime": true,"posPage": "1","posX": 200,"posY": 200 },"sealType": "","signType": 1 } ]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message", 成功 ]
      - len_eq: [ content.data.signfieldBeans, 2 ]

- test:
    name: 流程开启成功
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId_3
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: "查询流程详情，返回两个签署区"
    variables:
      - flowId: $sign_flowId_3
      - queryAccountId: $account_id_1
      - operator_id: $account_id_1
    api: api/mobile-shield/signflows_search_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - len_eq: [ content.data.signDocs.0.signfields, 2 ]
      - str_eq: [ content.data.signDocs.0.fileUrl, None ]

- test:
    name: "添加两个签署区-自动签"
    variables:
      - flowId: $sign_flowId_3
      - posBean: { 'posPage': '1','posX': 170, 'posY': 170 }
      - signfield: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean, 'signType': 1 }
      - posBean2: { 'posPage': '1','posX': 180, 'posY': 180 }
      - signfield2: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean2, 'signType': 1 }
      - signfields: [ $signfield,$signfield2 ]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ content.data.signfieldBeans, 2 ]

- test:
    name: "添加一个相同位置的自动签署区，不会替换"
    variables:
      - flowId: $sign_flowId_3
      - posBean: { 'posPage': '1','posX': 170, 'posY': 170 }
      - signfield: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean, 'signType': 1 }
      - signfields: [ $signfield ]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ content.data.signfieldBeans, 0 ]

- test:
    name: "添加一个不同位置的自动签署区，会添加签署区"
    variables:
      - flowId: $sign_flowId_3
      - posBean: { 'posPage': '1','posX': 200, 'posY': 200 }
      - signfield: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean, 'signType': 1 }
      - signfields: [ $signfield ]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ content.data.signfieldBeans, 1 ]

- test:
    name: "查询流程详情，返回三个签署区"
    variables:
      - flowId: $sign_flowId_3
      - queryAccountId: $account_id_1
      - operator_id: $account_id_1
    api: api/mobile-shield/signflows_search_V2.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
      - len_eq: [ content.data.signDocs.0.signfields, 3 ]
      - str_eq: [ content.data.signDocs.0.fileUrl, None ]

- test:
    name: 分步发起流程-专属云-贝壳定制非标
    variables:
      - json: { "autoArchive": "true","businessScene": "签署${get_timestamp()}","dedicatedCloudId": "${dedicated_cloud_id}","configInfo": { "noticeType": "1","noticeDeveloperUrl": "${notifyUrl}","signPlatform": "1,2,3","archiveLock": false,"redirectUrl": "","countdown": 0,"redirectDelayTime": "0","batchDropSeal": false },"extend": { },"initiatorAccountId": "$account_id_1","initiatorAuthorizedAccountId": "$account_id_1","payerAccountId": "" }
    api: api/signflow/signflows/signflows.yml
    extract:
      - sign_flowId_4: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - len_gt: [ "content.data.flowId",0 ]

- test:
    name: 追加文件到流程
    variables:
      - flowId: $sign_flowId_4
      - docs: [ { fileId: "${file_id_in_dedicated_cloud_id}", fileName: "文档.pdf", filePassword: "",encryption: 0 } ]
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]

- test:
    name: "同时添加两个相同坐标的自动签署区"
    variables:
      - flowId: $sign_flowId_4
      - posBean: { 'posPage': '1','posX': 200, 'posY': 200 }
      - signfield: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean, 'signType': 1 }
      - posBean2: { 'posPage': '1','posX': 200, 'posY': 200 }
      - signfield2: { 'certId': '', 'fileId': "${file_id_in_dedicated_cloud_id}", 'authorizedAccountId': $account_id_1, 'order': 2,  'posBean': $posBean2, 'signType': 1 }
      - signfields: [ $signfield,$signfield2 ]
    api: api/signflow/signfields/signfieldsCreate_userAuto_appid.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ content.data.signfieldBeans, 2 ]

- test:
    name: 流程开启成功
    api: api/signflow/signflows/signflowsStart.yml
    variables:
      - flowId: $sign_flowId_4
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message", 成功 ]

- test:
    name: 贝壳专用签署链接
    api: api/v1/signflows/flowId/executeUrl.yml
    variables:
      - flowId: $sign_flowId_4
      - json: { "accountId": "$account_id_1","redirectUrl": "https://www.esign.cn/`" }
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.data.url","http" ]