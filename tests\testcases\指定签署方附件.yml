- config:
    name: "指定签署方附件"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(opponent_appid)}
      - group: ${ENV(envCode)}
      - file_1: ${ENV(file_id_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - file_2: ${ENV(file_id_2_in_app_id_tengqing_PaaS_with_willing_and_real_name)}
      - psn_1: ${ENV(account_id1_in_tsign)}
      - psn_2: ${ENV(account_id2_in_tsign)}
      - name_2: ${ENV(name_2)}
      - org_1: ${ENV(orgid_dy)}
      - org_2: ${ENV(orgid_sx)}

- test:
    name: v2-RPC创建流程-身份证国徽面
    variables:
      - json: { "appId": "${app_id}","attachments": [ { "attachmentName": "我是附件","fileId": "${file_1}" } ],"docs": [ { "fileId": "${file_2}" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "businessScene","configInfo": { "countdown": 0 },"createByApi": true,"initiatorAccountId": "${psn_1}","initiatorAuthorizedAccountId": "${psn_1}" },"signers": [ { "signFields": [ { "autoExecute": false,"fileId": "${file_2}","posBean": { "posPage": "1","posX": 100,"posY": 100 },"sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "0" } ],"signOrder": 1,"signerAccount": { "attachmentConfigs": [ { "attachmentName": "1","checkSignerInfo": true,"required": true,"verificationType": 2,"verify": true } ],"signerAccountId": "${psn_1}","signerAuthorizedAccountId": "${psn_1}" } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.message",签署方附件要求校验类型不支持匹配签署方信息 ]
      - eq: [ "content.code",1435002 ]

- test:
    name: v2-RPC创建流程-企业签不允许校验身份证
    variables:
      - json: { "appId": "${app_id}","attachments": [ { "attachmentName": "我是附件","fileId": "${file_1}" } ],"docs": [ { "fileId": "${file_2}" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "businessScene","configInfo": { "countdown": 0 },"createByApi": true,"initiatorAccountId": "${psn_1}","initiatorAuthorizedAccountId": "${psn_1}" },"signers": [ { "signFields": [ { "autoExecute": false,"fileId": "${file_2}","posBean": { "posPage": "1","posX": 100,"posY": 100 },"sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "1" } ],"signOrder": 1,"signerAccount": { "attachmentConfigs": [ { "attachmentName": "1","checkSignerInfo": true,"required": true,"verificationType": 1,"verify": true } ],"signerAccountId": "${psn_1}","signerAuthorizedAccountId": "${org_1}" } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.message",签署方附件要求校验类型不支持匹配签署方信息 ]
      - eq: [ "content.code",1435002 ]

- test:
    name: v2-RPC创建流程-不校验时可以指定企业签身份证
    variables:
      - json: { "appId": "${app_id}","attachments": [ { "attachmentName": "我是附件","fileId": "${file_1}" } ],"docs": [ { "fileId": "${file_2}" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "businessScene","configInfo": { "countdown": 0 },"createByApi": true,"initiatorAccountId": "${psn_1}","initiatorAuthorizedAccountId": "${psn_1}" },"signers": [ { "signFields": [ { "autoExecute": false,"fileId": "${file_2}","posBean": { "posPage": "1","posX": 100,"posY": 100 },"sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "1" } ],"signOrder": 1,"signerAccount": { "attachmentConfigs": [ { "attachmentName": "1","checkSignerInfo": true,"required": true,"verificationType": 1,"verify": false } ],"signerAccountId": "${psn_1}","signerAuthorizedAccountId": "${org_1}" } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: v2-RPC创建流程-签署人通知方式不支持
    variables:
      - json: { "appId": "${app_id}","attachments": [ { "attachmentName": "我是附件","fileId": "${file_1}" } ],"docs": [ { "fileId": "${file_2}" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "businessScene","configInfo": { "countdown": 0 },"createByApi": true,"initiatorAccountId": "${psn_1}","initiatorAuthorizedAccountId": "${psn_1}" },"signers": [ { "signFields": [ { "autoExecute": false,"fileId": "${file_2}","posBean": { "posPage": "1","posX": 100,"posY": 100 },"sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "1" } ],"signOrder": 1,"signerAccount": { "noticeType": "-1","attachmentConfigs": [ { "attachmentName": "1","checkSignerInfo": true,"required": true,"verificationType": 1,"verify": false } ],"signerAccountId": "${psn_1}","signerAuthorizedAccountId": "${org_1}" } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    validate:
      - eq: [ status_code, 200 ]
      - contains: [ "content.message",通知方式不支持 ]
      - eq: [ "content.code", 1435002 ]

- test:
    name: v2-RPC创建流程-身份证人像面
    variables:
      - json: { "appId": "${app_id}","docs": [ { "fileId": "${file_2}" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "businessScene","configInfo": { "countdown": 0 },"createByApi": true,"initiatorAccountId": "${psn_1}","initiatorAuthorizedAccountId": "${psn_1}" },"signers": [ { "signFields": [ { "autoExecute": false,"fileId": "${file_2}","posBean": { "posPage": "1","posX": 100,"posY": 100 },"sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "0" } ],"signOrder": 1,"signerAccount": { "attachmentConfigs": [ { "attachmentName": "1","checkSignerInfo": true,"required": true,"verificationType": 1,"verify": true } ],"signerAccountId": "${psn_2}","signerAuthorizedAccountId": "${psn_2}" } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: 获取attachmentId
    variables:
      - flowId: ${flow_id}
      - queryAccountId: ${psn_2}
      - querySpaceAccountId: ${psn_2}
      - resourceShareId: ''
      - menuId: ''
      - scenario: ''
      - batchSign: false
      - operator_id: ${psn_2}
    api: api/signflow/signflows/signflowsConfig_para.yml
    extract:
      - attachmentId: content.data.pageConfig.attachmentConfigs.0.attachmentRequirements.0.attachmentId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: v2-上传无法通过的附件
    variables:
      - method: post
      - params: ""
      - flowId: ${flow_id}
      - operator_id: ${psn_2}
      - json: { "accountId": "${psn_2}","attachments": [ { "attachmentId": "${attachmentId}","attachmentName": "宣刚义身份证.png","fileId": "8eb0e820954b4ff99b52224a78c483ed" } ],"operatorAccountId": "${psn_2}","subjectId": "${psn_2}" }
    api: api/v2/signflows/flowId/attachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1437412 ]
      - contains: [ "content.message",不满足 ]

- test:
    name: v2-上传可以通过的附件
    variables:
      - method: post
      - params: ""
      - flowId: ${flow_id}
      - operator_id: ${psn_2}
      - json: { "accountId": "${psn_2}","attachments": [ { "attachmentId": "${attachmentId}","attachmentName": "宣刚义身份证.png","fileId": "850be5da9fad408d93f52e2392675f8b" } ],"operatorAccountId": "${psn_2}","subjectId": "${psn_2}" }
    api: api/v2/signflows/flowId/attachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: v2-修改附件名称
    variables:
      - method: PUT
      - params: ""
      - flowId: ${flow_id}
      - operator_id: ${psn_2}
      - json: { "attachments": [ { "attachmentName": "宣刚义身份证2.png","fileId": "850be5da9fad408d93f52e2392675f8b" } ] }
    api: api/v2/signflows/flowId/attachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: v2-签署页查询流程附件
    variables:
      - method: GET
      - params: accountId=${psn_2}&authorizerIds=${psn_2}
      - flowId: ${flow_id}
      - json: ''
      - operator_id: ${psn_2}
    api: api/v2/signflows/flowId/attachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]
      - contains: [ "content.data.attachmentList.0.attachments.0.attachmentName",'宣刚义身份证2.png' ]

- test:
    name: RPC查询流程附件
    variables:
      - json: { "flowIds": [ "${flow_id}" ] }
    api: api/RPC/batchQueryFlowAttachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - len_eq: [ "content.flowAttachmentList",1 ]

- test:
    name: v2-删除已上传的附件
    variables:
      - method: delete
      - params: fileIds=850be5da9fad408d93f52e2392675f8b
      - flowId: ${flow_id}
      - json: ''
      - operator_id: ${psn_2}
    api: api/v2/signflows/flowId/attachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: v2-签署页查询流程附件
    variables:
      - method: GET
      - params: accountId=${psn_2}&authorizerIds=${psn_2}
      - flowId: ${flow_id}
      - json: ''
      - operator_id: ${psn_2}
    api: api/v2/signflows/flowId/attachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - len_eq: [ "content.data.attachmentList",0 ]

- test:
    name: RPC查询流程附件
    variables:
      - json: { "flowIds": [ "${flow_id}" ] }
    api: api/RPC/batchQueryFlowAttachments.yml
    validate:
      - eq: [ status_code, 200 ]
      - str_eq: [ "content.flowAttachmentList",None ]


- test:
    name: v2-RPC创建流程-钉钉计费隔离-无订单无法发起
    variables:
      - json: { "appId": "${app_id}","docs": [ { "fileId": "${file_2}" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "businessScene","configInfo": { "billStrategy": "dingding_billing","countdown": 0 },"createByApi": true,"initiatorAccountId": "${psn_1}","initiatorAuthorizedAccountId": "${psn_1}" },"signers": [ { "signFields": [ { "autoExecute": false,"fileId": "${file_2}","posBean": { "posPage": "1","posX": 100,"posY": 100 },"sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "0" } ],"signOrder": 1,"signerAccount": { "attachmentConfigs": [ { "attachmentName": "1","checkSignerInfo": true,"required": true,"verificationType": 1,"verify": true } ],"signerAccountId": "${psn_2}","signerAuthorizedAccountId": "${psn_2}" } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435405 ]
      - contains: [ "content.message",企业账户中电子合同份数不足 ]

- test:
    name: v2-RPC创建流程-钉钉计费隔离-有订单可以发起
    variables:
      - json: { "appId": "${app_id}","docs": [ { "fileId": "${file_2}" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "businessScene","configInfo": { "billStrategy": "dingding_billing","countdown": 0 },"createByApi": false,"initiatorAccountId": "${org_1}","initiatorAuthorizedAccountId": "${org_1}" },"signers": [ { "signFields": [ { "autoExecute": false,"fileId": "${file_2}","posBean": { "posPage": "1","posX": 100,"posY": 100 },"sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "0" } ],"signOrder": 1,"signerAccount": { "attachmentConfigs": [ { "attachmentName": "1","checkSignerInfo": true,"required": true,"verificationType": 1,"verify": true } ],"signerAccountId": "${psn_2}","signerAuthorizedAccountId": "${psn_2}" } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: "一步到位创建流程-钉钉计费隔离-无订单无法发起"
    variables:
      - doc3: { encryption: 0,fileId: '${file_2}', source: 0 }
      - json: { "accountId": '${psn_1}',"createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "签署任务","configInfo": { "billStrategy": "dingding_billing","noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}" },"docs": [ $doc3 ],"signValidity": "","contractValidity": "","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": '${psn_2}',"signerAccountName": "","signerAuthorizerId": '${org_1}',"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "fileId": '${file_2}',"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 0,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",1435405 ]
      - contains: [ "content.message",'企业账户中电子合同份数不足' ]

- test:
    name: "一步到位创建流程-钉钉计费隔离-有订单可以发起"
    variables:
      - doc3: { encryption: 0,fileId: '${file_2}', source: 0 }
      - json: { "accountId": '${psn_2}',"createWay": "normal","autoInitiate": true,"autoArchive": true,"businessScene": "签署任务","configInfo": { "billStrategy": "dingding_billing","noticeType": "1,2,3,4","signPlatform": "1,2,3","redirectUrl": "","noticeDeveloperUrl": "${ENV(noticeDeveloperUrl)}" },"docs": [ $doc3 ],"signValidity": "","contractValidity": "","payerAccountId": "${org_1}","signers": [ { "seperateSigner": false,"signOrder": 1,"signerAccounts": [ { "signerAuthorizerType": 1,"signerSignRoles": "2","signerAccount": "","signerAccountId": '${psn_2}',"signerAccountName": "","signerAuthorizerId": '${org_1}',"signerAuthorizerName": "","signerNickname": "" } ],"signerRoleLabel": "甲方","signfields": [ { "fileId": '${file_2}',"autoExecute": false,"assignedPosbean": true,"assignedSeal": false,"posBean": { "signDateBean": null,"signDateBeanType": 0,"keyword": "","addSignTime": false,"signTimeFormat": "","posPage": 1,"posX": 100,"posY": 200,"qrcodeSign": false },"sealFileKey": "","sealId": "","sealType": "1","signType": 0,"signerRoleType": "0","thirdOrderNo": "" } ] } ],"recipients": [ ] }
    api: api/mobile-shield/createAllAtOnce.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: v2-创建海外签
    variables:
      - json: { "appId": "${app_id}","clientId": "","docs": [ { "fileId": "${file_1}","fileName": "文档2.pdf" } ],"flowInfo": { "autoArchive": true,"autoInitiate": true,"businessScene": "海外签","configInfo": { "batchDropSeal": true,"noticeType": "","signMode": "GLOBAL","signPlatform": "","willTypes": [ ] },"createByApi": false,"createWay": "","extend": { },"extraParam": { },"flowConfigItemDTO": { "buttonConfig": { "propButton": "" } },"flowId": "","initiatorAccountId": "${org_1}","initiatorAuthorizedAccountId": "${org_1}","payerAccountId": "","processId": "","signValidity": null,"templateCooperationId": "","templateId": "" },"signers": [ { "signFields": [ { "fileId": "${file_1}","handDrawnWay": "0","posBean": { "posPage": "1","posX": 200,"posY": 200,"width": 100 },"sealFileKey": "","sealId": "","sealIds": [ ],"sealType": "0,1","signType": 1,"signerRoleType": "2" } ],"signOrder": 1,"signerAccount": { "accessToken": "123456","allowedUploadAttachment": true,"authWay": "ACCESS_TOKEN","requiredRealName": true,"requiredWilling": false,"signerAccount": "","signerAccountId": "${psn_2}","signerAccountName": "${name_2}","signerAuthorizedAccountId": "${org_1}","signerAuthorizedAccountName": "","signerNickname": "","signerRoleLabel": "","willTypes": [ "" ] } } ] }
    api: api/signflow/signflows/createFlowV2Input.yml
    extract:
      - flow_id: content.data.flowId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - contains: [ "content.message",成功 ]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $flow_id
    api: api/mobile-shield/signflows_search_signfields.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code", 0 ]
      - eq: [ "content.message", "成功" ]
    extract:
      - signfieldId: content.data.signfields.0.signfieldId

- test:
    name: 获取印章-用户1
    variables:
      - flowId: ${flow_id}
      - loginAccountId: ${psn_2}
      - signerAccountId: ${psn_2}
    api: api/signflow/signflows/getSignSeals.yml
    extract:
      - psn_2_seal: content.data.personalSeals.0.sealId
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

- test:
    name: 签署-海外签-无需意愿
    variables:
      - flowId: ${flow_id}
      - accountId: ${psn_2}
      - updateSignfields: [ { "actorIndentityType": 4,"authorizedAccountId": "${org_1}","extendFieldDatas": { },"fileId": "${file_1}","order": 1,"posBean": { "posPage": "1","posX": 218,"posY": 232,"sealType": 1,"signType": 1,"width": 500 },"sealId": "${psn_2_seal}","signDateBeanType": 1,"signerAccountId": "${psn_2}","signfieldId": "${signfieldId}" } ]
      - addSignfields: [ ]
      - signfieldIds: [ "${signfieldId}" ]
      - approvalPolicy: 1
      - dingUser: { }
    api: api/iterate_cases/addUpdateExecute_sign_v3.yml
    validate:
      - eq: [ status_code, 200 ]
      - eq: [ "content.code",0 ]
      - eq: [ "content.message",成功 ]

