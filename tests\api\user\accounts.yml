# 用户中心请参考 http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=********

- api:
    def: create_accounts($idNo,$idType,$name,$email,$mobile)         #创建账号
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/createOverall
      method: POST
      headers: ${get_headers($app_id)}
      json:
        credentials: {"idno": $idNo}
        properties: {"name": $name, "realnameMobile": $mobile}
#        email: $email
#        mobile: $mobile

- api:
    def: create_accounts_v2($email,$mobile,$email2,$mobile2,$thirdPartyUserId)         #创建账号
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/createOverall
      method: POST
      headers: ${get_headers($app_id)}
      json:
        contacts: {"mobile": $mobile,"email":$email}
        idcards: {"mobile": $mobile2,"email":$email2,
                  "thirdparty": {
                    "thirdpartyKey": "ALI_PAY",
                    "thirdpartyUserId": $thirdPartyUserId,
                    "thirdpartyUserType": ""
                  }}



- api:
    def: update_accounts($accountId, $name, $email,$mobile)  #更新账号
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/$accountId/updateOverall
      method: POST
      headers: ${get_headers($app_id)}
      json:
        contacts: {"email": $email, "mobile": $mobile}
        properties: {"name": $name, "realnameMobile": $mobile}

- api:
    def: get_accounts($accountId)               #查询账号详情
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/$accountId/getAllInfo
      method: GET
      headers: ${get_headers($app_id)}

- api:
    def: get_accounts_from_thirdId($thirdId,$idNo)               #通过三方id获取账户id
    request:
      url: /v1/accounts?thirdId=$thirdId&idNo=$idNo
      method: GET
      headers: ${get_headers($app_id)}

- api:
    def: delete_accounts($accountId)            #注销账号
    request:
      url: ${ENV(footstone_user_url)}/v1/accounts/$accountId/deleteOverall
      method: DELETE
      headers: ${get_headers($app_id)}


#校验手机号/邮箱是否已经注册
- api:
    def: get_checkMobileEmail($principal)
    request:
      url: ${ENV(account_webserver_url)}/account-webserver/v1/accounts/checkMobileEmail
      method: POST
      headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(BZQ-App-Id)}
      json:
        {
          "principal": $principal
        }
    validate:
      - eq: [status_code, 200]

#登录申请
- api:
    def: login_apply($path,$mobileNew)
    request:
      url: ${ENV(account_webserver_url)}/account-webserver/login/apply/$path
      method: POST
      json:
        {
          "checkPrincial": true,
          "principal": $mobileNew
        }
    validate:
      - eq: [status_code, 200]

#登录验证
- api:
    def: commit($path,$credentials,$mobileNew,$loginParams)
    request:
      url: ${ENV(account_webserver_url)}/account-webserver/login/commit/$path
      method: POST
      json:
        {
          "credentials": $credentials,
          "principal": $mobileNew,
          "loginParams": $loginParams
        }
    validate:
      - eq: [status_code, 200]
