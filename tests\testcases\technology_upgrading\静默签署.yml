- config:
    name: "静默签署-前程无忧二期 - createFlowOneStep接口发起允许不传入经办人（只针对企业主体签署区）"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - app_id: ${ENV(mobile_shield_wukong_appid)}
      - thirdPartyUserId_person_1: autotest1${get_randomNo()}
      - thirdPartyUserId_person_ckl: autotest1${get_randomNo()}
      - app_id1: ${ENV(mobile_shield_wukong_appid)}
      - path_pdf1: data/个人借贷合同.pdf
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: 'https://www.baidu.com/', signPlatform: '1,2,3'}
      - standardoid: ${ENV(standardoid)}
      - standardgid: ${ENV(standardgid)}
      - appid_oid: ${ENV(mobile_shield_wukong_appid_oid)}
      - another_orgid_paltformAutoSign: ${ENV(another_orgid_paltformAutoSign)}
      - dongying_transfer_receiver: ${ENV(dongying_transfer_receiver)}
      - sixian_transfer_receiver: ${ENV(sixian_transfer_receiver)}

- test:
    name: "创建梁贤红账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId




- test:
    name: "创建机构 - esigntest测试书雪企业账号"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying2201211346wer
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId


- test:
    name: "创建机构 - esigntest沙箱2 - 该企业实名组织已注销"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201911141222231
      - creator: $caogu_accountId
      - idNumber: 911111111121212121
      - idType: CRED_ORG_USCC
      - name: esigntest沙箱2
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - shaxiang2_organize_id: content.data.orgId

- test:
    name: "创建机构 -  esigntest江门市诚峰镖局物流有限公司（印章平台测试企业）-该企业实名组织不存在管理员"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201912221141231
      - creator: $caogu_accountId
      - idNumber: 91440705MA52G73E89
      - idType: CRED_ORG_USCC
      - name: esigntest江门市诚峰镖局物流有限公司（印章平台测试企业）
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - jiangmen_organize_id: content.data.orgId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司1"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201211
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying1_organize_id: content.data.orgId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司2"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201212
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying2_organize_id: content.data.orgId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司3"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201213
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying3_organize_id: content.data.orgId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司4"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201214
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying4_organize_id: content.data.orgId

- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司5"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201215
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying5_organize_id: content.data.orgId


- test:
    name: "创建机构 - 东营伟信建筑安装工程有限公司6"
    variables:
      - app_id: $app_id1
      - thirdPartyUserId: dongying201216
      - creator: $caogu_accountId
      - idNumber: 91370502060407048H
      - idType: CRED_ORG_USCC
      - name: esigntest东营伟信建筑安装工程有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - dongying6_organize_id: content.data.orgId

- test:
    name: "创建签署人账号 - 陈凯丽"
    variables:
      - thirdPartyUserId: $thirdPartyUserId_person_ckl
      - name: 陈凯丽
      - idNumber: 362323199201061068
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - accountId_ckl: content.data.accountId




- test:
    name: "静默授权 - 陈凯丽"
    variables:
      - accountId: $accountId_ckl
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]

- test:
    name: "静默授权 - esigntest东营伟信建筑安装工程有限公司"
    variables:
      - accountId: $dongying1_organize_id
    api: api/mobile-shield/signAuth.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
      - eq: ["content.data", True]



- test:
    name: "文件上传 - 个人借贷合同.pdf"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - fileName: 个人借贷合同.pdf
      - fileSize: ${get_file_size($path_pdf1)}
    api: api/iterate_cases/getUploadUrl.yml
    extract:
      - uploadUrl: content.data.uploadUrl
      - fileId1: content.data.fileId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "上传文件到oss"
    variables:
      - contentMd5: ${get_file_base64_md5($path_pdf1)}
      - contentType: application/pdf
      - binary: ${open_file($path_pdf1)}
    request:
      url: $uploadUrl
      method: PUT
      headers:
        Content-Type: $contentType
        Content-Md5: $contentMd5
      data: $binary
    validate:
      - eq: [status_code, 200]
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]

#异常case
- test:
    name: createFlowOneStep - "签署主体和签署区主体类型不一致"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "签署主体和签署区主体类型不一致"]


- test:
    name: createFlowOneStep - "用户中心查询账户失败"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "tiangu6688",authorizedAccountId: $jiangmen_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      #        - contains: ["content.message", "用户中心查询账户失败"]
      - contains: ["content.message", "当前签署流程指定账号不存在/已注销（账号：tiangu6688），请检查后重试"]

- test:
    name: createFlowOneStep - "签署主体不存在实名组织，签署人不传，发起失败"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: "",authorizedAccountId: $jiangmen_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
#      - eq: ["content.message", "参数错误: 签署人账号不能为空"] 会成功

- test:
    name: createFlowOneStep - 签署人不能为机构类型"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $jiangmen_organize_id,authorizedAccountId: $jiangmen_organize_id}, signfields: [ { certId: "", autoExecute: false, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "参数错误: 签署人不能为机构类型"]

- test:
    name: createFlowOneStep - "用户未授权或授权已过期，不允许静默签署"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $jiangmen_organize_id,authorizedAccountId: $jiangmen_organize_id}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message", "用户未授权或授权已过期，不允许静默签署"]


- test:
    name: createFlowOneStep - "发起成功"
    variables:
      - doc: {encryption: 0,fileId: $fileId1, fileName: "个人借贷合同.pdf", source: 0}
      - attachments: []
      - docs: [$doc]
      - copiers: []
      - flowInfo: { autoArchive: true, autoInitiate: true, businessScene: " createFlowOneStep接口优化",flowConfigInfo: {noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}",noticeType: "1,2,3", redirectUrl: "", signPlatform: "1,2","mobileShieldWay":1 }, hashSign: false }
      - signers: [{platformSign: false, signOrder: 1,signerAccount: { signerAccountId: $accountId_ckl,authorizedAccountId: $jiangmen_organize_id}, signfields: [ { certId: "", autoExecute: true, actorIndentityType: 2, fileId: $fileId1,   posBean: { posPage: 1,  posX: 110,   posY: 110  }, signType: 1,   width: 150 }],  thirdOrderNo: 777 }]
    api: api/mobile-shield/createFlowOneStep.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.message", "成功"]
