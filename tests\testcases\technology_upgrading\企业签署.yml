- config:
    name: "企业签署"
    base_url: ${ENV(footstone_api_url)}
    variables:
      - path_pdf: data/个人借贷合同.pdf
      - path_pdf1: data/劳动合同书.pdf
      - path_pdf2: data/劳动合同书12.pdf
      - group: ''
      - app_id: ${ENV(wukong_no_realname)}
      - fileId1: ${get_file_id($app_id,$path_pdf1)}

- test:
    name: "wukong_no_realname下创建签署人王瑶济账号"
    variables:
      - thirdPartyUserId: yuzan201915222421234
      - name: 王瑶济
      - idNumber: 320721199305232634
      - idType: CRED_PSN_CH_IDCARD
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - yuzan_accountId: content.data.accountId

- test:
    name: "创建梁贤红账号"
    variables:
      - thirdPartyUserId: caogu20191111411321213
      - name: 梁贤红
      - idNumber: 331082199211223080
      - idType: "CRED_PSN_CH_IDCARD"
      - email:
      - mobile: ***********
    api: api/iterate_cases/p_createByThirdPartyUserId.yml
    extract:
      - caogu_accountId: content.data.accountId

- test:
    name: "创建机构 泗县深泉纯净水有限公司"
    variables:
      - thirdPartyUserId: si201913331141132
      - creator: $caogu_accountId
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

- test:
    name: "创建机构  泗县梁贤红乐福超市"
    variables:
      - thirdPartyUserId: sin111113393311141132
      - creator: $caogu_accountId
      - idNumber: 92341324MA2NX3TR55
      - idType: CRED_ORG_USCC
      - name: 泗县梁贤红乐福超市
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixianchaoshi_organize_id: content.data.orgId


- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixian_organize_id
      - alias: 印章1
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixian_organize_id
      - alias: 印章2
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixian_organize_id
      - alias: 印章11
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixianchaoshi_organize_id
      - alias: 印章3
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixianchaoshi_organize_id
      - alias: 印章4
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "创建企业印章"
    variables:
      - app_id: ${ENV(wukong_realname)}
      - orgId: $sixianchaoshi_organize_id
      - alias: 印章344
      - central: STAR
      - color: BLUE
      - height: 159
      - width: 159
      - htext: sixian
      - qtext: sixian
      - type: TEMPLATE_ROUND
    api: api/iterate_cases/seals_officialtemplate.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业1的印章"
    teardown_hooks:
      - ${teardown_seals_chaoshi($response)}
    variables:
      - orgId: $sixian_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - list1: list11
      - list2: list22
      - list3: list33
      - list4: list44
      - list5: list55
      - sealsId1: sealsId1
      - sealsId1_list: sealsId1_list
      - org_sealIds: org_sealIds
      - sealsId4: sealsId4
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]

- test:
    name: "查询企业2的印章"
    teardown_hooks:
      - ${teardown_seals_chaoshi($response)}
    variables:
      - orgId: $sixianchaoshi_organize_id
    api: api/seals/seals/select_c_seals.yml
    extract:
      - list11: list11

    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]
      - eq: ["content.message", "成功"]
###################-个企业签署区###################################


- test:
    name: "创建签署流程-一个签署区"
    variables:
      - autoArchive: true
      - businessScene: 悟空非实名签校验sealIds逻辑
      - configInfo: { noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}", noticeType: '1,2,3,4', redirectUrl: '', signPlatform: '1,2,3'}
      - contractValidity: ""
      - extend: {}
      - initiatorAccountId: $caogu_accountId
      - payerAccountId: ""
      - signValidity: ${get_timestamp(30)}
      - initiatorAuthorizedAccountId: ""
    api: api/iterate_cases/signflowsCreate.yml
    extract:
      - sign_flowId1: content.data.flowId
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]
      - eq: ["status_code", 200]

- test:
    name: "添加1份流程文档"
    variables:
      - docs: [{'fileHash': '', 'fileId': $fileId1, 'fileName': '文件1', 'filePassword': '', 'encryption': 0}]
      - flowId: $sign_flowId1
    api: api/iterate_cases/addFlowDoc.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "添加一个企业签署区-有sealId和sealIds"
    variables:
      - flowId: $sign_flowId1
      - signfields: [{"fileId":$fileId1,"signerAccountId":$yuzan_accountId,"actorIndentityType":"2","authorizedAccountId":$sixian_organize_id,"assignedPosbean":false,"order":1,"posBean":{"addSignTime":true,"posPage":"1","posX":100,"posY":200},"sealType":null,"signType":1}]
    api: api/iterate_cases/signfieldsCreate_handSign.yml
    extract:
      - signfieldId0: content.data.signfieldBeans.0.signfieldId
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: "开启签署流程"
    variables:
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsStart.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]


- test:
    name: "获取签署链接"
    variables:
      - flowId: $sign_flowId1
      - accountId: $yuzan_accountId
      - urlType: 0
      - multiSubject: true
      - organizeId: $sixian_organize_id
      - compressContext: true
    api: api/iterate_cases/executeUrl.yml
    extract:
      - shortUrl: content.data.shortUrl
      - code: content.code
      - message: content.message
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code",0]

- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId1
      - accountId: $yuzan_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - addSignfield: ${gen_signfield_data_V2(0,0,$fileId1,$sign_flowId1,$posBean,1,$accountId,$sixian_organize_id,$sealsId1,1)}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","当前用户不支持自由签署， 不能追加新的签署区"]


- test:
    name: 添加或更新签署区并执行签署-报错 企业主体缺少签署主体信息
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId1
      - accountId: $caogu_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - addSignfield: ${gen_signfield_data_V5(2,0,$fileId1,sign_flowId1,$posBean,1,$yuzan_accountId,None,$sealsId1,1)}
      - addSignfields: [$addSignfield]
      - updateSignfields: []
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","缺少签署主体信息"]



- test:
    name: 添加或更新签署区并执行签署
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId1
      - accountId: $yuzan_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - addSignfield: ${gen_signfield_update_data($sixian_organize_id, , $posBean, , $sealsId1, $signfieldId0)}
      - addSignfields: []
      - updateSignfields: [$addSignfield]
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","成功"]

- test:
    name: 添加或更新签署区并执行签署第二次
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId1
      - accountId: $yuzan_accountId
      - posBean: {"addSignTime":true,"posPage":"1","posX":100,"posY":200}
      - addSignfield: ${gen_signfield_update_data($sixian_organize_id, , $posBean, , $sealsId1, $signfieldId0)}
      - addSignfields: []
      - updateSignfields: [$addSignfield]
      - signfieldIds: [$signfieldId0]
      - dingUser: {}
      - approvalPolicy: 0
    api: api/signflow/aggregate_sign/addUpdateExecute_sign.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["status_code", 200]
      - contains: ["content.message","当前用户无待签署任务"]
    teardown_hooks:
      - ${hook_sleep_n_secs(3)}

- test:
    name: 归档流程
    variables:
      - app_id: ${ENV(wukong_no_realname)}
      - flowId: $sign_flowId1
    api: api/signflow/signflows/signflowsArchive.yml
    validate:
      - eq: [status_code, 200]
      - contains: ["content.message","流程已归档"]