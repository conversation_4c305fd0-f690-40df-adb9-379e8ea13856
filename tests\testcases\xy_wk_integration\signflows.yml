- config:
    name: "悟空轩辕整合"
    base_url: ${ENV(base_url)}
    variables:
      - app_id: ${ENV(app_id_tengqing_SaaS_with_willing)}
      - xuanyuan_sixian: ${ENV(mobile_shield_wukong_appid_oid)}
      - xuanyuan_sixian_name: ${ENV(xuanyuan_sixian_name)}
      - accountId_withoutRealName_in_tsign: ${ENV(accountId_withoutRealName_in_tsign)}
      - fileId: ${ENV(fileId)}
      - oid_wang_tsign: ${ENV(oid_wang_tsign)}
      - phone_wang: ${ENV(phone_wang)}
      - mail_wang: ${ENV(mail_wang)}
      - none_tsign_account_id: ${ENV(none_tsign_account_id)}
      - accountId_withRealName_withoutAuth: ${ENV(accountId_withRealName_withoutAuth)}

- test:
    name: 签署人传个人oid，签署主体传同一个oid-个人签
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $oid_wang_tsign
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 无发起人
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435002]
      - contains: ["content.message",发起人不允许为空]

- test:
    name: 无发起主体
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 无发起人及发起主体
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - len_gt: ["content.data.flowId",0]

- test:
    name: 发起人未实名
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $accountId_withoutRealName_in_tsign
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437610]
      - contains: ["content.message",账号未实名授权]

- test:
    name: 发起人已实名未授权
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: $none_tsign_account_id
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - contains: ["content.message",账号不存在或已注销]

- test:
    name: 发起人oid在标准签appid不存在
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAccountId: ${ENV(account_id_2)}
      - initiatorAuthorizedAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - contains: ["content.message",账号不存在或已注销]

- test:
    name: 发起主体未实名
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAuthorizedAccountId: $accountId_withoutRealName_in_tsign
      - initiatorAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1437610]
      - contains: ["content.message",账号未实名授权]

- test:
    name: 发起主体已实名未授权
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAuthorizedAccountId: $none_tsign_account_id
      - initiatorAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - contains: ["content.message",账号不存在或已注销]

- test:
    name: 发起主体oid在标准签appid不存在
    variables:
      - autoArchive: true
      - businessScene: 签签署署${get_randomNo()}
      - configInfo: {noticeType: "",noticeDeveloperUrl: "${ENV(noticeDeveloperUrl)}","signPlatform": "1,2,3,4,5","archiveLock": false,"redirectUrl": "","redirectDelayTime":"0","batchDropSeal":false}
      - initiatorAuthorizedAccountId: ${ENV(account_id_2)}
      - initiatorAccountId: $oid_wang_tsign
      - json: {autoArchive: $autoArchive, businessScene: $businessScene, configInfo: $configInfo, initiatorAccountId: $initiatorAccountId, initiatorAuthorizedAccountId: $initiatorAuthorizedAccountId}
    api: api/signflow/v3/signflows.yml
    validate:
      - eq: [status_code, 200]
      - eq: ["content.code", 1435011]
      - contains: ["content.message",账号不存在或已注销]